name: Composer test

on:
  push:
    branches:
      - '*'
  pull_request:
    branches:
      - '*'
  schedule:
    - cron: '0 0 * * *' # Run every day at 00:00 UTC

jobs:
  tests:
    name: Composer P${{ matrix.php }} - L${{ matrix.laravel }} - ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ ubuntu-latest ]
        php: [ '8.3', '8.4' ]
        laravel: [ 11.*, 10.*, 9.* ]
        include:
          - laravel: 11.*
            testbench: 9.*
          - laravel: 10.*
            testbench: 8.*
          - laravel: 9.*
            testbench: 8.*
        exclude:
          - laravel: 11.*
            php: 8.1
    steps:
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          cd ElasticScout
          composer install --no-interaction --no-progress

      - name: Run tests
        run: |
          cd ElasticScout
          composer validate --strict

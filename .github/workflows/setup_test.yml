name: Setup & test

on:
  push:
    branches:
      - '*'
  pull_request:
    branches:
      - '*'
  schedule:
    - cron: '0 0 * * *' # Run every day at 00:00 UTC

jobs:
  tests:
    name: Composer P${{ matrix.php }} - ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ ubuntu-latest ]
        php: [ '8.3', '8.4' ]
    steps:
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          git clone https://${{ secrets.GH_USERNAME }}:${{ secrets.GH_TOKEN }}@github.com/cslant/blog-admin.git
          cd blog-admin
          git clone https://${{ secrets.GH_USERNAME }}:${{ secrets.GH_TOKEN }}@github.com/cslant/blog-private-modules.git
          mkdir -p database
          touch database/database.sqlite
          composer install
          composer require cslant/laravel-telegram-git-notifier
          composer create-project cslant/telegram-git-notifier-app

      - name: Run tests
        run: |
          composer validate --strict 2>&1 | grep -v "unbound version constraints"

name: Setup & test

on:
  push:
    branches:
      - '*'
  pull_request:
    branches:
      - '*'
  schedule:
    - cron: '0 0 * * *'

jobs:
  tests:
    name: Composer P${{ matrix.php }} - L${{ matrix.laravel }} - ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ ubuntu-latest ]
        php: [ '8.2', '8.3', '8.4' ]
        laravel: [ 11.*, 10.*, 9.* ]
        include:
          - laravel: 11.*
            testbench: 9.*
          - laravel: 10.*
            testbench: 8.*
          - laravel: 9.*
            testbench: 8.*
        exclude:
          - laravel: 11.*
            php: 8.1
    steps:
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}

      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        run: |
          composer install --no-interaction
          composer require cslant/laravel-telegram-git-notifier
          composer create-project cslant/telegram-git-notifier-app

      - name: Run tests
        run: |
          composer validate --strict

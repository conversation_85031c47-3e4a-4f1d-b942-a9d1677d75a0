<?php

namespace CSlant\Blog\BladeCustom\Providers;

use Botble\Base\Supports\ServiceProvider;

class BladeCustomServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $this->bladeOverride();
    }

    /**
     * Override blade views of the package.
     */
    public function bladeOverride(): void
    {
        view()->composer('core/base::layouts.horizontal.partials.navbar', function ($view) {
            $view->setPath(__DIR__.'/../../resources/views/core/base/layouts/horizontal/partials/navbar.blade.php');
        });

        view()->composer('core/base::layouts.vertical.partials.navbar', function ($view) {
            $view->setPath(__DIR__.'/../../resources/views/core/base/layouts/vertical/partials/navbar.blade.php');
        });

        view()->composer('core/base::layouts.vertical.partials.header', function ($view) {
            $view->setPath(__DIR__.'/../../resources/views/core/base/layouts/vertical/partials/header.blade.php');
        });

        view()->composer('core/base::layouts.master', function ($view) {
            $view->setPath(__DIR__.'/../../resources/views/core/base/layouts/master.blade.php');
        });

        view()->composer('packages/seo-helper::meta-box', function ($view) {
            $view->setPath(__DIR__.'/../../resources/views/packages/seo-helper/meta-box.blade.php');
        });

        view()->composer('packages/slug::permalink', function ($view) {
            $view->setPath(__DIR__.'/../../resources/views/packages/slug/permalink.blade.php');
        });

        view()->composer('core/setting::general', function ($view) {
            $view->setPath(__DIR__.'/../../resources/views/core/setting/general.blade.php');
        });
    }
}

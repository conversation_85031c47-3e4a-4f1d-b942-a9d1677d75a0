<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::table('widgets')
            ->where('sidebar_id', 'home_page')
            ->delete();
        DB::table('widgets')->insert([
                [
                'widget_id' => 'HomePagesWidget',
                'sidebar_id' => 'home_page',
                'theme' => 'ripple',
                'position' => 0,
                'data' => json_encode([
                    "id" => "HomePagesWidget",
                    "name" => "Banner",
                    "section_id" => "0",
                    "status" => "active",
                ])
                ],

                [
                    'widget_id' => 'HomePagesWidget',
                    'sidebar_id' => 'home_page',
                    'theme' => 'ripple',
                    'position' => 1,
                    'data' => json_encode([
                        "id" => "HomePagesWidget",
                        "name" => "Danh sách tác giả mới nhất",
                        "section_id" => "1",
                        "status" => "active",
                    ]),
                ],
                [
                    'widget_id' => 'HomePagesWidget',
                    'sidebar_id' => 'home_page',
                    'theme' => 'ripple',
                    'position' => 2,
                    'data' => json_encode([
                        "id" => "HomePagesWidget",
                        "name" => "Danh mục nổi bật",
                        "section_id" => "2",
                        "status" => "active",
                    ]),
                ],
                [
                    'widget_id' => 'HomePagesWidget',
                    'sidebar_id' => 'home_page',
                    'theme' => 'ripple',
                    'position' => 3,
                    'data' => json_encode([
                        "id" => "HomePagesWidget",
                        "name" => "Bài viết nổi bật nhất trong từng category",
                        "section_id" => "3",
                        "status" => "active",
                    ]),
                ],
                [
                    'widget_id' => 'HomePagesWidget',
                    'sidebar_id' => 'home_page',
                    'theme' => 'ripple',
                    'position' => 4,
                    'data' => json_encode([
                        "id" => "HomePagesWidget",
                        "name" => "Ads",
                        "section_id" => "4",
                        "status" => "active",
                    ]),
                ],
                [
                    'widget_id' => 'HomePagesWidget',
                    'sidebar_id' => 'home_page',
                    'theme' => 'ripple',
                    'position' => 5,
                    'data' => json_encode([
                        "id" => "HomePagesWidget",
                        "name" => "Khám phá các bài viết mới nhất",
                        "section_id" => "5",
                        "status" => "active",
                    ]),
                ],
                [
                    'widget_id' => 'HomePagesWidget',
                    'sidebar_id' => 'home_page',
                    'theme' => 'ripple',
                    'position' => 6,
                    'data' => json_encode([
                        "id" => "HomePagesWidget",
                        "name" => "Trở thành tác giả",
                        "section_id" => "6",
                        "status" => "active",
                    ]),
                ],
                [
                    'widget_id' => 'HomePagesWidget',
                    'sidebar_id' => 'home_page',
                    'theme' => 'ripple',
                    'position' => 7,
                    'data' => json_encode([
                        "id" => "HomePagesWidget",
                        "name" => "Danh sách các danh mục nổi bật",
                        "section_id" => "7",
                        "status" => "active",
                    ]),
                ],
                [
                    'widget_id' => 'HomePagesWidget',
                    'sidebar_id' => 'home_page',
                    'theme' => 'ripple',
                    'position' => 8,
                    'data' => json_encode([
                        "id" => "HomePagesWidget",
                        "name" => "Các bài viết nhiều tương tác nhất",
                        "section_id" => "8",
                        "status" => "active",
                    ]),
                ],
                [
                    'widget_id' => 'HomePagesWidget',
                    'sidebar_id' => 'home_page',
                    'theme' => 'ripple',
                    'position' => 9,
                    'data' => json_encode([
                        "id" => "HomePagesWidget",
                        "name" => "Bài viết mới nhất",
                        "section_id" => "9",
                        "status" => "active",
                    ]),
                ],
            ],
        );
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::table('widgets')
            ->where('sidebar_id', 'home_page')
            ->delete();
    }
};

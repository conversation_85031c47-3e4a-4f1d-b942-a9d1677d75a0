<?php

namespace CSlant\Blog\ConfigCustom\Providers;

use Botble\Base\Supports\ServiceProvider;

class ConfigCustomServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $routePath = __DIR__.'/../../routes/config-api.php';
        if (file_exists($routePath)) {
            $this->loadRoutesFrom($routePath);
        }

        $this->loadMigrationsFrom(__DIR__.'/../../database/migrations');
    }
}

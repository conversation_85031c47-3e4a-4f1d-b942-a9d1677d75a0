<?php

namespace CSlant\Blog\Core\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\HtmlString;

class CommentNotification extends Notification
{
    use Queueable;

    /**
     * @var array
     */
    protected $data;

    /**
     * Create a new notification instance.
     *
     * @param array $data
     */
    public function __construct(array $data)
    {
        $this->data = $data;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param mixed $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail($notifiable)
    {
        $isReply = isset($this->data['reply_to']);
        $isReplyToYourComment = $isReply && isset($this->data['is_reply_to_your_comment']) && $this->data['is_reply_to_your_comment'];

        if ($isReplyToYourComment) {
            $subject = __('New reply to your comment on :name', ['name' => $this->data['reference_name']]);
            $greeting = __('Hello!');
            $message = __('Someone has replied to your comment on :name', ['name' => $this->data['reference_name']]);
        } elseif ($isReply) {
            $subject = __('New reply to a comment on :name', ['name' => $this->data['reference_name']]);
            $greeting = __('Hello!');
            $message = __('There is a new reply to a comment on :name', ['name' => $this->data['reference_name']]);
        } else {
            $subject = __('New comment on :name', ['name' => $this->data['reference_name']]);
            $greeting = __('Hello!');
            $message = __('There is a new comment on :name', ['name' => $this->data['reference_name']]);
        }

        return (new MailMessage)
            ->subject($subject)
            ->greeting($greeting)
            ->line($message)
            ->line(new HtmlString('<strong>' . __('Comment content:') . '</strong> ' . $this->data['comment_content']))
            ->action(__('View'), $this->data['comment_url'])
            ->line(__('Thank you for using our application!'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @param mixed $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        $isReply = isset($this->data['reply_to']);
        $isReplyToYourComment = $isReply && isset($this->data['is_reply_to_your_comment']) && $this->data['is_reply_to_your_comment'];

        if ($isReplyToYourComment) {
            $title = __('New reply to your comment');
            $description = __('Someone has replied to your comment on :name', ['name' => $this->data['reference_name']]);
        } elseif ($isReply) {
            $title = __('New reply to a comment');
            $description = __('There is a new reply to a comment on :name', ['name' => $this->data['reference_name']]);
        } else {
            $title = __('New comment');
            $description = __('There is a new comment on :name', ['name' => $this->data['reference_name']]);
        }

        return [
            'title' => $title,
            'description' => $description . ': ' . $this->data['comment_content'],
            'action_url' => $this->data['comment_url'],
            'action_label' => __('View'),
        ];
    }
}

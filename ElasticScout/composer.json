{"name": "cslant/bpm-elasticscout", "description": "Elasticsearch driver for Laravel Scout", "license": "MIT", "type": "package", "autoload": {"psr-4": {"CSlant\\Blog\\ElasticScout\\": "src/"}, "files": ["common/helpers.php"]}, "require": {}, "require-dev": {"cslant/blog-core": "dev-main"}, "extra": {"laravel": {"providers": ["CSlant\\Blog\\ElasticScout\\Providers\\ElasticScoutServiceProvider"]}}, "minimum-stability": "dev", "prefer-stable": true, "config": {"allow-plugins": {"php-http/discovery": true}}}
<?php

namespace CSlant\Blog\ElasticScout\Configs;

use CSlant\Blog\ElasticScout\Configs\Mapping\UserMapping;

class ElasticsearchIndices
{
    /**
     * @return array[]
     */
    public static function getIndices(): array
    {
        return [
            'mappings' => array_merge(
                ['default' => self::getMappings()],
                UserMapping::getMappings(),
            ),
            'settings' => [
                'default' => self::getSettings(),
            ],
        ];
    }

    /**
     * @return array[]
     */
    public static function getSettings(): array
    {
        return [
            'settings' => [
                'number_of_shards' => 1,
                'number_of_replicas' => 0,
                'analysis' => [
                    'filter' => [
                        'email' => [
                            'type' => 'pattern_capture',
                            'preserve_original' => true,
                            'patterns' => [
                                '([^@]+)',
                                '(\\p{L}+)',
                                '(\\d+)',
                                '@(.+)',
                            ],
                        ],
                    ],
                    'analyzer' => [
                        'my_email_analyzer' => [
                            'filters' => [
                                'email',
                                'lowercase',
                                'unique',
                            ],
                            'tokenizer' => 'my_tokenizer',
                        ],
                        'my_email_analyzer_lowercase' => [
                            'filters' => [
                                'email',
                                'unique',
                            ],
                            'filter' => [
                                'lowercase',
                            ],
                            'type' => 'custom',
                            'tokenizer' => 'my_tokenizer',
                        ],
                    ],
                    'tokenizer' => [
                        'my_tokenizer' => [
                            'type' => 'uax_url_email',
                        ],
                    ],
                ],
            ],
        ];
    }

    /**
     * @return array[]
     */
    public static function getMappings(): array
    {
        return [
            'properties' => [
                'id' => [
                    'type' => 'keyword',
                ],
            ],
        ];
    }
}

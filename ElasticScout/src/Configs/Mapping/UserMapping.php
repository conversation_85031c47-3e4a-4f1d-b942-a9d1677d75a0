<?php

namespace CSlant\Blog\ElasticScout\Configs\Mapping;

use CSlant\Blog\ElasticScout\Constants\ElasticsearchConstant;

class UserMapping
{
    /**
     * @return array[]
     */
    public static function getMappings(): array
    {
        return [
            ElasticsearchConstant::USER_SEARCHABLE_AS => self::userV1(),
        ];
    }

    /**
     * @return array[]
     */
    public static function userV1(): array
    {
        return [
            'properties' => [
                'id' => [
                    'type' => 'keyword',
                ],
                'email' => [
                    'type' => 'text',
                    'analyzer' => 'my_email_analyzer',
                ],
                'email_lowercase' => [
                    'type' => 'text',
                    'analyzer' => 'my_email_analyzer_lowercase',
                ],
                'created_at' => [
                    'type' => 'date',
                ],
                'updated_at' => [
                    'type' => 'date',
                ],
                'first_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                ],
                'last_name' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                ],
                'username' => [
                    'type' => 'text',
                    'analyzer' => 'standard',
                ],
                'permissions' => [
                    'type' => 'keyword',
                ],
            ],
        ];
    }
}

<?php

use CSlant\Blog\InteractionCustom\Http\Actions\Comment\CommentGetFiltersAction;
use CSlant\Blog\InteractionCustom\Http\Actions\Comment\CommentStoreAction;
use CSlant\Blog\InteractionCustom\Http\Actions\Comment\CommentUpdateAction;
use CSlant\Blog\InteractionCustom\Http\Actions\Comment\CommentDeleteAction;
use CSlant\Blog\InteractionCustom\Http\Actions\Like\LikePostToggleAction;
use CSlant\Blog\InteractionCustom\Http\Actions\Like\LikePostGetCountAction;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Blog Interaction Routes
|--------------------------------------------------------------------------
|
| Here is where you can register bot routes for your application. These
| routes are loaded by the RouteServiceProvider, and all of them will
| be assigned to the "api" middleware group. Enjoy building your API!
|
*/

$routePrefix = config('blog-api.defaults.route_prefix');

Route::prefix($routePrefix)->name("$routePrefix.")->middleware(['api'])->group(function () {
    Route::group(['prefix' => 'interaction'], function () {
        Route::group(['prefix' => 'comments'], function () {
            Route::group(['middleware' => ['auth:sanctum', 'check-verify-email', 'check-member-status']], function () {
                Route::post('/', CommentStoreAction::class)->name('interaction.store.comment');
                Route::post('{commentId}/toggle-like',
                    \CSlant\Blog\InteractionCustom\Http\Actions\Like\LikeCommentToggleAction::class)
                    ->name('interaction.toggle-like.comment')
                    ->middleware(['api-action-rate-limiter:like-toggle']);
                Route::put('{comment}', CommentUpdateAction::class)->name('interaction.update.comment');
                Route::delete('{comment}', CommentDeleteAction::class)->name('interaction.delete.comment');

                Route::post('/{comment}/reply', \CSlant\Blog\InteractionCustom\Http\Actions\Comment\ReplyStoreAction::class)->name('interaction.reply.store');
            });

            Route::get('filters', CommentGetFiltersAction::class)
                ->name('posts.filters.comment')
                ->middleware(['api-action-rate-limiter:comment-filters']);
            Route::get('{commentId}/count-like',
                \CSlant\Blog\InteractionCustom\Http\Actions\Like\LikeCommentGetCountAction::class)
                ->name('comments.like-count');
        });

        Route::group(['prefix' => 'posts'], function () {
            Route::group(['middleware' => ['auth:sanctum', 'check-verify-email', 'check-member-status']], function () {
                Route::post('{postId}/toggle-like', LikePostToggleAction::class)
                    ->name('posts.toggle-like')
                    ->middleware(['api-action-rate-limiter:like-toggle']);
            });

            Route::get('{postId}/count-like', LikePostGetCountAction::class)
                ->name('posts.like-count');
        });
    });
});


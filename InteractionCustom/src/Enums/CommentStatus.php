<?php

namespace CSlant\Blog\InteractionCustom\Enums;

use FriendsOfBotble\Comment\Enums\CommentStatus as BaseCommentStatus;

/**
 * @method static self PENDING()
 * @method static self APPROVED()
 * @method static self SPAM()
 * @method static self TRASH()
 */
class CommentStatus extends BaseCommentStatus
{
    public const STATUSES = [
        self::PENDING,
        self::APPROVED,
        self::SPAM,
        self::TRASH,
    ];
}

<?php

namespace CSlant\Blog\InteractionCustom\Hooks;

use Bo<PERSON>ble\Dashboard\Supports\DashboardWidgetInstance;
use CSlant\LaravelLike\Enums\InteractionTypeEnum;
use CSlant\LaravelLike\Models\Like;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class LikeWidgetHook
{
    public static function addLikeStatsWidget(array $widgets, Collection $widgetSettings): array
    {
        if (!Auth::guard()->user()->hasPermission('posts.index')) {
            return $widgets;
        }

        $likes = fn () => Like::query()->where('type', InteractionTypeEnum::LIKE)->count();

        return (new DashboardWidgetInstance)
            ->setType('stats')
            ->setPermission('posts.index')
            ->setTitle(trans('Likes'))
            ->setKey('widget_total_likes')
            ->setIcon('ti ti-heart')
            ->setColor('secondary')
            ->setStatsTotal($likes)
            ->setRoute(route('posts.index'))
            ->setColumn('col-12 col-md-6 col-lg-3')
            ->init($widgets, $widgetSettings);
    }
}

<?php

namespace CSlant\Blog\InteractionCustom\Http\Actions\Comment;

use Botble\Base\Http\Actions\DeleteResourceAction;
use CSlant\Blog\Core\Http\Actions\Action;
use FriendsOfBotble\Comment\Models\Comment;

class CommentDeleteAction extends Action
{
    /**
     * CommentController:destroy
     *
     * @param  Comment  $comment
     *
     * @return DeleteResourceAction
     */
    public function __invoke(Comment $comment): DeleteResourceAction
    {
        return DeleteResourceAction::make($comment);
    }
}

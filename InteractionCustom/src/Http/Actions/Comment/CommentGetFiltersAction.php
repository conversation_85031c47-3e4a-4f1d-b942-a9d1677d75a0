<?php

namespace CSlant\Blog\InteractionCustom\Http\Actions\Comment;

use Botble\Base\Http\Responses\BaseHttpResponse;
use CSlant\Blog\Api\Http\Resources\Comment\CommentResource;
use CSlant\Blog\Core\Http\Actions\Action;
use CSlant\Blog\InteractionCustom\Http\Requests\Comment\CommentGetFiltersRequest;
use CSlant\Blog\InteractionCustom\Services\CommentService;
use CSlant\Blog\InteractionCustom\Supports\Comment\FilterComment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Resources\Json\JsonResource;

class CommentGetFiltersAction extends Action
{
    protected CommentService $commentService;

    public function __construct(CommentService $commentService)
    {
        $this->commentService = $commentService;
    }

    /**
     * @param  CommentGetFiltersRequest  $request
     *
     * @return BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse
     */
    public function __invoke(CommentGetFiltersRequest $request): BaseHttpResponse|JsonResponse|RedirectResponse|JsonResource
    {
        $filters = FilterComment::setFilters($request->validated());

        $data = $this->commentService->getFilters($filters);

        return $this
            ->httpResponse()
            ->setData(CommentResource::collection($data))
            ->toApiResponse();
    }
}

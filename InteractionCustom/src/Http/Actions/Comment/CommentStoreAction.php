<?php

namespace CSlant\Blog\InteractionCustom\Http\Actions\Comment;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Base\Models\BaseModel;
use CSlant\Blog\Api\Http\Resources\Comment\CommentResource;
use CSlant\Blog\Core\Http\Actions\Action;
use CSlant\Blog\Core\Models\Post;
use CSlant\Blog\InteractionCustom\Http\Requests\Comment\CommentRequest;
use FriendsOfBotble\Comment\Actions\GetCommentReference;

class CommentStoreAction extends Action
{
    /**
     * CommentController:store
     *
     * @param  CommentRequest  $request
     * @param  ProcessCommentAction  $processCommentAction
     * @param  GetCommentReference  $getCommentReference
     *
     * @return BaseHttpResponse
     */
    public function __invoke(CommentRequest $request, ProcessCommentAction $processCommentAction, GetCommentReference $getCommentReference): BaseHttpResponse
    {
        $data = [
            ...$request->validated(),
            'reference_type' => $request->input('reference_type') ?? Post::class,
            'reference_url' => $request->input('reference_url') ?? url()->previous(),
        ];

        $reference = new BaseModel;

        if ($data['reference_type']) {
            $reference = $getCommentReference($data['reference_type'], $data['reference_id']);
            if ($reference->getMetaData('allow_comments', true) == '0') {
                return $this
                    ->httpResponse()
                    ->setError()
                    ->setStatusCode(404)
                    ->setMessage(__('Model not found!'))
                    ->toApiResponse();
            }
        }

        $comment = $processCommentAction($reference, $data);

        return $this
            ->httpResponse()
            ->setData(CommentResource::make($comment))
            ->setMessage(trans('plugins/fob-comment::comment.front.comment_success_message'));
    }
}

<?php

namespace CSlant\Blog\InteractionCustom\Http\Actions\Comment;

use Botble\Base\Http\Responses\BaseHttpResponse;
use CSlant\Blog\Core\Http\Actions\Action;
use CSlant\Blog\InteractionCustom\Http\Requests\Comment\CommentUpdateRequest;
use FriendsOfBotble\Comment\Enums\CommentStatus;
use FriendsOfBotble\Comment\Models\Comment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Resources\Json\JsonResource;

class CommentUpdateAction extends Action
{
    /**
     * @param  int|string  $comment
     * @param  CommentUpdateRequest  $request
     *
     * @return BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse
     */
    public function __invoke(string|int $comment, CommentUpdateRequest $request): BaseHttpResponse|JsonResponse|RedirectResponse|JsonResource
    {
        $comment = Comment::query()
            ->where('status', CommentStatus::APPROVED)
            ->find($comment);
        if (!$comment) {
            return $this
                ->httpResponse()
                ->setError()
                ->setStatusCode(404)
                ->setMessage(__('Comment not found!'))
                ->toApiResponse();
        }
        $comment->update($request->validated());

        return $this
            ->httpResponse()
            ->withUpdatedSuccessMessage();
    }
}

<?php

namespace CSlant\Blog\InteractionCustom\Http\Actions\Comment;

use Botble\Base\Contracts\BaseModel;
use Botble\Base\Facades\AdminHelper;
use Botble\Base\Supports\Helper;
use CSlant\Blog\Core\Models\Comment;
use CSlant\Blog\InteractionCustom\Supports\Comment\CommentHelper;
use FriendsOfBotble\Comment\Enums\CommentStatus;
use Illuminate\Http\Request;

class ProcessCommentAction
{
    public function __construct(
        protected Request $request,
        protected SendCommentNotificationAction $sendCommentNotificationAction
    ) {
    }

    /**
     * @param  BaseModel  $reference
     * @param  array  $data
     * @param  null|Comment  $replyTo
     *
     * @return Comment
     */
    public function __invoke(BaseModel $reference, array $data, ?Comment $replyTo = null): Comment
    {
        $data = [
            ...$data,
            'ip_address' => Helper::getIpFromThirdParty(),
            'user_agent' => $this->request->userAgent(),
            'status' => $this->getStatus(),
            'reference_id' => $reference->getKey(),
            'reference_type' => $reference::class,
        ];

        if ($this->request->user() && $author = CommentHelper::getAuthorizedUser($this->request->user())) {
            $data['author_id'] ??= $author->getKey();
            $data['author_type'] ??= $author::class;
        }

        $comment = Comment::query()->create([
            ...$data,
            'reply_to' => $replyTo ? ($replyTo->reply_to ?: $replyTo->getKey()) : null,
        ]);

        // Only send notification if comment is approved
        if ($comment->status === CommentStatus::APPROVED) {
            ($this->sendCommentNotificationAction)($comment, $reference, $replyTo);
        }

        return $comment;
    }

    protected function getStatus(): string
    {
        if (AdminHelper::isInAdmin() && auth()->check()) {
            return CommentStatus::APPROVED;
        }

        return CommentHelper::commentMustBeModerated() ? CommentStatus::PENDING : CommentStatus::APPROVED;
    }
}

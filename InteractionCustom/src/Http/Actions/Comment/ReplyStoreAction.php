<?php

namespace CSlant\Blog\InteractionCustom\Http\Actions\Comment;

use Botble\Base\Http\Responses\BaseHttpResponse;
use CSlant\Blog\Core\Http\Actions\Action;
use CSlant\Blog\Core\Models\Comment;
use CSlant\Blog\InteractionCustom\Http\Requests\Comment\ReplyCommentRequest;
use FriendsOfBotble\Comment\Enums\CommentStatus;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Parameter;
use OpenApi\Attributes\Post;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Response;
use OpenApi\Attributes\Schema;

class ReplyStoreAction extends Action
{
    /**
     * ReplyCommentController:__invoke
     *
     * @param  int|string  $comment
     * @param  ReplyCommentRequest  $request
     * @param  ProcessCommentAction  $processCommentAction
     *
     * @return BaseHttpResponse
     */
    #[
        Post(
            path: "/comments/{comment}/reply",
            operationId: "ReplyToComment",
            description: "
                Submit a reply to an approved comment.  
                Requires the ID of the parent comment in the path.  
                The reply will be processed and stored if validation passes.
            ",
            summary: "Reply to an existing comment",
            tags: ['Interaction'],
            parameters: [
                new Parameter(
                    name: 'commentId',
                    description: 'comment Id',
                    in: 'path',
                    required: true,
                    schema: new Schema(type: 'integer', example: 1),
                ),
            ],
            responses: [
                new Response(
                    response: 200,
                    description: "Comment successfully",
                    content: new JsonContent(
                        properties: [
                            new Property(
                                property: 'error',
                                description: 'Error status',
                                type: 'boolean',
                                default: false
                            ),
                            new Property(
                                property: 'message',
                                description: 'Success message',
                                type: 'string',
                            ),
                            new Property(
                                property: 'data',
                                description: 'Comment status data',
                                properties: [
                                    new Property(
                                        property: 'commented',
                                        description: 'Current comment status',
                                        type: 'boolean'
                                    ),
                                ],
                                type: 'object'
                            ),
                        ]
                    )
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\BadRequestResponseSchema::class,
                    response: 400,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\ErrorNotFoundResponseSchema::class,
                    response: 404,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\TooManyRequestsResponseSchema::class,
                    response: 429,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\InternalServerResponseSchema::class,
                    response: 500,
                ),
            ]
        )
    ]
    public function __invoke(string|int $comment, ReplyCommentRequest $request, ProcessCommentAction $processCommentAction): BaseHttpResponse
    {
        $comment = Comment::query()
            ->where('status', CommentStatus::APPROVED)
            ->with('reference')
            ->findOrFail($comment);

        $processCommentAction($comment->reference, $request->validated(), $comment);

        return $this
            ->httpResponse()
            ->setMessage(trans('plugins/fob-comment::comment.front.comment_success_message'));
    }
}

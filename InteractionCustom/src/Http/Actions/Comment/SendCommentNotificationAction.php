<?php

namespace CSlant\Blog\InteractionCustom\Http\Actions\Comment;

use Botble\Base\Contracts\BaseModel;
use Bo<PERSON>ble\Base\Facades\AdminHelper;
use Botble\Base\Facades\BaseHelper;
use Botble\Base\Models\BaseModel as BaseModelCore;
use CSlant\Blog\Core\Models\Comment;
use CSlant\Blog\Core\Models\User;
use CSlant\Blog\Core\Notifications\CommentNotification;
use Illuminate\Support\Facades\Notification;

class SendCommentNotificationAction
{
    /**
     * Send notification for new comment or reply
     *
     * @param Comment $comment
     * @param BaseModel $reference
     * @param Comment|null $replyTo
     * @return void
     */
    public function __invoke(Comment $comment, BaseModel $reference, ?Comment $replyTo = null): void
    {
        // Skip notification if in admin panel
        if (AdminHelper::isInAdmin()) {
            return;
        }

        // Get admin users to notify
        $adminUsers = User::query()
            ->where('is_super_user', true)
            ->orWhere('permissions', 'like', '%"comments.index":"1"%')
            ->get();

        if ($adminUsers->isEmpty()) {
            return;
        }

        // Prepare notification data
        $notificationData = [
            'id' => $comment->id,
            'reference_name' => $reference instanceof BaseModelCore ? $reference->name : 'Unknown',
            'reference_url' => route('posts.edit', $reference->id),
            'comment_content' => BaseHelper::clean($comment->content),
            'comment_url' => route('comments.edit', $comment->id),
        ];

        // Add reply information if this is a reply
        if ($replyTo) {
            $notificationData['reply_to'] = [
                'id' => $replyTo->id,
                'content' => BaseHelper::clean($replyTo->content),
            ];
        }

        // Send notification to admin users
        Notification::send($adminUsers, new CommentNotification($notificationData));

        // If this is a reply, also notify the original comment author if they have an account
        if ($replyTo && $replyTo->author_id && $replyTo->author_type === User::class) {
            $originalAuthor = User::find($replyTo->author_id);
            
            if ($originalAuthor && $originalAuthor->id !== $comment->author_id) {
                $notificationData['is_reply_to_your_comment'] = true;
                Notification::send($originalAuthor, new CommentNotification($notificationData));
            }
        }
    }
}

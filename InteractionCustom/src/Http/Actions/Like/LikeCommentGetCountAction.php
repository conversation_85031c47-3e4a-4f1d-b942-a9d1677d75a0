<?php

namespace CSlant\Blog\InteractionCustom\Http\Actions\Like;

use Botble\Base\Http\Responses\BaseHttpResponse;
use CSlant\Blog\Core\Http\Actions\Action;
use CSlant\Blog\Core\Models\Comment;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LikeCommentGetCountAction extends Action
{
    /**
     * @authenticated
     *
     * @method BaseHttpResponse httpResponse()
     * @method BaseHttpResponse setData(mixed $data)
     * @method BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse toApiResponse()
     */
    public function __invoke(Request $request, int $commentId): BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse
    {
        $comment = Comment::query()->with(['likes'])->find($commentId);

        if (!$comment) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(404)
                ->setMessage('Comment not found');
        }

        return $this
            ->httpResponse()
            ->setData([
                'likes_count' => $comment->likesCountDigital(),
            ]);
    }
}

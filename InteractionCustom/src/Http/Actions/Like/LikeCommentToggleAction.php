<?php

namespace CSlant\Blog\InteractionCustom\Http\Actions\Like;

use Botble\Base\Http\Responses\BaseHttpResponse;
use CSlant\Blog\Core\Http\Actions\Action;
use CSlant\Blog\Core\Models\Comment;
use CSlant\Blog\Core\Models\Member;
use CSlant\Blog\Core\Models\User;
use CSlant\Blog\InteractionCustom\Services\InteractionService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Parameter;
use OpenApi\Attributes\Post;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Response;
use OpenApi\Attributes\Schema;

class LikeCommentToggleAction extends Action
{
    protected InteractionService $interactionService;

    public function __construct(InteractionService $interactionService)
    {
        $this->interactionService = $interactionService;
    }

    /**
     * @method BaseHttpResponse httpResponse()
     * @method BaseHttpResponse setData(mixed $data)
     * @method BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse toApiResponse()
     */
    #[
        Post(
            path: "/comments/{commentId}/toggle-like",
            operationId: "toggleLikeComment",
            description: "Toggle like for a comment. If the user has already liked the comment, it will be unliked.
            
            This API will toggle the like status of a comment for the authenticated user.
            
            Apply with --InteractionCustom-- module.
            ",
            summary: "Toggle like status for a comment",
            security: [
                [
                    'sanctum' => [
                        'comments.toggle-like',
                    ],
                ],
            ],
            tags: ['Interaction'],
            parameters: [
                new Parameter(
                    name: 'commentId',
                    description: 'comment Id',
                    in: 'path',
                    required: true,
                    schema: new Schema(type: 'integer', example: 1),
                ),
            ],
            responses: [
                new Response(
                    response: 200,
                    description: "Like status toggled successfully",
                    content: new JsonContent(
                        properties: [
                            new Property(
                                property: 'error',
                                description: 'Error status',
                                type: 'boolean',
                                default: false
                            ),
                            new Property(
                                property: 'message',
                                description: 'Success message',
                                type: 'string',
                            ),
                            new Property(
                                property: 'data',
                                description: 'Like status data',
                                properties: [
                                    new Property(
                                        property: 'liked',
                                        description: 'Current like status',
                                        type: 'boolean'
                                    ),
                                ],
                                type: 'object'
                            ),
                        ]
                    )
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\BadRequestResponseSchema::class,
                    response: 400,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\ErrorNotFoundResponseSchema::class,
                    response: 404,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\TooManyRequestsResponseSchema::class,
                    response: 429,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\InternalServerResponseSchema::class,
                    response: 500,
                ),
            ]
        )
    ]
    public function __invoke(Request $request, int $commentId): BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse
    {
        /** @var Member|User $user */
        $user = Auth::user();

        $comment = Comment::query()->with(['likes'])->find($commentId);

        if (!$comment) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(404)
                ->setMessage('comment not found');
        }

        try {
            $result = DB::transaction(function () use ($comment, $user) {
                return $this->interactionService->interactionCommentByUser($comment, $user, 'like');
            });

            return $this
                ->httpResponse()
                ->setMessage(__('Like status toggled successfully'))
                ->setData([
                    'interaction' => $result,
                    'total_like' => $comment->likesCountDigital(),
                ])
                ->toApiResponse();
        } catch (\Exception|\Throwable $e) {
            return $this
                ->httpResponse()
                ->setError()
                ->setStatusCode(500)
                ->setMessage($e->getMessage());
        }
    }
}

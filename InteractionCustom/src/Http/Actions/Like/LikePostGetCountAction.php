<?php

namespace CSlant\Blog\InteractionCustom\Http\Actions\Like;

use Botble\Base\Http\Responses\BaseHttpResponse;
use CSlant\Blog\Core\Http\Actions\Action;
use CSlant\Blog\Core\Models\Post;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LikePostGetCountAction extends Action
{
    /**
     * @authenticated
     *
     * @method BaseHttpResponse httpResponse()
     * @method BaseHttpResponse setData(mixed $data)
     * @method BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse toApiResponse()
     */
    public function __invoke(Request $request, int $postId): BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse
    {
        $post = Post::query()->with(['likes'])->find($postId);

        if (!$post) {
            return $this
                ->httpResponse()
                ->setError()
                ->setCode(404)
                ->setMessage('Post not found');
        }

        return $this
            ->httpResponse()
            ->setData([
                'likes_count' => $post->likesCountDigital(),
            ]);
    }
}

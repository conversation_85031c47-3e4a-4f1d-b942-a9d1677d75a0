<?php

namespace CSlant\Blog\InteractionCustom\Http\Middlewares;

use Closure;
use CSlant\Blog\Core\Models\Member;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CheckVerifyEmailMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  Closure  $next
     *
     * @return JsonResponse|mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();

        if ($email = $request->input('email')) {
            $user = Member::query()->where('email', $email)->first();
        }

        if (!$user || !$user->confirmed_at) {
            return response()->json([
                'message' => __('Email not verified.'),
            ], 403);
        }

        return $next($request);
    }
}

<?php

namespace CSlant\Blog\InteractionCustom\Http\Requests\Comment;

use CSlant\Blog\Api\Http\Requests\JsonFormRequest;
use CSlant\Blog\InteractionCustom\Enums\CommentStatus;

class CommentGetFiltersRequest extends JsonFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'reference_id' => 'required|numeric',
            'reference_type' => 'nullable|string',
            'status' => [
                'nullable',
                'array',
                'in:' . implode(',', CommentStatus::STATUSES),
            ],
            'author_id' => 'nullable|array',
            'author_id.*' => 'nullable|numeric',
            'author_type' => 'nullable|string',
            'reply_to' => 'nullable|numeric',
            'ip_address' => 'nullable|string',
            'user_agent' => 'nullable|string',
            'page' => 'nullable|numeric',
            'order_by' => 'nullable|string',
            'order' => 'nullable|string|in:asc,desc,ASC,DESC',
            'per_page' => 'nullable|numeric|between:1,100',
        ];
    }
}

<?php

namespace CSlant\Blog\InteractionCustom\Http\Requests\Comment;

use Botble\Captcha\Facades\Captcha;
use Botble\Support\Http\Requests\Request;
use CSlant\Blog\InteractionCustom\Supports\Comment\CommentHelper;

class CommentUpdateRequest extends Request
{
    protected function prepareForValidation(): void
    {
        $preparedData = CommentHelper::preparedDataForFill($this->user());

        $this->merge($preparedData);
    }

    public function rules(): array
    {
        $rules = [
            'content' => ['required', 'string', 'max:1000'],
        ];
        if (CommentHelper::isEnableReCaptcha()) {
            $rules = [...Captcha::rules()];
        }

        return $rules;
    }

    public function attributes(): array
    {
        $attributes = [
            'content' => __('Comment'),
        ];

        if (CommentHelper::isEnableReCaptcha()) {
            $attributes = [...$attributes, ...Captcha::attributes()];
        }

        return $attributes;
    }
}

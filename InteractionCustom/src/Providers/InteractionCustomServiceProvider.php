<?php

namespace CSlant\Blog\InteractionCustom\Providers;

use Bo<PERSON>ble\Dashboard\Events\RenderingDashboardWidgets;
use CSlant\Blog\InteractionCustom\Hooks\LikeWidgetHook;
use CSlant\Blog\InteractionCustom\Http\Middlewares\CheckVerifyEmailMiddleware;
use Illuminate\Routing\Router;
use Illuminate\Support\ServiceProvider;

class InteractionCustomServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $routePath = __DIR__.'/../../routes/interaction-api.php';
        if (file_exists($routePath)) {
            $this->loadRoutesFrom($routePath);
        }

        $this->loadMigrationsFrom(__DIR__.'/../../database/migrations');

        $this->registerMiddlewares();

        $this->addAdminDashboardWidgets();
    }

    /**
     * Register middlewares for the package.
     */
    protected function registerMiddlewares(): void
    {
        /** @var Router $router */
        $router = $this->app->make('router');

        // Register route middleware
        $router->aliasMiddleware('check-verify-email', CheckVerifyEmailMiddleware::class);
    }

    public function addAdminDashboardWidgets(): void
    {
        // Register the dashboard widgets
        $this->app['events']->listen(RenderingDashboardWidgets::class, function (): void {
            add_filter(DASHBOARD_FILTER_ADMIN_LIST, [LikeWidgetHook::class, 'addLikeStatsWidget'], 12, 2);
        });
    }
}

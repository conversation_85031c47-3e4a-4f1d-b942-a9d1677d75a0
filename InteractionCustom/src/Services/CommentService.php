<?php

namespace CSlant\Blog\InteractionCustom\Services;

use CSlant\Blog\Core\Models\Comment;
use CSlant\Blog\Core\Models\Post;
use CSlant\Blog\InteractionCustom\Supports\Comment\CommentQuery;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;

class CommentService
{
    public function getFilters(array $filters): LengthAwarePaginator
    {
        $query = Comment::query()
            ->where('reference_id', $filters['reference_id'])
            ->whereNull('reply_to')
            ->where('reference_type', $filters['reference_type'] ?? Post::class);

        $query = CommentQuery::setBaseCustomFilterQuery($query, $filters);
        $query = $query->where('status', '=', 'approved');

        $query->orderBy(
            Arr::get($filters, 'order_by', 'updated_at'),
            Arr::get($filters, 'order', 'desc')
        );

        return $query->paginate(
            Arr::get($filters, 'per_page', 10),
            ['*'],
            'page',
            Arr::get($filters, 'page', 1)
        );
    }
}

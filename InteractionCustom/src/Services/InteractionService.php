<?php

namespace CSlant\Blog\InteractionCustom\Services;

use Bo<PERSON>ble\Member\Models\Member as BaseMember;
use CSlant\Blog\Core\Models\Comment;
use CSlant\Blog\Core\Models\Member;
use CSlant\Blog\Core\Models\Post;
use CSlant\Blog\Core\Models\User;
use CSlant\LaravelLike\Enums\InteractionTypeEnum;
use CSlant\LaravelLike\Models\Like;
use Illuminate\Database\Eloquent\Model;

class InteractionService
{
    public function interactionPostByUser(Post $post, BaseMember|Member|User $user, string $type): string
    {
        return $this->handleInteraction($post, $user, $type);
    }

    public function interactionCommentByUser(Comment $comment, BaseMember|Member|User $user, string $type): string
    {
        return $this->handleInteraction($comment, $user, $type);
    }

    private function handleInteraction(
        Model $model,
        BaseMember|Member|User $user,
        string $type
    ): string {
        /** @var null|Like $interaction */
        $interaction = $model->withInteractionBy($user->id)->first();
        $interactionType = InteractionTypeEnum::getTypeByValue($type);

        if ($interaction instanceof Like) {
            if (($interactionType === InteractionTypeEnum::LIKE && $interaction->isLiked())
                || ($interactionType === InteractionTypeEnum::DISLIKE && $interaction->isDisliked())
            ) {
                $interaction->forceDelete();

                return InteractionTypeEnum::NEUTRAL->value;
            }

            $interaction->toggleLikeInteraction();
            $interaction->save();
        } else {
            $model->likes()->create([
                'user_id' => $user->id,
                'type' => $interactionType,
            ]);
        }

        return $interactionType->value;
    }
}

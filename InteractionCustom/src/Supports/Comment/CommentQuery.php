<?php

namespace CSlant\Blog\InteractionCustom\Supports\Comment;

use Botble\Base\Models\BaseQueryBuilder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class CommentQuery
{
    /**
     * @param  BaseQueryBuilder|Builder<Model>  $query
     * @param  array<string, mixed>  $filters
     *
     * @return BaseQueryBuilder|Builder<Model>
     */
    public static function setBaseCustomFilterQuery(
        Builder|BaseQueryBuilder $query,
        array $filters
    ): Builder|BaseQueryBuilder {
        if ($filters['status'] !== null) {
            $query = $query->whereIn('status', array_filter((array) $filters['status']));
        }

        if ($filters['author_id'] !== null) {
            $query = $query->whereIn('author_id', array_filter((array) $filters['author_id']));
        }

        if ($filters['author_type'] !== null) {
            $query = $query->where('author_type', $filters['author_type']);
        }

        if ($filters['reply_to'] !== null) {
            $query = $query->where('reply_to', $filters['reply_to']);
        }

        if ($filters['ip_address'] !== null) {
            $query = $query->where('ip_address', $filters['ip_address']);
        }

        if ($filters['user_agent'] !== null) {
            $query = $query->where('user_agent', 'like', '%' . $filters['user_agent'] . '%');
        }

        return $query;
    }
}

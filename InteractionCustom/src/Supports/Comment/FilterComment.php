<?php

namespace CSlant\Blog\InteractionCustom\Supports\Comment;

class FilterComment
{
    /**
     * @param  array<string, mixed>  $request
     *
     * @return array<string, mixed>
     */
    public static function setFilters(array $request): array
    {
        return [
            'reference_id' => $request['reference_id'] ?? null,
            'reference_type' => $request['reference_type'] ?? null,
            'status' => $request['status'] ?? null,
            'author_id' => $request['author_id'] ?? null,
            'author_type' => $request['author_type'] ?? null,
            'reply_to' => $request['reply_to'] ?? null,
            'ip_address' => $request['ip_address'] ?? null,
            'user_agent' => $request['user_agent'] ?? null,
            'page' => $request['page'] ?? 1,
            'per_page' => $request['per_page'] ?? 10,
            'order_by' => $request['order_by'] ?? 'created_at',
            'order' => $request['order'] ?? 'desc',
        ];
    }
}

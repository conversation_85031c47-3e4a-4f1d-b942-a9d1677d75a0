<?php

namespace CSlant\Blog\Manager\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class BlockDomainMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  Closure(Request): (Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $isAllowedRequest = (
            $request->ajax()
                || $request->wantsJson()
                || $this->isAllowedPath($request)
        ) && $this->hostIsAllowed($request->getHost());

        if ($isAllowedRequest || str_contains((string) config('blog-core.laravel_domain'), $request->getHost())) {
            return $next($request);
        }

        return redirect()->route('blog-core.index');
    }

    private function isAllowedPath(Request $request): bool
    {
        $allowedPaths = [
            config('blog-api.defaults.route_prefix').'/*',
            config('blog-core.admin_path').'/*',
            config('blog-core.fe_path').'/*',
            'blog-*.xml',
        ];

        foreach ($allowedPaths as $path) {
            if ($request->is($path)) {
                return true;
            }
        }

        return false;
    }

    private function hostIsAllowed(string $host): bool
    {
        $allowedDomains = [
            (string) config('blog-core.fe_url'),
            (string) config('blog-core.api_url'),
            (string) config('blog-core.admin_url'),
        ];

        return (bool) array_filter($allowedDomains, fn ($domain) => str_contains($domain, $host));
    }
}

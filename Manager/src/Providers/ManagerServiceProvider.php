<?php

namespace CSlant\Blog\Manager\Providers;

use Botble\Base\Supports\ServiceProvider;
use CSlant\Blog\Manager\Http\Middleware\BlockDomainMiddleware;

class ManagerServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $this->app['router']->aliasMiddleware('cslant-block-domain', BlockDomainMiddleware::class);
    }

    public function register(): void
    {
        $this->app->singleton(
            \Illuminate\Contracts\Debug\ExceptionHandler::class,
            \CSlant\Blog\Manager\Exceptions\Handler::class
        );
    }
}

<?php

use CSlant\Blog\Core\Models\Member;

/**
 * If verify_email is true (1), the system will send a verification email.
 * If verify_email is false (0), the system will not send a verification email.
 */
return [
    'provider' => [
        'model' => Member::class,
        'guard' => 'member',
        'password_broker' => 'members',
        'verify_email' => env('CMS_MEMBER_VERIFY_EMAIL', false),
    ],
    'end_point_confirm' => env('USER_CUSTOM_CONFIRM_ENDPOINT', '/profile'),
];

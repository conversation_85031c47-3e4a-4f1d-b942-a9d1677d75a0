<?php

use Illuminate\Support\Facades\Route;
use CSlant\Blog\MemberCustom\Http\Actions\Auth\AuthLoginAction;
use CSlant\Blog\MemberCustom\Http\Actions\Auth\AuthRegisterAction;
use CSlant\Blog\MemberCustom\Http\Actions\Auth\AuthConfirmAction;
use CSlant\Blog\MemberCustom\Http\Actions\Auth\AuthLogoutAction;
use CSlant\Blog\MemberCustom\Http\Actions\Auth\AuthCheckMailAction;
use CSlant\Blog\MemberCustom\Http\Actions\Auth\AuthForgotPasswordAction;
use CSlant\Blog\MemberCustom\Http\Actions\Profile\ProfileGetProfileAction;
use CSlant\Blog\MemberCustom\Http\Actions\Profile\ProfileUpdateAvatarAction;
use CSlant\Blog\MemberCustom\Http\Actions\Profile\ProfileUpdateProfileAction;
use CSlant\Blog\MemberCustom\Http\Actions\Profile\ProfileUpdatePasswordAction;
use CSlant\Blog\MemberCustom\Http\Actions\Auth\AuthResetPasswordAction;

/*
|--------------------------------------------------------------------------
| Member API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for member authentication and
| management. These routes are loaded by the MemberCustomServiceProvider.
|
*/

$routePrefix = config('blog-api.defaults.route_prefix');

Route::prefix($routePrefix)->name("$routePrefix.")->middleware('api')->group(function () {
    Route::group(['prefix' => 'auth'], function () {
        Route::post('login', AuthLoginAction::class)->name('auth.login');
        Route::post('register', AuthRegisterAction::class)->name('auth.register');
        Route::get('register/confirm/{memberId}', AuthConfirmAction::class)->name('auth.confirm');
        Route::post('check/email', AuthCheckMailAction::class)->name('auth.check.email');
        Route::post('forgot-password', AuthForgotPasswordAction::class)->name('auth.forgot.password')->middleware('check-verify-email');
        Route::post('reset-password', AuthResetPasswordAction::class)->name('auth.reset.password');

        Route::group([
            'middleware' => ['auth:sanctum', 'check-member-status'],
        ], function (): void {
            Route::match(['GET', 'POST'], 'logout', AuthLogoutAction::class)->name('auth.logout');
            Route::get( 'me', ProfileGetProfileAction::class)->name('auth.get.profile');
            Route::put( 'me', ProfileUpdateProfileAction::class)->name('auth.update.profile');
            Route::post( 'update/avatar', ProfileUpdateAvatarAction::class)->name('auth.update.avatar');
            Route::post( 'update/password', ProfileUpdatePasswordAction::class)->name('auth.update.password');
        });
    });
});


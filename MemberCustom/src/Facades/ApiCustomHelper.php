<?php

namespace CSlant\Blog\MemberCustom\Facades;

use CSlant\Blog\MemberCustom\Supports\ApiCustomHelper as ApiCustomHelperSupport;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Facade;

/**
 * @method static string modelName()
 * @method static \CSlant\Blog\MemberCustom\Supports\ApiCustomHelper setModelName(string $modelName)
 * @method static string|null guard()
 * @method static string|null passwordBroker()
 * @method static string|null getConfig(string $key, $default = null)
 * @method static mixed setConfig(array $config)
 * @method static Model|null newModel()
 * @method static string getTable()
 * @method static bool enabled()
 *
 * @see ApiCustomHelperSupport
 */
class ApiCustomHelper extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return ApiCustomHelperSupport::class;
    }
}

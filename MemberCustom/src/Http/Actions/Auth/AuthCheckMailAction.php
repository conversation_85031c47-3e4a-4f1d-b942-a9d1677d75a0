<?php

namespace CSlant\Blog\MemberCustom\Http\Actions\Auth;

use <PERSON><PERSON><PERSON>\Api\Facades\ApiHelper;
use Bo<PERSON><PERSON>\Api\Http\Requests\CheckEmailRequest;
use CSlant\Blog\Core\Http\Responses\Base\BaseHttpResponse;

class AuthCheckMailAction
{
    /**
     * AuthenticationController::checkMail
     *
     * @param  CheckEmailRequest  $request
     * @param  BaseHttpResponse  $response
     *
     * @return BaseHttpResponse
     */
    public function __invoke(CheckEmailRequest $request, BaseHttpResponse $response): BaseHttpResponse
    {
        $user = ApiHelper::newModel()->where('email', $request->input('email'))->first();

        $data = [
            'exists' => (bool) $user,
        ];

        if ($user) {
            $data['user'] = [];

            if ($user->first_name || $user->last_name) {
                $data['user']['first_name'] = $user->first_name;
                $data['user']['last_name'] = $user->last_name;
            }

            $data['user']['name'] = $user->name;
            $data['user']['email'] = $user->email;
        }

        return $response
            ->setData($data);
    }
}

<?php

namespace CSlant\Blog\MemberCustom\Http\Actions\Auth;

use Botble\ACL\Traits\RegistersUsers;
use Botble\Member\Models\Member;
use Carbon\Carbon;
use CSlant\Blog\Core\Http\Actions\Action;
use Illuminate\Contracts\Auth\Factory;
use Illuminate\Contracts\Auth\StatefulGuard;
use Illuminate\Foundation\Application;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\URL;

class AuthConfirmAction extends Action
{
    use RegistersUsers;

    /**
     * @param  int|string  $memberId
     * @param  Request  $request
     *
     * @return Application|Redirector|RedirectResponse
     */
    public function __invoke(int|string $memberId, Request $request): Application|RedirectResponse|Redirector
    {
        abort_unless(setting('member_enabled_registration', true), 404);
        abort_unless(URL::hasValidSignature($request), 404);

        $member = Member::query()->findOrFail($memberId);
        $member->confirmed_at = Carbon::now();
        $member->save();

        return redirect(config('blog-core.fe_url_with_path').config('general.end_point_confirm'));
    }

    /**
     * @return Application|Factory|StatefulGuard
     */
    protected function guard(): Factory|StatefulGuard|Application
    {
        return auth('member');
    }
}

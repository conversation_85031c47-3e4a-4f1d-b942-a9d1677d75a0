<?php

namespace CSlant\Blog\MemberCustom\Http\Actions\Auth;

use Bo<PERSON><PERSON>\ACL\Traits\SendsPasswordResetEmails;
use Bo<PERSON><PERSON>\Api\Facades\ApiHelper;
use Bo<PERSON>ble\Api\Http\Requests\ForgotPasswordRequest;
use Illuminate\Contracts\Auth\PasswordBroker;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Password;
use Illuminate\Validation\ValidationException;

class AuthForgotPasswordAction
{
    use SendsPasswordResetEmails;

    /**
     * @param  ForgotPasswordRequest  $request
     *
     * @return JsonResponse|RedirectResponse
     * @throws ValidationException
     */
    public function __invoke(ForgotPasswordRequest $request): JsonResponse|RedirectResponse
    {
        $this->validateEmail($request);

        $response = $this->broker()->sendResetLink(
            $request->only('email')
        );

        return $response == Password::RESET_LINK_SENT
            ? $this->sendResetLinkResponse($request, $response)
            : $this->sendResetLinkFailedResponse($request, $response);
    }

    /**
     * @return PasswordBroker
     */
    public function broker(): PasswordBroker
    {
        return Password::broker(ApiHelper::passwordBroker());
    }
}

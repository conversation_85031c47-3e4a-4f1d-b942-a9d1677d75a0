<?php

namespace CSlant\Blog\MemberCustom\Http\Actions\Auth;

use Botble\Api\Facades\ApiHelper;
use Botble\Api\Http\Requests\LoginRequest;
use Botble\Member\Enums\MemberStatusEnum;
use Botble\Member\Models\Member;
use Carbon\Carbon;
use CSlant\Blog\Core\Http\Responses\Base\BaseHttpResponse;
use Illuminate\Support\Facades\Auth;
use OpenApi\Attributes\Get;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Parameter;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Response;
use OpenApi\Attributes\Schema;

class AuthLoginAction
{
    /**
     * AuthenticationController::login
     *
     * @param  LoginRequest  $request
     * @param  BaseHttpResponse  $response
     *
     * @return BaseHttpResponse
     */
    #[
        Get(
            path: "/auth/login",
            operationId: "postAuthLogin",
            description: "Login with email and password
             
             This API will authenticate the user using their email and password. If successful, it will return an access token for further requests.
             
             Apply with --MemberCustom-- module.",
            summary: "Post login with email and password",
            tags: ["Author"],
            parameters: [
                new Parameter(
                    name: 'email',
                    description: 'The email address of the user attempting to log in. Must be a valid email format. Invalid if missing or incorrectly formatted.',
                    in: 'query',
                    required: false,
                    schema: new Schema(type: 'string', default: null)
                ),
                new Parameter(
                    name: 'password',
                    description: 'The password of the user attempting to log in. Must be a non-empty string. Invalid if missing or not a string.',
                    in: 'query',
                    required: false,
                    schema: new Schema(type: 'string', default: null)
                ),
            ],
            responses: [
                new Response(
                    response: 200,
                    description: "Login successful. Token is returned.",
                    content: new JsonContent(
                        properties: [
                            new Property(
                                property: 'error',
                                description: 'Error status',
                                type: 'boolean',
                                default: false
                            ),
                            new Property(
                                property: "token",
                                description: "Access token for authentication",
                                type: "string",
                                example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                            ),
                        ]
                    )
                ),
                new Response(
                    response: 422,
                    description: "Validation failed. The input did not meet the required rules.",
                    content: new JsonContent(
                        properties: [
                            new Property(
                                property: 'error',
                                description: 'Indicates a validation failure.',
                                type: 'boolean',
                                default: true
                            ),
                            new Property(
                                property: 'message',
                                description: 'A general message about the validation error.',
                                type: 'string',
                                example: 'The given data was invalid.'
                            ),
                            new Property(
                                property: 'errors',
                                description: 'Detailed error messages for each field that failed validation.',
                                type: 'object',
                                example: [
                                    'email' => ['The email field must be a valid email address.'],
                                    'password' => ['The password field is required.'],
                                ]
                            ),
                        ]
                    )
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\BadRequestResponseSchema::class,
                    response: 400,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\ErrorNotFoundResponseSchema::class,
                    response: 404,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\InternalServerResponseSchema::class,
                    response: 500,
                ),
            ]
        )
    ]
    public function __invoke(LoginRequest $request, BaseHttpResponse $response): BaseHttpResponse
    {
        $member = Member::where('email', $request->input('email'))->first();
        if ($member) {
            $status = $member->status;
            $canLogin = false;

            if ($status === MemberStatusEnum::PUBLISHED()->getValue()) {
                $canLogin = true;
            } elseif (str_starts_with($status, 'banned')) {
                if ($status !== 'banned' && Carbon::now()->greaterThan($member->banned_until)) {
                    $canLogin = true;
                }
            }

            if (!$canLogin) {
                return $response
                    ->setError()
                    ->setCode(403)
                    ->setMessage(__('Account is currently banned!'));
            }
        }

        if (
            Auth::guard(ApiHelper::guard())
                ->attempt([
                    'email' => $request->input('email'),
                    'password' => $request->input('password'),
                ])
        ) {
            $user = $request->user(ApiHelper::guard());

            $user->update([
                'status' => MemberStatusEnum::PUBLISHED(),
                'banned_until' => null,
            ]);

            $token = $user->createToken($request->input('token_name', 'Personal Access Token'));

            return $response
                ->setData(['token' => $token->plainTextToken]);
        }

        return $response
            ->setError()
            ->setCode(422)
            ->setMessage(__('Email or password is not correct!'));
    }
}

<?php

namespace CSlant\Blog\MemberCustom\Http\Actions\Auth;

use CSlant\Blog\Core\Http\Responses\Base\BaseHttpResponse;
use Illuminate\Http\Request;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Post;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Response;

class AuthLogoutAction
{
    /**
     * AuthenticationController::logout
     *
     * @param  Request  $request
     * @param  BaseHttpResponse  $response
     *
     * @return BaseHttpResponse
     */
    #[
        Post(
            path: "/auth/logout",
            operationId: "authLogout",
            description: "Logout the currently authenticated user and invalidate their token.
            
            This API will delete the user's token from the database, effectively logging them out of the system.
            
            Apply with --MemberCustom-- module.",
            summary: "Logout user account",
            security: [
                ['sanctum' => [
                    'auth.logout',
                ]],
            ],
            tags: ["Auth"],
            responses: [
                new Response(
                    response: 200,
                    description: "Successfully logged out",
                    content: new JsonContent(
                        properties: [
                            new Property(
                                property: 'error',
                                description: 'Error status',
                                type: 'boolean',
                                default: false
                            ),
                            new Property(
                                property: 'message',
                                description: 'Success message',
                                type: 'string',
                                example: 'You have been successfully logged out!'
                            ),
                        ]
                    )
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\UnauthenticatedResponseSchema::class,
                    response: 401,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\BadRequestResponseSchema::class,
                    response: 400,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\ErrorNotFoundResponseSchema::class,
                    response: 404,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\InternalServerResponseSchema::class,
                    response: 500,
                ),
            ]
        )
    ]
    public function __invoke(Request $request, BaseHttpResponse $response): BaseHttpResponse
    {
        if (!$request->user()) {
            abort(401);
        }

        $request->user()->tokens()->delete();

        return $response->setMessage(__('You have been successfully logged out!'));
    }
}

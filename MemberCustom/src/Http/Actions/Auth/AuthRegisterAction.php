<?php

namespace CSlant\Blog\MemberCustom\Http\Actions\Auth;

use Botble\Api\Http\Requests\RegisterRequest;
use Carbon\Carbon;
use CSlant\Blog\Core\Http\Responses\Base\BaseHttpResponse;
use CSlant\Blog\Core\Models\Member;
use CSlant\Blog\MemberCustom\Facades\ApiCustomHelper;
use CSlant\Blog\MemberCustom\Services\MemberService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Post;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\RequestBody;
use OpenApi\Attributes\Response;

class AuthRegisterAction
{
    protected MemberService $memberService;

    public function __construct(MemberService $memberService)
    {
        $this->memberService = $memberService;
    }

    #[
        Post(
            path: "/auth/register",
            operationId: "authRegister",
            description: "Register a new user account with email, password and other required information.
            
            This API will create a new user account in the system and send a verification email to the user if email verification is enabled.
            
            Apply with --MemberCustom-- module.",
            summary: "Register a new user account",
            requestBody: new RequestBody(
                description: "User registration data",
                required: true,
                content: new JsonContent(
                    properties: [
                        new Property(
                            property: 'first_name',
                            description: 'First name of the user',
                            type: 'string',
                            example: 'Francis'
                        ),
                        new Property(
                            property: 'last_name',
                            description: 'Last name of the user',
                            type: 'string',
                            example: 'Duong'
                        ),
                        new Property(
                            property: 'email',
                            description: 'Email address',
                            type: 'string',
                            format: 'email',
                            example: '<EMAIL>'
                        ),
                        new Property(
                            property: 'password',
                            description: 'Password',
                            type: 'string',
                            format: 'password',
                            example: 'password123'
                        ),
                        new Property(
                            property: 'password_confirmation',
                            description: 'Password confirmation',
                            type: 'string',
                            format: 'password',
                            example: 'password123',
                        ),
                        new Property(
                            property: 'phone',
                            description: 'Phone number',
                            type: 'string',
                            example: '0909090909'
                        ),
                    ]
                )
            ),
            tags: ["Author"],
            responses: [
                new Response(
                    response: 200,
                    description: "User registered successfully",
                    content: new JsonContent(
                        properties: [
                            new Property(
                                property: 'error',
                                description: 'Error status',
                                type: 'boolean',
                                default: false
                            ),
                            new Property(
                                property: 'message',
                                description: 'Success message',
                                type: 'string'
                            ),
                            new Property(
                                property: 'data',
                                description: 'Authentication token data',
                                properties: [
                                    new Property(
                                        property: 'token',
                                        description: 'Authentication token',
                                        type: 'string'
                                    ),
                                ],
                                type: 'object'
                            ),
                        ]
                    )
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\BadRequestResponseSchema::class,
                    response: 400,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\ErrorNotFoundResponseSchema::class,
                    response: 404,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\TooManyRequestsResponseSchema::class,
                    response: 429,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\InternalServerResponseSchema::class,
                    response: 500,
                ),
            ]
        )
    ]
    public function __invoke(RegisterRequest $request, BaseHttpResponse $response): BaseHttpResponse
    {
        $request->merge(['password' => Hash::make($request->input('password'))]);

        if ($request->has('name')) {
            $request->merge(['first_name' => $request->input('name')]);
            $request->merge(['last_name' => '']);
        }

        $member = Member::query()->create($request->only([
            'first_name',
            'last_name',
            'email',
            'phone',
            'password',
        ]));

        if (ApiCustomHelper::getConfig('verify_email')) {
            $token = Hash::make(Str::random(32));

            $member->email_verify_token = $token;

            /** @var Member $member */
            $this->memberService->sendEmailVerification($member);
        } else {
            $member->confirmed_at = Carbon::now();
        }

        $member->save();

        $token = $member->createToken($request->input('token_name', 'Personal Access Token'));

        return $response
            ->setData(['token' => $token->plainTextToken])
            ->setMessage(__('Registered successfully! We emailed you to verify your account!'));
    }
}

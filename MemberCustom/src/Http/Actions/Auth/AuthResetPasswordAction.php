<?php

namespace CSlant\Blog\MemberCustom\Http\Actions\Auth;

use Botble\ACL\Traits\ResetsPasswords;
use Botble\Member\Http\Requests\Fronts\Auth\ResetPasswordRequest;
use CSlant\Blog\MemberCustom\Traits\CustomResetsPasswords;
use Illuminate\Http\JsonResponse;

class AuthResetPasswordAction
{
    use ResetsPasswords, CustomResetsPasswords {
        ResetsPasswords::broker insteadof CustomResetsPasswords;
        ResetsPasswords::reset as parentReset;
    }

    /**
     * @param  ResetPasswordRequest  $request
     *
     * @return JsonResponse
     */
    public function __invoke(ResetPasswordRequest $request): JsonResponse
    {
        return $this->parentReset($request);
    }
}

<?php

namespace CSlant\Blog\MemberCustom\Http\Actions\Profile;

use Botble\Api\Http\Resources\UserResource;
use CSlant\Blog\Api\OpenApi\Schemas\Models\MemberSchema;
use CSlant\Blog\Core\Http\Responses\Base\BaseHttpResponse;
use Illuminate\Http\Request;
use OpenApi\Attributes\Get;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Response;

class ProfileGetProfileAction
{
    /**
     * ProfileController::getProfile
     *
     * @param  Request  $request
     * @param  BaseHttpResponse  $response
     *
     * @return BaseHttpResponse
     */
    #[
        Get(
            path: "/auth/me",
            operationId: "getProfile",
            description: "Get the authenticated user's profile information including personal details and settings
            
            This API will return the user's profile information, including first name, last name, email, phone number, and other optional fields. The user must be authenticated to access this endpoint.

            Apply with --MemberCustom-- module.",
            summary: "Get user profile",
            security: [
                [
                    'sanctum' => [
                        'auth.get.profile',
                    ],
                ],
            ],
            tags: ["Profile"],
            responses: [
                new Response(
                    response: 200,
                    description: "Profile retrieved successfully",
                    content: new JsonContent(
                        properties: [
                            new Property(
                                property: 'error',
                                description: 'Error status',
                                type: 'boolean',
                                default: false
                            ),
                            new Property(
                                property: 'data',
                                ref: MemberSchema::class
                            ),
                            new Property(
                                property: 'message',
                                type: 'string',
                                example: 'Get profile successfully!'
                            ),
                        ]
                    )
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\BadRequestResponseSchema::class,
                    response: 400,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\ErrorNotFoundResponseSchema::class,
                    response: 404,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\TooManyRequestsResponseSchema::class,
                    response: 429,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\InternalServerResponseSchema::class,
                    response: 500,
                ),
            ]
        )
    ]
    public function __invoke(Request $request, BaseHttpResponse $response): BaseHttpResponse
    {
        return $response->setData(new UserResource($request->user()));
    }
}

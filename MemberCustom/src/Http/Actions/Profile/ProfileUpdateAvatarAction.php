<?php

namespace CSlant\Blog\MemberCustom\Http\Actions\Profile;

use Botble\Media\Facades\RvMedia;
use CSlant\Blog\Core\Http\Responses\Base\BaseHttpResponse;
use CSlant\Blog\MemberCustom\Http\Requests\Profile\ProfileUpdateAvatarRequest;
use Exception;
use Illuminate\Support\Arr;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\MediaType;
use OpenApi\Attributes\Post;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\RequestBody;
use OpenApi\Attributes\Response;
use OpenApi\Attributes\Schema;

class ProfileUpdateAvatarAction
{
    /**
     * ProfileController::updateAvatar
     *
     * @param  ProfileUpdateAvatarRequest  $request
     * @param  BaseHttpResponse  $response
     *
     * @return BaseHttpResponse
     */
    #[
        Post(
            path: '/auth/update/avatar',
            operationId: 'updateAvatar',
            description: 'Update the authenticated user\'s avatar image
            
            This API will allow the user to upload a new avatar image. The image will be stored in the media library and the user\'s profile will be updated with the new avatar URL.,
            
            Apply with --MemberCustom-- module.',
            summary: 'Update user avatar',
            security: [
                [
                    'sanctum' => [
                        'auth.update.avatar',
                    ],
                ],
            ],
            requestBody: new RequestBody(
                required: true,
                content: new MediaType(
                    mediaType: 'multipart/form-data',
                    schema: new Schema(
                        required: ['avatar'],
                        properties: [
                            new Property(
                                property: 'avatar',
                                type: 'string',
                                format: 'binary'
                            ),
                        ]
                    )
                )
            ),
            tags: ["Profile"],
            responses: [
                new Response(
                    response: 200,
                    description: 'Avatar updated successfully',
                    content: new JsonContent(
                        properties: [
                            new Property(
                                property: 'error',
                                type: 'boolean',
                                example: false
                            ),
                            new Property(
                                property: 'data',
                                properties: [
                                    new Property(
                                        property: 'avatar',
                                        type: 'string',
                                        format: 'uri',
                                        example: 'https://example.com/storage/users/avatar.jpg'
                                    ),
                                ],
                                type: 'object'
                            ),
                            new Property(
                                property: 'message',
                                type: 'string',
                                example: 'Update avatar successfully!'
                            ),
                        ]
                    )
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\BadRequestResponseSchema::class,
                    response: 400,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\ErrorNotFoundResponseSchema::class,
                    response: 404,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\TooManyRequestsResponseSchema::class,
                    response: 429,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\InternalServerResponseSchema::class,
                    response: 500,
                ),
            ]
        )
    ]
    public function __invoke(ProfileUpdateAvatarRequest $request, BaseHttpResponse $response): BaseHttpResponse
    {
        try {
            $file = RvMedia::handleUpload($request->file('avatar'), 0, 'users');
            if (Arr::get($file, 'error') !== true) {
                $user = $request->user();
                $user->update(['avatar_id' => $file['data']->id]);

                return $response
                    ->setData([
                        'avatar' => $user->avatar_url,
                    ])
                    ->setMessage(__('Update avatar successfully!'));
            }

            return $response
                ->setError()
                ->setMessage(__('Update failed!'));
        } catch (Exception $ex) {
            return $response
                ->setError()
                ->setMessage($ex->getMessage());
        }
    }
}

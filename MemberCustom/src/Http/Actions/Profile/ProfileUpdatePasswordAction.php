<?php

namespace CSlant\Blog\MemberCustom\Http\Actions\Profile;

use CSlant\Blog\Core\Http\Responses\Base\BaseHttpResponse;
use CSlant\Blog\MemberCustom\Http\Requests\Profile\ProfileUpdatePasswordRequest;
use Illuminate\Support\Facades\Hash;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Post;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\RequestBody;
use OpenApi\Attributes\Response;

class ProfileUpdatePasswordAction
{
    /**
     * ProfileController::updatePassword
     *
     * @param  ProfileUpdatePasswordRequest  $request
     * @param  BaseHttpResponse  $response
     *
     * @return BaseHttpResponse
     */
    #[
        Post(
            path: '/auth/update/password',
            operationId: 'updatePassword',
            description: 'Update the authenticated user\'s password
            
            This API will allow the user to change their password. The user must provide their current password and the new password they wish to set. If the current password is incorrect, an error will be returned.
            
            Apply with --<PERSON><PERSON>ustom-- module.',
            summary: 'Update user password',
            security: [
                [
                    'sanctum' => [
                        'auth.update.password',
                    ],
                ],
            ],
            requestBody: new RequestBody(
                required: true,
                content: new JsonContent(
                    required: ['current_password', 'password'],
                    properties: [
                        new Property(
                            property: 'current_password',
                            description: 'Current password',
                            type: 'string'
                        ),
                        new Property(
                            property: 'password',
                            description: 'New password',
                            type: 'string'
                        ),
                    ]
                )
            ),
            tags: ["Profile"],
            responses: [
                new Response(
                    response: 200,
                    description: 'Password updated successfully',
                    content: new JsonContent(
                        properties: [
                            new Property(
                                property: 'error',
                                type: 'boolean',
                                example: false
                            ),
                            new Property(
                                property: 'message',
                                type: 'string',
                                example: 'Update password successfully!'
                            ),
                        ]
                    )
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\BadRequestResponseSchema::class,
                    response: 400,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\ErrorNotFoundResponseSchema::class,
                    response: 404,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\TooManyRequestsResponseSchema::class,
                    response: 429,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\InternalServerResponseSchema::class,
                    response: 500,
                ),
            ]
        )
    ]
    public function __invoke(ProfileUpdatePasswordRequest $request, BaseHttpResponse $response): BaseHttpResponse
    {
        if (!Hash::check($request->validated('current_password'), $request->user()->password)) {
            return $response
                ->setError()
                ->setCode(400)
                ->setMessage(__('Current password is not valid!'));
        }

        $request->user()->update([
            'password' => Hash::make($request->input('password')),
        ]);

        return $response->setMessage(__('Update password successfully!'));
    }
}

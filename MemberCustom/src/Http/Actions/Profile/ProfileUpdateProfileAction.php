<?php

namespace CSlant\Blog\MemberCustom\Http\Actions\Profile;

use CSlant\Blog\Api\OpenApi\Schemas\Models\MemberSchema;
use CSlant\Blog\Core\Http\Responses\Base\BaseHttpResponse;
use CSlant\Blog\MemberCustom\Http\Requests\Profile\ProfileUpdateProfileRequest;
use Exception;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Put;
use OpenApi\Attributes\RequestBody;
use OpenApi\Attributes\Response;

class ProfileUpdateProfileAction
{
    /**
     * ProfileController::updateProfile
     *
     * @param  ProfileUpdateProfileRequest  $request
     * @param  BaseHttpResponse  $response
     *
     * @return BaseHttpResponse
     */
    #[
        Put(
            path: '/auth/me',
            operationId: 'updateProfile',
            description: 'Update the authenticated user\'s profile information
            
            This API will allow the user to update their profile information, including first name, last name, phone number
            
            And other optional fields. The user must be authenticated to access this endpoint.
            
            Apply with --MemberCustom-- module.',
            summary: 'Update user profile',
            security: [
                [
                    'sanctum' => [
                        'auth.update.profile',
                    ],
                ],
            ],
            requestBody: new RequestBody(
                required: true,
                content: new JsonContent(
                    properties: [
                        new Property(
                            property: 'first_name',
                            type: 'string'
                        ),
                        new Property(
                            property: 'last_name',
                            type: 'string'
                        ),
                        new Property(
                            property: 'phone',
                            type: 'string'
                        ),
                        new Property(
                            property: 'dob',
                            type: 'string',
                            format: 'date'
                        ),
                        new Property(
                            property: 'gender',
                            type: 'string',
                            enum: ['male', 'female', 'other']
                        ),
                        new Property(
                            property: 'description',
                            type: 'string'
                        ),
                        new Property(
                            property: 'email',
                            type: 'string',
                            format: 'email'
                        ),
                    ]
                )
            ),
            tags: ["Profile"],
            responses: [
                new Response(
                    response: 200,
                    description: 'Profile updated successfully',
                    content: new JsonContent(
                        properties: [
                            new Property(
                                property: 'error',
                                type: 'boolean',
                                example: false
                            ),
                            new Property(
                                property: 'data',
                                ref: MemberSchema::class
                            ),
                            new Property(
                                property: 'message',
                                type: 'string',
                                example: 'Update profile successfully!'
                            ),
                        ]
                    )
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\BadRequestResponseSchema::class,
                    response: 400,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\ErrorNotFoundResponseSchema::class,
                    response: 404,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\TooManyRequestsResponseSchema::class,
                    response: 429,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\InternalServerResponseSchema::class,
                    response: 500,
                ),
            ]
        )
    ]
    public function __invoke(ProfileUpdateProfileRequest $request, BaseHttpResponse $response): BaseHttpResponse
    {
        try {
            $data = $request->validated();

            if ($data['name']) {
                $data['first_name'] = $data['name'];
                unset($data['name']);
            }

            $request->user()->update($request->validated());

            return $response
                ->setData($request->user()->toArray())
                ->setMessage(__('Update profile successfully!'));
        } catch (Exception $ex) {
            return $response
                ->setError()
                ->setMessage($ex->getMessage());
        }
    }
}

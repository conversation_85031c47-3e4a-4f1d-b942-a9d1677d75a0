<?php

namespace CSlant\Blog\MemberCustom\Http\Middlewares;

use Botble\Member\Enums\MemberStatusEnum;
use Carbon\Carbon;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CheckMemberStatusMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  Closure  $next
     *
     * @return JsonResponse|mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $member = $request->user();

        if (!$member) {
            return response()->json(['message' => __('User not found.')], 404);
        }

        $status = $member->status;

        if ($status === MemberStatusEnum::PUBLISHED()->getValue()) {
            return $next($request);
        }

        if (str_starts_with($status, 'banned')) {
            if ($status === 'banned') {
                return response()->json(['message' => __('Account is permanently banned.')], 403);
            }

            if (Carbon::now()->greaterThan($member->banned_until)) {
                return $next($request);
            }

            return response()->json(['message' => __('Account is currently banned')], 403);
        }

        return response()->json(['message' => __('Account status does not permit login.')], 403);
    }
}

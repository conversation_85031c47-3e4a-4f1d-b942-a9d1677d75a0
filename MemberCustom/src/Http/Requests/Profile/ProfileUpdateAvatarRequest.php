<?php

namespace CSlant\Blog\MemberCustom\Http\Requests\Profile;

use Botble\Media\Facades\RvMedia;
use CSlant\Blog\Api\Http\Requests\JsonFormRequest;

class ProfileUpdateAvatarRequest extends JsonFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'avatar' => RvMedia::imageValidationRule(),
        ];
    }
}

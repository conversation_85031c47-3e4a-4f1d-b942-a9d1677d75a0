<?php

namespace CSlant\Blog\MemberCustom\Http\Requests\Profile;

use CSlant\Blog\Api\Http\Requests\JsonFormRequest;

class ProfileUpdatePasswordRequest extends JsonFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'password' => 'required|min:6|max:60',
            'current_password' => 'required|string|min:6|max:60',
        ];
    }
}

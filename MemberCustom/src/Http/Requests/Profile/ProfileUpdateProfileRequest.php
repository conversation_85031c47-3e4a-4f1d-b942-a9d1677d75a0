<?php

namespace CSlant\Blog\MemberCustom\Http\Requests\Profile;

use Botble\Api\Facades\ApiHelper;
use Botble\Base\Facades\BaseHelper;
use CSlant\Blog\Api\Http\Requests\JsonFormRequest;
use Illuminate\Validation\Rule;

class ProfileUpdateProfileRequest extends JsonFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $userId = $this->user()->getKey();

        return [
            'first_name' => ['nullable', 'required_without:name', 'string', 'max:120', 'min:2'],
            'last_name' => ['nullable', 'required_without:name', 'string', 'max:120', 'min:2'],
            'name' => ['nullable', 'required_without:first_name', 'string', 'max:120', 'min:2'],
            'phone' => ['nullable', 'string', ...BaseHelper::getPhoneValidationRule(true)],
            'dob' => ['nullable', 'sometimes', 'date_format:' . BaseHelper::getDateFormat(), 'max:20'],
            'gender' => ['nullable', 'string', Rule::in(['male', 'female', 'other'])],
            'description' => ['nullable', 'string', 'max:1000'],
            'email' => [
                'nullable',
                'max:60',
                'min:6',
                'email',
                'unique:' . ApiHelper::getTable() . ',email,' . $userId,
            ],
        ];
    }
}

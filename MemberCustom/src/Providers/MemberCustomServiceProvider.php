<?php

namespace CSlant\Blog\MemberCustom\Providers;

use Botble\Base\Supports\ServiceProvider;
use CSlant\Blog\MemberCustom\Http\Middlewares\CheckMemberStatusMiddleware;
use Illuminate\Routing\Router;

class MemberCustomServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $routePath = __DIR__.'/../../routes/member-api.php';
        if (file_exists($routePath)) {
            $this->loadRoutesFrom($routePath);
        }
        $this->registerMiddlewares();
    }

    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        $configPath = __DIR__.'/../../config/general.php';
        $this->mergeConfigFrom($configPath, 'general');
    }

    /**
     * Register middlewares for the package.
     */
    protected function registerMiddlewares(): void
    {
        /** @var Router $router */
        $router = $this->app->make('router');

        // Register route middleware
        $router->aliasMiddleware('check-member-status', CheckMemberStatusMiddleware::class);
    }
}

<?php

namespace CSlant\Blog\MemberCustom\Services;

use CSlant\Blog\Core\Models\Member;
use CSlant\Blog\MemberCustom\Notifications\ConfirmEmailNotification;

/**
 * Class MemberService
 *
 * @package CSlant\Blog\MemberCustom\Services
 *
 */
class MemberService
{
    /**
     * @param  Member  $member
     *
     * @return void
     */
    public function sendEmailVerification(Member $member): void
    {
        $member->notify(new ConfirmEmailNotification);
    }
}

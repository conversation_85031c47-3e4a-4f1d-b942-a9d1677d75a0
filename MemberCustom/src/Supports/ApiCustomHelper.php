<?php

namespace CSlant\Blog\MemberCustom\Supports;

use Botble\Base\Models\BaseModel;
use CSlant\Blog\Core\Models\Member;
use Illuminate\Database\Eloquent\Model;

class ApiCustomHelper
{
    public function modelName(): string
    {
        return (string)$this->getConfig('model', Member::class);
    }

    public function setModelName(string $modelName): self
    {
        config(['general.provider.model' => $modelName]);

        return $this;
    }

    public function guard(): string|null
    {
        return $this->getConfig('guard');
    }

    public function passwordBroker(): string|null
    {
        return $this->getConfig('password_broker');
    }

    public function getConfig(string $key, $default = null): string|null
    {
        return config('general.provider.' . $key, $default);
    }

    public function setConfig(array $config)
    {
        return config(['general.provider' => $config]);
    }

    public function newModel(): ?Model
    {
        $model = $this->modelName();

        if (!$model || !class_exists($model)) {
            return new BaseModel;
        }

        return new $model;
    }

    public function getTable(): string
    {
        return $this->newModel()->getTable();
    }

    public function enabled(): bool
    {
        return setting('api_enabled', 0) == 1;
    }
}

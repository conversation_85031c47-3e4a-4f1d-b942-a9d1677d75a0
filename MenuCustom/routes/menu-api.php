<?php

use Illuminate\Support\Facades\Route;
use CSlant\Blog\MenuCustom\Http\Actions\Menu\MenuGetAction;

/*
|--------------------------------------------------------------------------
| Blog API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register bot routes for your application. These
| routes are loaded by the RouteServiceProvider, and all of them will
| be assigned to the "api" middleware group. Enjoy building your API!
|
*/

$routePrefix = config('blog-api.defaults.route_prefix');

Route::prefix($routePrefix)->name("$routePrefix.")->middleware('api')->group(function () {
    Route::group(['prefix' => 'menus'], function () {
        Route::get('/', MenuGetAction::class)->name('menus.get');
    });
});


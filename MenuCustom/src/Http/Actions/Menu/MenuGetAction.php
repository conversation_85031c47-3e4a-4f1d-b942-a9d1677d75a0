<?php

namespace CSlant\Blog\MenuCustom\Http\Actions\Menu;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Menu\Models\Menu;
use CSlant\Blog\Core\Http\Actions\Action;
use CSlant\Blog\MenuCustom\Http\Resources\Menu\MenuResource;
use CSlant\Blog\MenuCustom\OpenApi\Schemas\Resources\MenuResourceSchema;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use OpenApi\Attributes\Get;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Response;

class MenuGetAction extends Action
{
    /**
     * @param  Request  $request
     *
     * @return BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse
     */
    #[
        Get(
            path: "/menus",
            operationId: "getMenu",
            description: "Get all the menu information
            
            This API will get records from the database and return the menu information.
            
            Apply with --MenuCustom-- module.
            ",
            summary: "Get menus",
            tags: ["Menu"],
            responses: [
                new Response(
                    response: 200,
                    description: "Menu successfully",
                    content: new JsonContent(
                        properties: [
                            new Property(
                                property: 'error',
                                description: 'Error status',
                                type: 'boolean',
                                default: false
                            ),
                            new Property(
                                property: 'data',
                                ref: MenuResourceSchema::class
                            ),
                            new Property(
                                property: 'message',
                                type: 'string',
                                example: 'Get menus successfully!'
                            ),
                        ]
                    )
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\BadRequestResponseSchema::class,
                    response: 400,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\ErrorNotFoundResponseSchema::class,
                    response: 404,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\TooManyRequestsResponseSchema::class,
                    response: 429,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\InternalServerResponseSchema::class,
                    response: 500,
                ),
            ]
        )
    ]
    public function __invoke(Request $request): BaseHttpResponse|JsonResponse|RedirectResponse|JsonResource
    {
        $menus = Menu::query()->select(['id', 'name', 'slug', 'status'])
            ->wherePublished()
            ->with(['menuNodes', 'locations'])
            ->get();

        return $this
            ->httpResponse()
            ->setData(MenuResource::collection($menus))
            ->toApiResponse();
    }
}

<?php

namespace CSlant\Blog\MenuCustom\Http\Resources\Menu;

use CSlant\Blog\Core\Models\MenuLocation;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin MenuLocation
 */
class MenuLocationResource extends JsonResource
{
    /**
     * @param $request
     *
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        /** @var MenuLocation $this */
        return [
            'menu_id' => $this->menu_id,
            'location' => $this->location,
        ];
    }
}

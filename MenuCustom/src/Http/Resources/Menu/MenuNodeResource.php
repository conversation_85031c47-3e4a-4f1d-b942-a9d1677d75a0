<?php

namespace CSlant\Blog\MenuCustom\Http\Resources\Menu;

use CSlant\Blog\Core\Models\Menu;
use CSlant\Blog\Core\Models\MenuNode;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Menu
 */
class MenuNodeResource extends JsonResource
{
    /**
     * @param $request
     *
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        /** @var MenuNode $this */
        return [
            'url' => $this->url,
            'icon_font' => $this->icon_font,
            'position' => $this->position,
            'title' => $this->title,
            'css_class' => $this->css_class,
            'target' => $this->target,
            'has_child' => $this->has_child,
        ];
    }
}

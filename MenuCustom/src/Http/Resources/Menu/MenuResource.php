<?php

namespace CSlant\Blog\MenuCustom\Http\Resources\Menu;

use CSlant\Blog\Core\Models\Menu;
use CSlant\Blog\Core\Models\Slug;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Menu
 */
class MenuResource extends JsonResource
{
    /**
     * @param $request
     *
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        /** @var Menu $this */
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug instanceof Slug ? $this->slug->key : $this->slug,
            'status' => $this->status,
            'menu_nodes' => MenuNodeResource::collection($this->whenLoaded('menuNodes')),
            'location' => MenuLocationResource::collection($this->whenLoaded('locations')),
        ];
    }
}

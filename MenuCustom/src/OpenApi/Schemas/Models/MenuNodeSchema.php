<?php

namespace CSlant\Blog\MenuCustom\OpenApi\Schemas\Models;

use OpenApi\Attributes\Property;
use OpenApi\Attributes\Schema;

#[Schema(
    schema: "MenuNode",
    required: ["id"],
    properties: [
        new Property(property: "id", type: "integer", uniqueItems: true),
        new Property(property: "menu_id", type: "integer"),
        new Property(property: "parent_id", type: "integer", default: 0),
        new Property(property: "reference_id", type: "integer", nullable: true),
        new Property(property: "reference_type", type: "string", nullable: true),
        new Property(property: "url", type: "string", nullable: true),
        new Property(property: "position", type: "integer", default: 0),
        new Property(property: "title", type: "string", nullable: true),
        new Property(property: "css_class", type: "string", nullable: true),
        new Property(property: "target", type: "string", default: "_self"),
        new Property(property: "has_child", type: "integer", default: 0),
    ],
    type: "object"
)]
class MenuNodeSchema
{
}

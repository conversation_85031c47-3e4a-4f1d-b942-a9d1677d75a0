<?php

namespace CSlant\Blog\MenuCustom\OpenApi\Schemas\Models;

use OpenApi\Attributes\Items;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Schema;

#[Schema(
    schema: "Menu",
    required: ["id"],
    properties: [
        new Property(property: "id", type: "integer", uniqueItems: true),
        new Property(property: "name", description: "Name", type: "string", maxLength: 120, example: "Name"),
        new Property(property: "slug", description: "Slug", type: "string", maxLength: 120, example: "Slug"),
        new Property(property: "status", type: "string", enum: ["publish", "draft", "pending"]),
        new Property(
            property: "menu_nodes",
            type: "array",
            items: new Items(ref: MenuNodeSchema::class)
        ),
        new Property(
            property: "locations",
            type: "array",
            items: new Items(ref: MenuLocationSchema::class)
        ),
    ],
    type: "object"
)]
class MenuSchema
{
}

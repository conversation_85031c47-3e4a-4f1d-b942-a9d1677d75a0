<?php

namespace CSlant\Blog\MenuCustom\Providers;

use Botble\Base\Supports\ServiceProvider;

class MenuCustomServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $routePath = __DIR__.'/../../routes/menu-api.php';
        if (file_exists($routePath)) {
            $this->loadRoutesFrom($routePath);
        }
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        DB::statement("
            CREATE VIEW post_scores AS
            SELECT 
                posts.id,
                posts.name,
                posts.views,
                COUNT(DISTINCT likes.id) AS likes_count,
                COUNT(DISTINCT fob_comments.id) AS comments_count,
                (COUNT(DISTINCT likes.id) * 2 + posts.views + COUNT(DISTINCT fob_comments.id) * 4) AS score
            FROM posts
            LEFT JOIN likes ON likes.model_id = posts.id 
                AND likes.model_type = 'CSlant\\\\Blog\\\\Core\\\\Models\\\\Post'
            LEFT JOIN fob_comments ON fob_comments.reference_id = posts.id 
                AND fob_comments.reference_type = 'CSlant\\\\Blog\\\\Core\\\\Models\\\\Post'
            WHERE posts.status = 'published'
            GROUP BY posts.id, posts.name, posts.views
        ");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down(): void
    {
        DB::statement("DROP VIEW IF EXISTS post_scores");
    }
};

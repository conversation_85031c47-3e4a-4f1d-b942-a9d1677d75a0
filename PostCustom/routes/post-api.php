<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Blog Post Custom Routes
|--------------------------------------------------------------------------
|
| Here is where you can register bot routes for your application. These
| routes are loaded by the RouteServiceProvider, and all of them will
| be assigned to the "api" middleware group. Enjoy building your API!
|
*/

$routePrefix = config('blog-api.defaults.route_prefix');

Route::prefix($routePrefix)->name("$routePrefix.")->middleware(['api'])->group(function () {
    Route::group(['prefix' => 'posts'], function () {
        Route::group(['prefix' => 'custom'], function () {
            Route::get('filters-with-score', \CSlant\Blog\PostCustom\Http\Actions\Post\PostGetCustomFiltersWithScoreAction::class)
                ->name('post.get.custom.filters-with-score');
        });
    });

    Route::group(['prefix' => 'categories'], function () {
        Route::group(['prefix' => 'custom'], function () {
            Route::get('filters-with-post-score', \CSlant\Blog\PostCustom\Http\Actions\Category\CategoryGetFiltersWithPostScoreAction::class)
                ->name('post.get.custom.filters-with-post-score');
            Route::get('{slug}', \CSlant\Blog\PostCustom\Http\Actions\Category\CategoryGetBySlugWithPostCustomAction::class)
                ->name('post.get.custom.category-by-slug');
        });
    });

    Route::group(['prefix' => 'tags'], function () {
        Route::group(['prefix' => 'custom'], function () {
            Route::get('filters-with-post-score', \CSlant\Blog\PostCustom\Http\Actions\Tag\TagGetFiltersWithPostScoreAction::class)
                ->name('post.get.custom.filters-with-post-score');
        });
    });
});


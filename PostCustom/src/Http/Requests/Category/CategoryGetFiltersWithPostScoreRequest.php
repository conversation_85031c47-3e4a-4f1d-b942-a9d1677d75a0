<?php

namespace CSlant\Blog\PostCustom\Http\Requests\Category;

use CSlant\Blog\Api\Http\Requests\JsonFormRequest;

class CategoryGetFiltersWithPostScoreRequest extends JsonFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'slug' => 'nullable|string|max:255',

            'exclude' => 'nullable|array',
            'exclude.*' => 'nullable|integer|exists:posts,id',
            'include' => 'nullable|array',
            'include.*' => 'nullable|integer|exists:posts,id',

            'search' => 'nullable|string|max:255',
            'order_by' => 'nullable|string',
            'order' => 'nullable|string|in:asc,desc,ASC,DESC',
            'page' => 'nullable|numeric',
            'per_page' => 'nullable|numeric|between:1,100',
        ];
    }
}

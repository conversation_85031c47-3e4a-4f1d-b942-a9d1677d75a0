<?php

namespace CSlant\Blog\PostCustom\Http\Resources\Category;

use CSlant\Blog\Api\Http\Resources\Category\CategoryResource;
use CSlant\Blog\Core\Facades\Base\BaseHelper;
use CSlant\Blog\Core\Facades\Base\Media\RvMedia;
use CSlant\Blog\Core\Models\Slug;
use CSlant\Blog\PostCustom\Models\Category;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Category
 */
class CategoryWithPostScoreResource extends JsonResource
{
    /**
     * @param $request
     *
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        /** @var Category $this */
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug instanceof Slug ? $this->slug->key : $this->slug,
            'icon' => $this->icon ? BaseHelper::renderIcon($this->icon) : null,
            'description' => $this->description,
            'children' => CategoryResource::collection($this->children),
            'parent' => CategoryResource::make($this->parent),

            'image' => $this->image ? RvMedia::url($this->image) : null,
            'color' => $this->color,

            'posts_count' => $this->posts_count,
            'total_views' => $this->total_views,
            'total_likes' => $this->total_likes,
            'total_comments' => $this->total_comments,
            'total_score' => $this->total_score,
        ];
    }
}

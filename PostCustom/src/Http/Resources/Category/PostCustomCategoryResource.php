<?php

namespace CSlant\Blog\PostCustom\Http\Resources\Category;

use CSlant\Blog\Core\Models\Slug;
use CSlant\Blog\PostCustom\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Category
 */
class PostCustomCategoryResource extends JsonResource
{
    /**
     * @param  Request  $request
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        /** @var Category $this */
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug instanceof Slug ? $this->slug->key : $this->slug,
            'color' => $this->color,
            // 'url' => $this->url,
            'icon' => $this->icon,
            'description' => $this->description,
        ];
    }
}

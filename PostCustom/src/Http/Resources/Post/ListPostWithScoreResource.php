<?php

namespace CSlant\Blog\PostCustom\Http\Resources\Post;

use CSlant\Blog\Api\Http\Resources\Author\AuthorResource;
use CSlant\Blog\Api\Http\Resources\Tag\TagResource;
use CSlant\Blog\Core\Facades\Base\Media\RvMedia;
use CSlant\Blog\Core\Models\Slug;
use CSlant\Blog\PostCustom\Http\Resources\Category\PostCustomCategoryResource;
use CSlant\Blog\PostCustom\Models\Post;
use CSlant\LaravelLike\Models\Like;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;

/**
 * @mixin Post
 */
class ListPostWithScoreResource extends JsonResource
{
    /**
     * @param  Request  $request
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $userId = Auth::user() ? Auth::user()->id : 0;

        $isLiked = false;
        if ($userId) {
            $isLiked = Like::query()
                ->where('model_id', $this->id)
                ->where('model_type', 'CSlant\\Blog\\Core\\Models\\Post')
                ->where('user_id', $userId)
                ->exists();
        }

        /** @var Post $this */
        return [
            'id' => $this->id,
            'name' => $this->name,
            'score' => $this->postScore->score,
            'slug' => $this->slug instanceof Slug ? $this->slug->key : $this->slug,
            'description' => $this->description,
            'image' => $this->image ? RvMedia::url($this->image) : null,
            'categories' => PostCustomCategoryResource::collection($this->categories),
            'tags' => TagResource::collection($this->tags),
            'author' => AuthorResource::make($this->author),
            'is_liked' => $isLiked,
            'likes_count' => $this->relationLoaded('postScore') ? $this->postScore->likes_count : 0,
            'comments_count' => $this->relationLoaded('postScore') ? $this->postScore->comments_count : 0,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}

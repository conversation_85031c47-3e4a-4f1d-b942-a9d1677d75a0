<?php

namespace CSlant\Blog\PostCustom\Http\Resources\Tag;

use CSlant\Blog\Core\Models\Slug;
use CSlant\Blog\PostCustom\Models\Tag;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin Tag
 */
class TagWithPostScoreResource extends JsonResource
{
    /**
     * @param $request
     *
     * @return array<string, mixed>
     */
    public function toArray($request): array
    {
        /** @var Tag $this */
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug instanceof Slug ? $this->slug->key : $this->slug,
            'description' => $this->description,
            'posts' => $this->relationLoaded('posts') ? $this->posts : null,

            'posts_count' => $this->posts_count ?? 0,
            'total_views' => $this->total_views ?? 0,
            'total_likes' => $this->total_likes ?? 0,
            'total_comments' => $this->total_comments ?? 0,
            'total_score' => $this->total_score ?? 0,
        ];
    }
}

<?php

namespace CSlant\Blog\PostCustom\Models;

use AllowDynamicProperties;
use Carbon\Carbon;
use CSlant\Blog\Core\Models\Category as BaseCategory;
use CSlant\Blog\Core\Models\Slug;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\DB;

/**
 * Class Category
 *
 * @package CSlant\Blog\PostCustom\Models
 *
 * @property int $id
 * @property string $name
 * @property int $parent_id
 * @property string $description
 * @property string $status
 * @property int $author_id
 * @property string $author_type
 * @property string $icon
 * @property int $order
 * @property int $is_featured
 * @property int $is_default
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Slug $slug
 * @property string $url
 * @property string $image
 * @property string $color
 * @property Category $parent
 * @property Category[] $children
 * @property-read PostScore[] $postScores
 * @property-read Post[] $posts
 *
 * @property-read int $posts_count
 * @property-read int $total_views
 * @property-read int $total_likes
 * @property-read int $total_comments
 * @property-read int $total_score
 *
 * @method static Builder|Tag newModelQuery()
 * @method static Builder|Tag whenLoaded($model)
 * @method static Builder|Tag newQuery()
 * @method static Builder|Tag query()
 * @method static Builder|Tag first()
 * @method static Builder|Tag find($id)
 * @method static Builder|Tag with($relations)
 * @method static Builder|Tag whereId($value)
 * @method static Builder|Tag whereIn($column, $values)
 * @method static Builder|Tag where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Tag findOrFail($id)
 * @method static Tag create($data)
 * @method Builder|Tag withPostScore()
 *
 * @mixin BaseCategory
 */
#[AllowDynamicProperties]
class Category extends BaseCategory
{
    public function posts(): BelongsToMany
    {
        return $this->belongsToMany(Post::class, 'post_categories')->with('slugable');
    }

    public function postScores(): BelongsToMany
    {
        return $this->belongsToMany(PostScore::class, 'post_categories', 'category_id', 'post_id');
    }

    /**
     * Scope to get the post-score for the category
     * (e.g., ->withPostScore() to get the post-score for the category)
     *
     * @param  Builder  $query
     *
     * @return Builder
     */
    public function scopeWithPostScore(Builder $query): Builder
    {
        return $query
            ->leftJoin('post_categories', 'categories.id', '=', 'post_categories.category_id')
            ->leftJoin('post_scores', 'post_categories.post_id', '=', 'post_scores.id')
            ->select(
                'categories.id',
                'categories.name',
                'categories.parent_id',
                'categories.description',
                'categories.status',
                'categories.author_id',
                'categories.author_type',
                'categories.icon',
                'categories.order',
                'categories.is_featured',
                'categories.is_default',
                'categories.created_at',
                'categories.updated_at',
                'categories.image',
                'categories.color',
                DB::raw('SUM(post_scores.score) as total_score'),
                DB::raw('SUM(post_scores.views) as total_views'),
                DB::raw('SUM(post_scores.likes_count) as total_likes'),
                DB::raw('SUM(post_scores.comments_count) as total_comments'),
            )
            ->groupBy(
                'categories.id',
                'categories.name',
                'categories.parent_id',
                'categories.description',
                'categories.status',
                'categories.author_id',
                'categories.author_type',
                'categories.icon',
                'categories.order',
                'categories.is_featured',
                'categories.is_default',
                'categories.created_at',
                'categories.updated_at',
                'categories.image',
                'categories.color'
            );
    }
}

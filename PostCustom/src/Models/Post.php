<?php

namespace CSlant\Blog\PostCustom\Models;

use CSlant\Blog\Core\Models\Category;
use CSlant\Blog\Core\Models\Comment;
use CSlant\Blog\Core\Models\Post as BasePost;
use CSlant\Blog\Core\Models\Slug;
use CSlant\Blog\Core\Models\Tag;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;

/**
 * Class Post
 *
 * @package CSlant\Blog\PostCustom\Models
 *
 * @property int $id
 * @property string $name
 * @property string $description
 * @property string $content
 * @property string $status
 * @property int $author_id
 * @property string $author_type
 * @property int $is_featured
 * @property int $views
 * @property string $format_type
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Slug $slug
 * @property string $url
 * @property Tag[] $tags
 * @property Category[] $categories
 * @property Comment[] $comments
 * @property string $image
 * @property string $author
 * @property-read PostScore $postScore
 *
 * @method static Builder|BasePost newModelQuery()
 * @method static Builder|Post newQuery()
 * @method static Builder|Post query()
 * @method static Builder|Post first()
 * @method static Builder|Post find($id)
 * @method static Builder|Post with($relations)
 * @method static Builder|Post whereId($value)
 * @method static Builder|Post whereIn($column, $values)
 * @method static Builder|Post where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Post findOrFail($id)
 * @method static Post create($data)
 *
 */
class Post extends BasePost
{
    /**
     * @return HasOne
     */
    public function postScore(): HasOne
    {
        return $this->hasOne(PostScore::class, 'id', 'id');
    }
}

<?php

namespace CSlant\Blog\PostCustom\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class PostScore
 *
 * The view table for the post-score.
 *
 * @package CSlant\Blog\PostCustom\Models
 *
 * @property-read int $id
 * @property-read string $name
 * @property-read int $views
 * @property-read int $likes_count
 * @property-read int $comments_count
 * @property-read int $score
 * @property-read Post $post
 */
class PostScore extends Model
{
    /**
     * The "booted" method of the model.
     * This is the view table.
     *
     * @return void
     */
    protected $table = 'post_scores';

    protected $primaryKey = 'id';

    /**
     * The "incrementing" property get from a Post model.
     *
     * @var bool
     */
    public $incrementing = false;

    public $timestamps = false;

    protected $guarded = [
        'id',
    ];

    protected $fillable = [
        'id',
        'name',
        'views',
        'likes_count',
        'comments_count',
        'score',
    ];

    /**
     * The "booted" method of the model.
     *
     * @return void
     */
    protected static function booted()
    {
        // Add your booted logic here if needed
    }

    /**
     * @return BelongsTo
     */
    public function post(): BelongsTo
    {
        return $this->belongsTo(Post::class, 'id', 'id');
    }
}

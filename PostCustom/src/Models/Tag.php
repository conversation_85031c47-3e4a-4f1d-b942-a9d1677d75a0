<?php

namespace CSlant\Blog\PostCustom\Models;

use AllowDynamicProperties;
use Carbon\Carbon;
use CSlant\Blog\Core\Models\Slug;
use CSlant\Blog\Core\Models\Tag as BaseTag;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\DB;

/**
 * Class Tag
 *
 * @package CSlant\Blog\PostCustom\Models
 *
 * @property int $id
 * @property string $name
 * @property string $description
 * @property string $status
 * @property int $author_id
 * @property string $author_type
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Slug $slug
 * @property-read PostScore[] $postScores
 * @property-read Post[] $posts
 *
 * @property-read int|string $posts_count
 * @property-read int|string $total_views
 * @property-read int|string $total_likes
 * @property-read int|string $total_comments
 * @property-read int|string $total_score
 *
 * @method static Builder|Tag newModelQuery()
 * @method static Builder|Tag whenLoaded($model)
 * @method static Builder|Tag newQuery()
 * @method static Builder|Tag query()
 * @method static Builder|Tag first()
 * @method static Builder|Tag find($id)
 * @method static Builder|Tag with($relations)
 * @method static Builder|Tag whereId($value)
 * @method static Builder|Tag whereIn($column, $values)
 * @method static Builder|Tag where($column, $operator = null, $value = null, $boolean = 'and')
 * @method static Tag findOrFail($id)
 * @method static Tag create($data)
 * @method Builder|Tag withPostScore()
 *
 * @mixin BaseTag
 */
#[AllowDynamicProperties]
class Tag extends BaseTag
{
    public function posts(): BelongsToMany
    {
        return $this->belongsToMany(Post::class, 'post_tags', 'tag_id', 'post_id');
    }

    public function postScores(): BelongsToMany
    {
        return $this->belongsToMany(PostScore::class, 'post_tags', 'tag_id', 'post_id');
    }

    /**
     * Scope to get the post-score for the tag
     * (e.g., ->withPostScore() to get the post-score for the tag)
     *
     * @param  Builder  $query
     *
     * @return Builder
     */
    public function scopeWithPostScore(Builder $query): Builder
    {
        return $query
            ->leftJoin('post_tags', 'tags.id', '=', 'post_tags.tag_id')
            ->leftJoin('post_scores', 'post_tags.post_id', '=', 'post_scores.id')
            ->select(
                'tags.id',
                'tags.name',
                'tags.description',
                'tags.status',
                'tags.author_id',
                'tags.author_type',
                'tags.created_at',
                'tags.updated_at',
                DB::raw('SUM(post_scores.score) as total_score'),
                DB::raw('SUM(post_scores.views) as total_views'),
                DB::raw('SUM(post_scores.likes_count) as total_likes'),
                DB::raw('SUM(post_scores.comments_count) as total_comments'),
            )
            ->groupBy(
                'tags.id',
                'tags.name',
                'tags.description',
                'tags.status',
                'tags.author_id',
                'tags.author_type',
                'tags.created_at',
                'tags.updated_at'
            );
    }
}

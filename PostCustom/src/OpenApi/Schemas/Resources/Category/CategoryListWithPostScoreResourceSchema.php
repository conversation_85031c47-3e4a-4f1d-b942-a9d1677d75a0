<?php

namespace CSlant\Blog\PostCustom\OpenApi\Schemas\Resources\Category;

use CSlant\Blog\Api\OpenApi\Schemas\Models\CategorySchema;
use OpenApi\Attributes\Items;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Schema;

#[Schema(
    schema: "CategoryListWithPostScoreResource",
    required: ["id", "name", "slug", "description"],
    properties: [
        new Property(property: "id", type: "integer", uniqueItems: true),
        new Property(property: "name", description: "Category name", type: "string", maxLength: 255),
        new Property(property: "slug", description: "Category slug", type: "string", maxLength: 255, uniqueItems: true),
        new Property(property: "description", type: "string", nullable: true),
        new Property(property: "icon", description: "Category icon", type: "string", nullable: true),
        new Property(
            property: "children",
            type: "array",
            items: new Items(ref: CategorySchema::class)
        ),
        new Property(
            property: "parent",
            ref: CategorySchema::class,
            type: "object",
        ),
        new Property(
            property: "image",
            description: "Category image",
            type: "string",
            format: "uri",
            nullable: true,
        ),
        new Property(property: "color", description: "Category color", type: "string", nullable: true),
        new Property(property: "posts_count", description: "Category posts count", type: "integer", nullable: true),
        new Property(property: "total_views", description: "Category total views", type: "integer", nullable: true),
        new Property(property: "total_likes", description: "Category total likes", type: "integer", nullable: true),
        new Property(property: "total_comments", description: "Category total comments", type: "integer", nullable: true),
        new Property(property: "total_score", description: "Category total score", type: "integer", nullable: true),
    ],
    type: "object"
)]
class CategoryListWithPostScoreResourceSchema
{
}

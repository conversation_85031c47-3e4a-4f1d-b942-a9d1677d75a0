<?php

namespace CSlant\Blog\PostCustom\OpenApi\Schemas\Resources\Category;

use OpenApi\Attributes\Property;
use OpenApi\Attributes\Schema;

#[Schema(
    schema: "PostCustomCategoryModelResource",
    required: ["id", "name", "slug", "description"],
    properties: [
        new Property(property: "id", type: "integer", uniqueItems: true),
        new Property(property: "name", description: "Category name", type: "string", maxLength: 255),
        new Property(property: "slug", description: "Category slug", type: "string", maxLength: 255, uniqueItems: true),
        new Property(property: "color", description: "Category color", type: "string", nullable: true),
        new Property(property: "url", description: "Category url", type: "string", maxLength: 255, nullable: true),
        new Property(property: "icon", description: "Category icon", type: "string", nullable: true),
        new Property(property: "description", type: "string", nullable: true),
    ],
    type: "object"
)]
class PostCustomCategoryModelResourceSchema
{
}

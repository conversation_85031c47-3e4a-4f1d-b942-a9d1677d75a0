<?php

namespace CSlant\Blog\PostCustom\OpenApi\Schemas\Resources\Post;

use CSlant\Blog\Api\OpenApi\Schemas\Resources\Author\AuthorModelResourceSchema;
use CSlant\Blog\Api\OpenApi\Schemas\Resources\Tag\TagModelResourceSchema;
use CSlant\Blog\PostCustom\OpenApi\Schemas\Resources\Category\PostCustomCategoryModelResourceSchema;
use OpenApi\Attributes\Items;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Schema;

#[Schema(
    schema: "PostListWithScoreResource",
    required: ["id", "name", "slug", "description", "categories", "tags", "author"],
    properties: [
        new Property(property: "id", type: "integer", uniqueItems: true),
        new Property(property: "name", description: "Post name", type: "string", maxLength: 255),
        new Property(property: "score", description: "Post score", type: "integer"),
        new Property(property: "slug", description: "Post slug", type: "string", maxLength: 255, uniqueItems: true),
        new Property(property: "description", description: "Post description", type: "string"),
        new Property(property: "image", description: "Post image", type: "string", nullable: true),
        new Property(
            property: "categories",
            type: "array",
            items: new Items(ref: PostCustomCategoryModelResourceSchema::class)
        ),
        new Property(
            property: "tags",
            type: "array",
            items: new Items(ref: TagModelResourceSchema::class)
        ),
        new Property(
            property: "author",
            ref: AuthorModelResourceSchema::class,
            type: "object",
        ),
        new Property(property: "likes_count", description: "Likes count", type: "string", nullable: true),
        new Property(property: "comments_count", description: "Comments count", type: "string", nullable: true),
    ],
    type: "object"
)]
class PostListWithScoreResourceSchema
{
}

<?php

namespace CSlant\Blog\PostCustom\OpenApi\Schemas\Resources\Tag;

use OpenApi\Attributes\Property;
use OpenApi\Attributes\Schema;

#[Schema(
    schema: "TagWithPostScoreModelResource",
    required: ["id", "name", "slug", "description"],
    properties: [
        new Property(property: "id", type: "integer", uniqueItems: true),
        new Property(property: "name", description: "Tag name", type: "string", maxLength: 120),
        new Property(property: "slug", description: "Tag Slug", type: "string", maxLength: 255, uniqueItems: true),
        new Property(property: "description", description: "Tag description", type: "string", maxLength: 400, nullable: true),
        new Property(property: "posts_count", description: "Author posts_count", type: "integer", nullable: true),
        new Property(property: "total_views", description: "Tag total_views", type: "integer", nullable: true),
        new Property(property: "total_likes", description: "Tag total_likes", type: "integer", nullable: true),
        new Property(property: "total_comments", description: "Tag total_comments", type: "integer", nullable: true),
        new Property(property: "total_score", description: "Tag total_score", type: "integer", nullable: true),
    ],
    type: "object"
)]

class TagWithPostScoreModelResourceSchema
{
}

<?php

namespace CSlant\Blog\PostCustom\Providers;

use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Routing\Router;
use Illuminate\Support\ServiceProvider;

class PostCustomServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @throws BindingResolutionException
     */
    public function boot(): void
    {
        $routePath = __DIR__.'/../../routes/post-api.php';
        if (file_exists($routePath)) {
            $this->loadRoutesFrom($routePath);
        }

        $this->loadMigrationsFrom(__DIR__.'/../../database/migrations');

        $this->registerMiddlewares();
    }

    /**
     * Register middlewares for the package.
     *
     * @throws BindingResolutionException
     */
    protected function registerMiddlewares(): void
    {
        /** @var Router $router */
        $router = $this->app->make('router');
    }
}

<?php

namespace CSlant\Blog\PostCustom\Services;

use CSlant\Blog\Api\Supports\Queries\QueryCategory;
use CSlant\Blog\Core\Http\Responses\Base\BaseHttpResponse;
use CSlant\Blog\PostCustom\Models\Category;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;

/**
 * Class TagCustomService
 *
 * @package CSlant\Blog\Api\Services
 *
 * @method BaseHttpResponse httpResponse()
 */
class CategoryCustomService
{
    /**
     * Get tags by filters.
     *
     * @param  array<string, mixed>  $filters
     *
     * @return LengthAwarePaginator<int, Category>
     */
    public function getFilters(array $filters): LengthAwarePaginator
    {
        $query = Category::query()
            ->withCount('posts')
            ->with(['postScores']);

        $query = QueryCategory::setBaseCustomFilterQuery($query, $filters);
        $query = $query->wherePublished()
            ->withPostScore()
            ->orderBy(
                Arr::get($filters, 'order_by', 'posts_count'),
                Arr::get($filters, 'order', 'desc')
            );

        return $query->paginate((int) $filters['per_page']);
    }
}

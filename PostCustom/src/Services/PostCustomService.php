<?php

namespace CSlant\Blog\PostCustom\Services;

use CSlant\Blog\Api\Supports\Queries\QueryPost;
use CSlant\Blog\PostCustom\Models\Post;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class PostCustomService
{
    public function setBaseCustomFilterQuery($query, array $filters)
    {
        return QueryPost::setBaseCustomFilterQuery($query, $filters);
    }

    /**
     * @param  array<string, mixed>  $filters
     *
     * @return LengthAwarePaginator<int, Post>
     */
    public function getCustomFilters(array $filters): LengthAwarePaginator
    {
        $query = Post::query()->withCount(['comments', 'likes'])->with(['comments', 'likes', 'postScore']);

        $query = $this->setBaseCustomFilterQuery($query, $filters);

        $query = $query->wherePublished();

        if ($filters['order_by'] === 'score') {
            $query = $query
                ->join('post_scores', function ($join) {
                    $join->on('posts.id', '=', 'post_scores.id');
                })
                ->select('posts.*', DB::raw('post_scores.score'))
                ->orderBy('post_scores.score', Arr::get($filters, 'order', 'desc'));
        } else {
            $query = $query->orderBy(
                Arr::get($filters, 'order_by', 'updated_at'),
                Arr::get($filters, 'order', 'desc')
            );
        }

        return $query->paginate((int) $filters['per_page']);
    }
}

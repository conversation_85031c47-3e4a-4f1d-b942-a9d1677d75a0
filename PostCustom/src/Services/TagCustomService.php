<?php

namespace CSlant\Blog\PostCustom\Services;

use CSlant\Blog\Api\Supports\Queries\QueryTag;
use CSlant\Blog\Core\Http\Responses\Base\BaseHttpResponse;
use CSlant\Blog\PostCustom\Models\Tag;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;

/**
 * Class TagCustomService
 *
 * @package CSlant\Blog\Api\Services
 *
 * @method BaseHttpResponse httpResponse()
 */
class TagCustomService
{
    /**
     * Get tags by filters.
     *
     * @param  array<string, mixed>  $filters
     *
     * @return LengthAwarePaginator<int, Tag>
     */
    public function getFilters(array $filters): LengthAwarePaginator
    {
        $query = Tag::query()
            ->withCount('posts')
            ->with(['postScores']);

        $query = QueryTag::setBaseCustomFilterQuery($query, $filters);
        $query = $query->wherePublished();
        $orderBy = Arr::get($filters, 'order_by', 'posts_count');

        if (in_array($orderBy, ['total_score', 'total_views', 'total_likes', 'total_comments'])) {
            $query = $query
                ->withPostScore()
                ->orderBy($orderBy, Arr::get($filters, 'order', 'desc'));
        } else {
            $query = $query->orderBy($orderBy, Arr::get($filters, 'order', 'desc'));
        }

        return $query->paginate((int) $filters['per_page']);
    }
}

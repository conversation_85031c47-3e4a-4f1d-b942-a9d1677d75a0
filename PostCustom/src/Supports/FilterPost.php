<?php

namespace CSlant\Blog\PostCustom\Supports;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\Blog\Supports\FilterPost as BaseFilterPost;

class FilterPost extends BaseFilterPost
{
    public static function setFilters(array $request): array
    {
        if (isset($request['order'])) {
            $request['order'] = strtolower($request['order']);
        }

        $orderBy = $request['order_by'] ?? 'updated_at';

        if ($orderBy == 'total_score') {
            $orderBy = 'score';
        } elseif ($orderBy == 'total_comments') {
            $orderBy = 'comments_count';
        } elseif ($orderBy == 'total_likes') {
            $orderBy = 'likes_count';
        } elseif ($orderBy == 'views_count' || $orderBy == 'total_views') {
            $orderBy = 'views';
        }

        return [
            'page' => $request['page'] ?? 1,
            'per_page' => $request['per_page'] ?? 10,
            'search' => $request['search'] ?? null,
            'author' => $request['author'] ?? null,
            'author_exclude' => $request['author_exclude'] ?? null,
            'exclude' => $request['exclude'] ?? null,
            'include' => $request['include'] ?? null,
            'after' => $request['after'] ?? null,
            'before' => $request['before'] ?? null,
            'order' => isset($request['order']) && in_array($request['order'], ['asc', 'desc']) ? $request['order'] : 'desc',
            'order_by' => $orderBy,
            'status' => BaseStatusEnum::PUBLISHED,
            'categories' => $request['categories'] ?? null,
            'categories_exclude' => $request['categories_exclude'] ?? null,
            'tags' => $request['tags'] ?? null,
            'tags_exclude' => $request['tags_exclude'] ?? null,
            'featured' => $request['featured'] ?? null,
        ];
    }
}

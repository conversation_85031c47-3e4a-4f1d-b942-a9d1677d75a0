# Do not merge all code in the develop branch to the main branch.

Develop branch........

### 💻 Start in Mac with arm64

To run as amd64. You need to set the default platform to `linux/amd64`:

```shell
export DOCKER_DEFAULT_PLATFORM=linux/amd64
```

### Run the runner

Then, you can just run the following command to start the runner.

```bash
bash runner.sh b
```

### Usage

```bash
docker compose run --rm -w /var/dev node22 ash
```


<?php

namespace CSlant\Blog\ThemeCustom\Hooks;

use Bo<PERSON>ble\Dashboard\Supports\DashboardWidgetInstance;
use CSlant\Blog\Core\Models\Category;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class CategoryTotalWidgetHook
{
    public static function addCategoryTotalWidget(array $widgets, Collection $widgetSettings): array
    {
        if (!Auth::guard()->user()->hasPermission('categories.index')) {
            return $widgets;
        }

        $posts = fn () => Category::query()->count();

        return (new DashboardWidgetInstance)
            ->setType('stats')
            ->setPermission('categories.index')
            ->setTitle(trans('Categories'))
            ->setKey('widget_total_categories')
            ->setIcon('ti ti-folder')
            ->setColor('info')
            ->setStatsTotal($posts)
            ->setRoute(route('categories.index'))
            ->setColumn('col-12 col-md-6 col-lg-3')
            ->init($widgets, $widgetSettings);
    }
}

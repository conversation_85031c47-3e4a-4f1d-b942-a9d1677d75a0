<?php

namespace CSlant\Blog\ThemeCustom\Hooks;

use Bo<PERSON>ble\Dashboard\Supports\DashboardWidgetInstance;
use CSlant\Blog\Core\Models\Member;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class MemberTotalWidgetHook
{
    public static function addMemberTotalWidget(array $widgets, Collection $widgetSettings): array
    {
        if (!Auth::guard()->user()->hasPermission('member.index')) {
            return $widgets;
        }

        $posts = fn () => Member::query()->count();

        return (new DashboardWidgetInstance)
            ->setType('stats')
            ->setPermission('member.index')
            ->setTitle(trans('Members'))
            ->setKey('widget_total_members')
            ->setIcon('ti ti-user')
            ->setColor('info')
            ->setStatsTotal($posts)
            ->setRoute(route('member.index'))
            ->setColumn('col-12 col-md-6 col-lg-3')
            ->init($widgets, $widgetSettings);
    }
}

<?php

namespace CSlant\Blog\ThemeCustom\Hooks;

use Bo<PERSON>ble\Dashboard\Supports\DashboardWidgetInstance;
use CSlant\Blog\Core\Models\Post;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class PostTotalWidgetHook
{
    public static function addPostTotalWidget(array $widgets, Collection $widgetSettings): array
    {
        if (!Auth::guard()->user()->hasPermission('posts.index')) {
            return $widgets;
        }

        $posts = fn () => Post::query()->count();

        return (new DashboardWidgetInstance)
            ->setType('stats')
            ->setPermission('posts.index')
            ->setTitle(trans('Posts'))
            ->setKey('widget_total_posts')
            ->setIcon('ti ti-file-text')
            ->setColor('primary')
            ->setStatsTotal($posts)
            ->setRoute(route('posts.index'))
            ->setColumn('col-12 col-md-6 col-lg-3')
            ->init($widgets, $widgetSettings);
    }
}

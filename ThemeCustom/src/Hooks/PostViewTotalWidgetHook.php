<?php

namespace CSlant\Blog\ThemeCustom\Hooks;

use Bo<PERSON>ble\Dashboard\Supports\DashboardWidgetInstance;
use CSlant\Blog\Core\Models\Post;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class PostViewTotalWidgetHook
{
    public static function addViewStatsWidget(array $widgets, Collection $widgetSettings): array
    {
        if (!Auth::guard()->user()->hasPermission('posts.index')) {
            return $widgets;
        }

        $views = fn () => Post::query()->sum('views');

        return (new DashboardWidgetInstance)
            ->setType('stats')
            ->setPermission('posts.index')
            ->setTitle(trans('Views'))
            ->setKey('widget_total_post_views')
            ->setIcon('ti ti-eye')
            ->setColor('danger')
            ->setStatsTotal($views)
            ->setRoute(route('posts.index'))
            ->setColumn('col-12 col-md-6 col-lg-3')
            ->init($widgets, $widgetSettings);
    }
}

<?php

namespace CSlant\Blog\ThemeCustom\Providers;

use Bo<PERSON><PERSON>\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Dashboard\Events\RenderingDashboardWidgets;
use CSlant\Blog\ThemeCustom\Hooks\CategoryTotalWidgetHook;
use CSlant\Blog\ThemeCustom\Hooks\MemberTotalWidgetHook;
use CSlant\Blog\ThemeCustom\Hooks\PostTotalWidgetHook;
use CSlant\Blog\ThemeCustom\Hooks\PostViewTotalWidgetHook;

class ThemeCustomServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $routePath = __DIR__.'/../../routes/public.php';
        if (file_exists($routePath)) {
            $this->loadRoutesFrom($routePath);
        }

        $this->middlewareOverride();

        $this->addAdminDashboardWidgets();
    }

    /**
     * Override blade views of the package.
     */
    public function middlewareOverride(): void
    {
        $this->app->bind(
            \Botble\Base\Http\Middleware\EnsureLicenseHasBeenActivated::class,
            \CSlant\Blog\ThemeCustom\Http\Middlewares\EnsureLicenseHasBeenActivated::class
        );
    }

    public function addAdminDashboardWidgets(): void
    {
        // Register the dashboard widgets
        $this->app['events']->listen(RenderingDashboardWidgets::class, function (): void {
            add_filter(DASHBOARD_FILTER_ADMIN_LIST, [PostViewTotalWidgetHook::class, 'addViewStatsWidget'], 12, 2);
            add_filter(DASHBOARD_FILTER_ADMIN_LIST, [PostTotalWidgetHook::class, 'addPostTotalWidget'], 12, 2);
            add_filter(DASHBOARD_FILTER_ADMIN_LIST, [CategoryTotalWidgetHook::class, 'addCategoryTotalWidget'], 12, 2);
            add_filter(DASHBOARD_FILTER_ADMIN_LIST, [MemberTotalWidgetHook::class, 'addMemberTotalWidget'], 12, 2);
        });
    }
}

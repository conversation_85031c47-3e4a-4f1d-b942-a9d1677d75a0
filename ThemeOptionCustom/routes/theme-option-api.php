<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Blog API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register bot routes for your application. These
| routes are loaded by the RouteServiceProvider, and all of them will
| be assigned to the "api" middleware group. Enjoy building your API!
|
*/

$routePrefix = config('blog-api.defaults.route_prefix');

Route::prefix($routePrefix)->name("$routePrefix.")->middleware('api')->group(function () {
    Route::group(['prefix' => 'themes'], function () {
        Route::get( 'all', \CSlant\Blog\ThemeOptionCustom\Http\Actions\ThemeOption\ThemeOptionGetAllAction::class)->name('themes.all');
        Route::get( 'options', \CSlant\Blog\ThemeOptionCustom\Http\Actions\ThemeOption\ThemeOptionGetAction::class)->name('themes.options');
    });
});


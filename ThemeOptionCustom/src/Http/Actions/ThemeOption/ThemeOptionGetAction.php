<?php

namespace CSlant\Blog\ThemeOptionCustom\Http\Actions\ThemeOption;

use Bo<PERSON>ble\Base\Http\Responses\BaseHttpResponse;
use Botble\Theme\Events\RenderingThemeOptionSettings;
use Botble\Theme\Facades\ThemeOption;
use CSlant\Blog\Core\Facades\Base\Media\RvMedia;
use CSlant\Blog\Core\Http\Actions\Action;
use CSlant\Blog\Core\Models\Page;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ThemeOptionGetAction extends Action
{
    /**
     * @param  Request  $request
     *
     * @return BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse
     */
    public function __invoke(Request $request): BaseHttpResponse|JsonResponse|RedirectResponse|JsonResource
    {
        RenderingThemeOptionSettings::dispatch();

        //$sections = ThemeOption::constructSections();

        $logo = [
            'logo_height' => theme_option('logo_height') ?? 'auto',
            'favicon' => RvMedia::getImageUrl(theme_option('favicon'), 'thumb', false, RvMedia::getDefaultImage()) ?? '',
            'logo' => RvMedia::getImageUrl(theme_option('logo'), 'thumb', false, RvMedia::getDefaultImage()) ?? '',
        ];

        $blogPageId = theme_option('blog_page_id', setting('blog_page_id'));

        $blog = [
            'blog_page' => Page::query()->find($blogPageId) ?? null,
            'number_of_posts_in_a_category' => theme_option('number_of_posts_in_a_category', 12),
            'number_of_posts_in_a_tag' => theme_option('number_of_posts_in_a_tag', 12),
        ];

        return $this
            ->httpResponse()
            ->setData([
                'general' => [
                    'primary_color' => theme_option('primary_color'),
                    'primary_color_hover' => theme_option('primary_color_hover'),
                    'site_description' => theme_option('site_description'),
                    'address' => theme_option('address'),
                    'website' => theme_option('website'),
                    'contact_email' => theme_option('contact_email'),

                    'site_title' => theme_option('site_title'),
                    'site_title_separator' => theme_option('site_title_separator'),
                    'seo_title' => theme_option('seo_title'),
                    'seo_description' => theme_option('seo_description'),
                    'seo_index' => (bool) theme_option('seo_index'),
                    'seo_og_image' => RvMedia::getImageUrl(theme_option('seo_og_image'), 'thumb', false, RvMedia::getDefaultImage()) ?? '',

                    'term_and_privacy_policy_url' => theme_option('term_and_privacy_policy_url'),
                    'copyright' => theme_option('copyright'),

                    'date_format' => theme_option('date_format'),
                ],
                'theme_breadcrumb_enabled' => theme_option('theme_breadcrumb_enabled', true),
                'logo' => $logo,
                'blog' => $blog,
                'typography' => [
                    'tp_primary_font' => theme_option('tp_primary_font'),
                    'tp_h1_size' => theme_option('tp_h1_size'),
                    'tp_h2_size' => theme_option('tp_h2_size'),
                    'tp_h3_size' => theme_option('tp_h3_size'),
                    'tp_h4_size' => theme_option('tp_h4_size'),
                    'tp_h5_size' => theme_option('tp_h5_size'),
                    'tp_h6_size' => theme_option('tp_h6_size'),
                    'tp_body_size' => theme_option('tp_body_size'),
                ],
                'social_links' => json_decode(theme_option('social_links'), true),
                'cookie_consent' => [
                    'cookie_consent_enable' => theme_option('cookie_consent_enable', false),
                    'cookie_consent_message' => theme_option('cookie_consent_message'),
                ],
            ])
            ->toApiResponse();
    }
}

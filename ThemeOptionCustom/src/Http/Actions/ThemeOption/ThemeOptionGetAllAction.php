<?php

namespace CSlant\Blog\ThemeOptionCustom\Http\Actions\ThemeOption;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Theme\Events\RenderingThemeOptionSettings;
use CSlant\Blog\Core\Http\Actions\Action;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ThemeOptionGetAllAction extends Action
{
    /**
     * @param  Request  $request
     *
     * @return BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse
     */
    public function __invoke(Request $request): BaseHttpResponse|JsonResponse|RedirectResponse|JsonResource
    {
        RenderingThemeOptionSettings::dispatch();

        return $this
            ->httpResponse()
            ->setData([
                'theme_' => theme_option(''),
            ])
            ->toApiResponse();
    }
}

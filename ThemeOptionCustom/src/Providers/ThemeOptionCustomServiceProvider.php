<?php

namespace CSlant\Blog\ThemeOptionCustom\Providers;

use Botble\Base\Supports\ServiceProvider;

class ThemeOptionCustomServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $routePath = __DIR__.'/../../routes/theme-option-api.php';
        if (file_exists($routePath)) {
            $this->loadRoutesFrom($routePath);
        }
    }
}

<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| User API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for user authentication and
| management. These routes are loaded by the UserCustomServiceProvider.
|
*/

$routePrefix = config('blog-api.defaults.route_prefix');

Route::prefix($routePrefix)->name("$routePrefix.")->middleware('api')->group(function () {
    // Add a new route for getting admin user
    Route::get('/auth/admin-user', \CSlant\Blog\UserCustom\Http\Actions\Auth\AuthAdminUserAction::class)->name('auth.admin-user');
});

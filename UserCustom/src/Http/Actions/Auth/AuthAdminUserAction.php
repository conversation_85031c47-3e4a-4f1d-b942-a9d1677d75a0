<?php

namespace CSlant\Blog\UserCustom\Http\Actions\Auth;

use Botble\ACL\Models\User;
use CSlant\Blog\Core\Http\Responses\Base\BaseHttpResponse;
use CSlant\Blog\UserCustom\Http\Resources\AdminUserResource;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use OpenApi\Attributes\Get;
use OpenApi\Attributes\JsonContent;
use OpenApi\Attributes\Property;
use OpenApi\Attributes\Response;

/**
 * Class AuthAdminUserAction
 *
 * Retrieves the authenticated admin user's information based on session token.
 */
class AuthAdminUserAction
{
    /**
     * Get admin user information if logged in
     *
     * @param  Request  $request
     * @param  BaseHttpResponse  $response
     *
     * @return BaseHttpResponse
     */
    #[
        Get(
            path: "/auth/admin-user",
            operationId: "getAdminUser",
            description: "Get the authenticated admin user's information if logged in
            
            This API will check if there's an admin user logged in and return their information.
            If no admin user is logged in, it will return null data.
            
            Apply with --UserCustom-- module.",
            summary: "Get admin user information",
            responses: [
                new Response(
                    response: 200,
                    description: "Admin user information retrieved successfully",
                    content: new JsonContent(
                        properties: [
                            new Property(
                                property: 'error',
                                description: 'Error status',
                                type: 'boolean',
                                default: false
                            ),
                            new Property(
                                property: "data",
                                description: "Admin user data or null if not logged in",
                                type: "object",
                                nullable: true
                            ),
                        ]
                    )
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\BadRequestResponseSchema::class,
                    response: 400,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\ErrorNotFoundResponseSchema::class,
                    response: 404,
                ),
                new Response(
                    ref: \CSlant\Blog\Api\OpenApi\Responses\Errors\InternalServerResponseSchema::class,
                    response: 500,
                ),
            ]
        )
    ]
    public function __invoke(Request $request, BaseHttpResponse $response): BaseHttpResponse
    {
        $sessionCookieName = config('user-custom.cms_session_cookie_key_name');

        $sessionId = $request->cookie($sessionCookieName);

        if (!$sessionId) {
            return $response->setData(null);
        }

        // Set the session ID to access admin session data
        Session::setId($sessionId);
        Session::start();

        // Get token from session (Botble uses _token for session token)
        $token = Session::get('_token');
    dd($token);
        Session::save();
        Session::flush();

        if (!$token) {
            return $response->setData(null);
        }

        try {
            $tokenRecord = DB::table('personal_access_tokens')
                ->where('token', hash('sha256', $token))
                ->first();

            if ($tokenRecord) {
                $user = User::query()->find($tokenRecord->tokenable_id);
                if ($user) {
                    return $response->setData(new AdminUserResource($user));
                }
            }

            $user = User::query()->where('remember_token', $token)->first();

            if ($user) {
                return $response->setData(new AdminUserResource($user));
            }

            $user = User::query()->orderBy('updated_at', 'desc')->first();

            if ($user) {
                return $response->setData(new AdminUserResource($user));
            }

            return $response->setData(null);
        } catch (\Exception $e) {
            Log::error('Error finding user by token: '.$e->getMessage());

            return $response
                ->setError()
                ->setMessage('Error finding user by token')
                ->setCode(500)
                ->setData(null);
        }
    }
}

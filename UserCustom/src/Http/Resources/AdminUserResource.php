<?php

namespace CSlant\Blog\UserCustom\Http\Resources;

use CSlant\Blog\Core\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdminUserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     *
     * @return array
     */
    public function toArray(Request $request): array
    {
        /** @var User $this */
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'name' => $this->name,
            'email' => $this->email,
            'avatar' => $this->avatar_url,
            'super_user' => $this->super_user,
            'roles' => $this->roles->pluck('name')->toArray(),
            // 'permissions' => $this->permissions,
            // 'last_login' => $this->last_login ? $this->last_login->format('Y-m-d H:i:s') : null,
        ];
    }
}

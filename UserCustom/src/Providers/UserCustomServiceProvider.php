<?php

namespace CSlant\Blog\UserCustom\Providers;

use Botble\Base\Supports\ServiceProvider;

class UserCustomServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $routePath = __DIR__.'/../../routes/user-api.php';
        if (file_exists($routePath)) {
            $this->loadRoutesFrom($routePath);
        }
    }

    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        $configPath = __DIR__.'/../../config/user-custom.php';
        $this->mergeConfigFrom($configPath, 'user-custom');
    }
}

<?php

namespace CSlant\Blog\WidgetCustom\Http\Actions\Widget;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Widget\Models\Widget;
use CSlant\Blog\Core\Http\Actions\Action;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WidgetGetAction extends Action
{
    /**
     * @param  Request  $request
     *
     * @return BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse
     */
    public function __invoke(Request $request): BaseHttpResponse|JsonResponse|RedirectResponse|JsonResource
    {
        $widgets = Widget::query()
            ->select('position', 'data')
            ->where('sidebar_id', config('general.sidebar_id'))
            ->where('widget_id', config('general.widget_id'))
            ->orderBy('position')
            ->get();

        $sectionHome = config('general.section_home_id');
        $data = [];
        foreach ($widgets as $key => $widget) {
            $data[$key]['name'] = $widget->data['name'];
            $data[$key]['widget'] = !empty($sectionHome[$widget->data['section_id']]) ? $sectionHome[$widget->data['section_id']] : $widget->data['section_id'];
            $data[$key]['position'] = $widget->position;
            $data[$key]['status'] = $widget->data['status'];
        }

        return $this
            ->httpResponse()
            ->setData($data)
            ->toApiResponse();
    }
}

<?php

namespace CSlant\Blog\WidgetCustom\Providers;

use Botble\Base\Supports\ServiceProvider;

class WidgetCustomServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     */
    public function boot(): void
    {
        $routePath = __DIR__.'/../../routes/widget-api.php';
        if (file_exists($routePath)) {
            $this->loadRoutesFrom($routePath);
        }
    }

    /**
     * Register services.
     *
     * @return void
     */
    public function register(): void
    {
        $configPath = __DIR__.'/../../config/general.php';
        $this->mergeConfigFrom($configPath, 'general');
    }
}

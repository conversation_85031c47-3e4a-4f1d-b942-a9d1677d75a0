# blog-admin

```
 ██████╗███████╗██╗      █████╗ ███╗   ██╗████████╗    ██████╗ ██╗      ██████╗  ██████╗ 
██╔════╝██╔════╝██║     ██╔══██╗████╗  ██║╚══██╔══╝    ██╔══██╗██║     ██╔═══██╗██╔════╝ 
██║     ███████╗██║     ███████║██╔██╗ ██║   ██║       ██████╔╝██║     ██║   ██║██║  ███╗
██║     ╚════██║██║     ██╔══██║██║╚██╗██║   ██║       ██╔══██╗██║     ██║   ██║██║   ██║
╚██████╗███████║███████╗██║  ██║██║ ╚████║   ██║       ██████╔╝███████╗╚██████╔╝╚██████╔╝
 ╚═════╝╚══════╝╚══════╝╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝       ╚═════╝ ╚══════╝ ╚═════╝  ╚═════╝ 
```

- Admin URL: https://blog-admin.cslant.com/start24
- API URL: https://blog-api.cslant.com/cs-api

##  Commit template

If you want to use a commit template, you can use the following command in the root directory of this project:

```bash
git config --local commit.template .github/commit_message.txt
```

### Update env

```bash
cp .env.example .env
```

### Composer install

```bash
composer install
php artisan key:generate
```

### API Installation

```bash
php artisan install:api
```

### Migrate database

```bash
php artisan migrate
```

### Seed database

```bash
php artisan db:seed
```

### Storage link

```bash
php artisan storage:link
```

### Run the server

```bash
php artisan serve
```

---

## Development

### Update API docs

Public provider:

```bash
php artisan vendor:publish --provider "L5Swagger\L5SwaggerServiceProvider" --tag=config
```

If you have changed the API docs, you need to update the docs by running:

```bash
php artisan l5-swagger:generate
```

The command will generate or update the API docs in the `storage/api-docs` directory.

### See the API docs

After updating the API docs, you can see the API docs by visiting the following URL:

```
https://blog-api.cslant.com/cs-api/docs/ui
```

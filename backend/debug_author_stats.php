<?php

// Debug script để kiểm tra author stats
echo "=== DEBUG AUTHOR STATS ===\n\n";

try {
    // Test 1: Check if we can connect to database
    echo "1. Testing database connection...\n";
    $pdo = new PDO('mysql:host=localhost;dbname=your_db', 'user', 'pass');
    echo "✓ Database connected\n\n";
    
    // Test 2: Check posts table
    echo "2. Testing posts table...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM posts WHERE author_type = 'Botble\\\\ACL\\\\Models\\\\User'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ Found {$result['count']} posts with User authors\n\n";
    
    // Test 3: Check users table
    echo "3. Testing users table...\n";
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $result = $stmt->fetch(PDO::<PERSON>ETCH_ASSOC);
    echo "✓ Found {$result['count']} users\n\n";
    
    // Test 4: Check basic author stats query
    echo "4. Testing basic author stats query...\n";
    $sql = "
        SELECT 
            users.id,
            users.first_name,
            users.last_name, 
            users.email,
            COUNT(posts.id) as total_posts,
            COALESCE(SUM(posts.views), 0) as total_views
        FROM posts
        JOIN users ON posts.author_id = users.id
        WHERE posts.author_type = 'Botble\\\\ACL\\\\Models\\\\User'
        GROUP BY users.id, users.first_name, users.last_name, users.email
        ORDER BY total_views DESC
        LIMIT 5
    ";
    
    $stmt = $pdo->query($sql);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (count($results) > 0) {
        echo "✓ Query successful! Found " . count($results) . " authors:\n";
        foreach ($results as $author) {
            $name = trim($author['first_name'] . ' ' . $author['last_name']);
            echo "  - {$name}: {$author['total_posts']} posts, {$author['total_views']} views\n";
        }
    } else {
        echo "✗ No authors found!\n";
    }
    echo "\n";
    
    // Test 5: Check post_scores view
    echo "5. Testing post_scores view...\n";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM post_scores LIMIT 1");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "✓ post_scores view exists with {$result['count']} records\n";
        
        // Test enhanced query with post_scores
        echo "6. Testing enhanced query with post_scores...\n";
        $sql = "
            SELECT 
                posts.author_id,
                SUM(post_scores.comments) as total_comments,
                SUM(post_scores.score) as total_score
            FROM post_scores
            JOIN posts ON post_scores.id = posts.id
            WHERE posts.author_type = 'Botble\\\\ACL\\\\Models\\\\User'
            GROUP BY posts.author_id
            LIMIT 5
        ";
        
        $stmt = $pdo->query($sql);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($results) > 0) {
            echo "✓ Enhanced query successful! Sample data:\n";
            foreach ($results as $stat) {
                echo "  - Author {$stat['author_id']}: {$stat['total_comments']} comments, {$stat['total_score']} score\n";
            }
        } else {
            echo "✗ No enhanced data found!\n";
        }
        
    } catch (Exception $e) {
        echo "⚠ post_scores view not available: " . $e->getMessage() . "\n";
        echo "Will use fallback to posts table only\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}

echo "\n=== DEBUG COMPLETE ===\n";

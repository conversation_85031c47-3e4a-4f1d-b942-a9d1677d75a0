<?php

namespace CSlant\Blog\Custom\Http\Actions\Footer;

use Botble\Base\Http\Responses\BaseHttpResponse;
use Bo<PERSON>ble\Widget\Models\Widget;
use CSlant\Blog\Core\Http\Actions\Action;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FooterGetAction extends Action
{
    /**
     * @param  Request  $request
     *
     * @return BaseHttpResponse|JsonResource|JsonResponse|RedirectResponse
     */
    public function __invoke(Request $request): BaseHttpResponse|JsonResponse|RedirectResponse|JsonResource
    {
        $widgets = Widget::query()
            ->select('position', 'data')
            ->where('sidebar_id', 'footer')
            ->where('widget_id', 'FootersWidget')
            ->orderBy('position')
            ->get();

        $data = [];
        if (count($widgets) > 0) {
            foreach ($widgets as $key => $widget) {
                $data[$key]['name'] = $widget->data['name'];
                $data[$key]['position'] = $widget->position;
                $data[$key]['menu_id'] = $widget->data['menu_id'];
            }
        }
        return $this
            ->httpResponse()
            ->setData($data)
            ->toApiResponse();
    }
}

(()=>{var t={6080:()=>{[].slice.call(document.querySelectorAll('[data-bs-toggle="switch-icon"]')).map((function(t){t.addEventListener("click",(e=>{e.stopPropagation(),t.classList.toggle("active")}))}))},7330:()=>{var t="tablerTheme",e=new Proxy(new URLSearchParams(window.location.search),{get:function(t,e){return t.get(e)}});if(e.theme)localStorage.setItem(t,e.theme),selectedTheme=e.theme;else{var s=localStorage.getItem(t);selectedTheme=s||"light"}"dark"===selectedTheme?document.body.setAttribute("data-bs-theme",selectedTheme):document.body.removeAttribute("data-bs-theme")},4865:function(t,e,s){var i,n;
/* NProgress, (c) 2013, 2014 Rico Sta. Cruz - http://ricostacruz.com/nprogress
 * @license MIT */i=function(){var t,e,s={version:"0.2.0"},i=s.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function n(t,e,s){return t<e?e:t>s?s:t}function r(t){return 100*(-1+t)}function o(t,e,s){var n;return(n="translate3d"===i.positionUsing?{transform:"translate3d("+r(t)+"%,0,0)"}:"translate"===i.positionUsing?{transform:"translate("+r(t)+"%,0)"}:{"margin-left":r(t)+"%"}).transition="all "+e+"ms "+s,n}s.configure=function(t){var e,s;for(e in t)void 0!==(s=t[e])&&t.hasOwnProperty(e)&&(i[e]=s);return this},s.status=null,s.set=function(t){var e=s.isStarted();t=n(t,i.minimum,1),s.status=1===t?null:t;var r=s.render(!e),u=r.querySelector(i.barSelector),c=i.speed,h=i.easing;return r.offsetWidth,a((function(e){""===i.positionUsing&&(i.positionUsing=s.getPositioningCSS()),l(u,o(t,c,h)),1===t?(l(r,{transition:"none",opacity:1}),r.offsetWidth,setTimeout((function(){l(r,{transition:"all "+c+"ms linear",opacity:0}),setTimeout((function(){s.remove(),e()}),c)}),c)):setTimeout(e,c)})),this},s.isStarted=function(){return"number"==typeof s.status},s.start=function(){s.status||s.set(0);var t=function(){setTimeout((function(){s.status&&(s.trickle(),t())}),i.trickleSpeed)};return i.trickle&&t(),this},s.done=function(t){return t||s.status?s.inc(.3+.5*Math.random()).set(1):this},s.inc=function(t){var e=s.status;return e?("number"!=typeof t&&(t=(1-e)*n(Math.random()*e,.1,.95)),e=n(e+t,0,.994),s.set(e)):s.start()},s.trickle=function(){return s.inc(Math.random()*i.trickleRate)},t=0,e=0,s.promise=function(i){return i&&"resolved"!==i.state()?(0===e&&s.start(),t++,e++,i.always((function(){0==--e?(t=0,s.done()):s.set((t-e)/t)})),this):this},s.render=function(t){if(s.isRendered())return document.getElementById("nprogress");c(document.documentElement,"nprogress-busy");var e=document.createElement("div");e.id="nprogress",e.innerHTML=i.template;var n,o=e.querySelector(i.barSelector),a=t?"-100":r(s.status||0),u=document.querySelector(i.parent);return l(o,{transition:"all 0 linear",transform:"translate3d("+a+"%,0,0)"}),i.showSpinner||(n=e.querySelector(i.spinnerSelector))&&p(n),u!=document.body&&c(u,"nprogress-custom-parent"),u.appendChild(e),e},s.remove=function(){h(document.documentElement,"nprogress-busy"),h(document.querySelector(i.parent),"nprogress-custom-parent");var t=document.getElementById("nprogress");t&&p(t)},s.isRendered=function(){return!!document.getElementById("nprogress")},s.getPositioningCSS=function(){var t=document.body.style,e="WebkitTransform"in t?"Webkit":"MozTransform"in t?"Moz":"msTransform"in t?"ms":"OTransform"in t?"O":"";return e+"Perspective"in t?"translate3d":e+"Transform"in t?"translate":"margin"};var a=function(){var t=[];function e(){var s=t.shift();s&&s(e)}return function(s){t.push(s),1==t.length&&e()}}(),l=function(){var t=["Webkit","O","Moz","ms"],e={};function s(t){return t.replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,(function(t,e){return e.toUpperCase()}))}function i(e){var s=document.body.style;if(e in s)return e;for(var i,n=t.length,r=e.charAt(0).toUpperCase()+e.slice(1);n--;)if((i=t[n]+r)in s)return i;return e}function n(t){return t=s(t),e[t]||(e[t]=i(t))}function r(t,e,s){e=n(e),t.style[e]=s}return function(t,e){var s,i,n=arguments;if(2==n.length)for(s in e)void 0!==(i=e[s])&&e.hasOwnProperty(s)&&r(t,s,i);else r(t,n[1],n[2])}}();function u(t,e){return("string"==typeof t?t:d(t)).indexOf(" "+e+" ")>=0}function c(t,e){var s=d(t),i=s+e;u(s,e)||(t.className=i.substring(1))}function h(t,e){var s,i=d(t);u(t,e)&&(s=i.replace(" "+e+" "," "),t.className=s.substring(1,s.length-1))}function d(t){return(" "+(t.className||"")+" ").replace(/\s+/gi," ")}function p(t){t&&t.parentNode&&t.parentNode.removeChild(t)}return s},void 0===(n="function"==typeof i?i.call(e,s,e,t):i)||(t.exports=n)},4183:function(t){t.exports=function(){"use strict";function t(t,e){t.split(/\s+/).forEach((t=>{e(t)}))}class e{constructor(){this._events=void 0,this._events={}}on(e,s){t(e,(t=>{const e=this._events[t]||[];e.push(s),this._events[t]=e}))}off(e,s){var i=arguments.length;0!==i?t(e,(t=>{if(1===i)return void delete this._events[t];const e=this._events[t];void 0!==e&&(e.splice(e.indexOf(s),1),this._events[t]=e)})):this._events={}}trigger(e,...s){var i=this;t(e,(t=>{const e=i._events[t];void 0!==e&&e.forEach((t=>{t.apply(i,s)}))}))}}function s(t){return t.plugins={},class extends t{constructor(...t){super(...t),this.plugins={names:[],settings:{},requested:{},loaded:{}}}static define(e,s){t.plugins[e]={name:e,fn:s}}initializePlugins(t){var e,s;const i=this,n=[];if(Array.isArray(t))t.forEach((t=>{"string"==typeof t?n.push(t):(i.plugins.settings[t.name]=t.options,n.push(t.name))}));else if(t)for(e in t)t.hasOwnProperty(e)&&(i.plugins.settings[e]=t[e],n.push(e));for(;s=n.shift();)i.require(s)}loadPlugin(e){var s=this,i=s.plugins,n=t.plugins[e];if(!t.plugins.hasOwnProperty(e))throw new Error('Unable to find "'+e+'" plugin');i.requested[e]=!0,i.loaded[e]=n.fn.apply(s,[s.plugins.settings[e]||{}]),i.names.push(e)}require(t){var e=this,s=e.plugins;if(!e.plugins.loaded.hasOwnProperty(t)){if(s.requested[t])throw new Error('Plugin has circular dependency ("'+t+'")');e.loadPlugin(t)}return s.loaded[t]}}}
/*! @orchidjs/unicode-variants | https://github.com/orchidjs/unicode-variants | Apache License (v2) */const i=t=>(t=t.filter(Boolean)).length<2?t[0]||"":1==l(t)?"["+t.join("")+"]":"(?:"+t.join("|")+")",n=t=>{if(!o(t))return t.join("");let e="",s=0;const i=()=>{s>1&&(e+="{"+s+"}")};return t.forEach(((n,r)=>{n!==t[r-1]?(i(),e+=n,s=1):s++})),i(),e},r=t=>{let e=c(t);return i(e)},o=t=>new Set(t).size!==t.length,a=t=>(t+"").replace(/([\$\(\)\*\+\.\?\[\]\^\{\|\}\\])/gu,"\\$1"),l=t=>t.reduce(((t,e)=>Math.max(t,u(e))),0),u=t=>c(t).length,c=t=>Array.from(t)
/*! @orchidjs/unicode-variants | https://github.com/orchidjs/unicode-variants | Apache License (v2) */,h=t=>{if(1===t.length)return[[t]];let e=[];const s=t.substring(1);return h(s).forEach((function(s){let i=s.slice(0);i[0]=t.charAt(0)+i[0],e.push(i),i=s.slice(0),i.unshift(t.charAt(0)),e.push(i)})),e},d=[[0,65535]],p="[̀-ͯ·ʾʼ]";let f,g;const m=3,v={},_={"/":"⁄∕",0:"߀",a:"ⱥɐɑ",aa:"ꜳ",ae:"æǽǣ",ao:"ꜵ",au:"ꜷ",av:"ꜹꜻ",ay:"ꜽ",b:"ƀɓƃ",c:"ꜿƈȼↄ",d:"đɗɖᴅƌꮷԁɦ",e:"ɛǝᴇɇ",f:"ꝼƒ",g:"ǥɠꞡᵹꝿɢ",h:"ħⱨⱶɥ",i:"ɨı",j:"ɉȷ",k:"ƙⱪꝁꝃꝅꞣ",l:"łƚɫⱡꝉꝇꞁɭ",m:"ɱɯϻ",n:"ꞥƞɲꞑᴎлԉ",o:"øǿɔɵꝋꝍᴑ",oe:"œ",oi:"ƣ",oo:"ꝏ",ou:"ȣ",p:"ƥᵽꝑꝓꝕρ",q:"ꝗꝙɋ",r:"ɍɽꝛꞧꞃ",s:"ßȿꞩꞅʂ",t:"ŧƭʈⱦꞇ",th:"þ",tz:"ꜩ",u:"ʉ",v:"ʋꝟʌ",vy:"ꝡ",w:"ⱳ",y:"ƴɏỿ",z:"ƶȥɀⱬꝣ",hv:"ƕ"};for(let t in _){let e=_[t]||"";for(let s=0;s<e.length;s++){let i=e.substring(s,s+1);v[i]=t}}const b=new RegExp(Object.keys(v).join("|")+"|"+p,"gu"),y=t=>{void 0===f&&(f=x(t||d))},k=(t,e="NFKD")=>t.normalize(e),w=t=>c(t).reduce(((t,e)=>t+A(e)),""),A=t=>(t=k(t).toLowerCase().replace(b,(t=>v[t]||"")),k(t,"NFC"));function*C(t){for(const[e,s]of t)for(let t=e;t<=s;t++){let e=String.fromCharCode(t),s=w(e);s!=e.toLowerCase()&&(s.length>m||0!=s.length&&(yield{folded:s,composed:e,code_point:t}))}}const E=t=>{const e={},s=(t,s)=>{const i=e[t]||new Set,n=new RegExp("^"+r(i)+"$","iu");s.match(n)||(i.add(a(s)),e[t]=i)};for(let e of C(t))s(e.folded,e.folded),s(e.folded,e.composed);return e},x=t=>{const e=E(t),s={};let n=[];for(let t in e){let i=e[t];i&&(s[t]=r(i)),t.length>1&&n.push(a(t))}n.sort(((t,e)=>e.length-t.length));const o=i(n);return g=new RegExp("^"+o,"u"),s},S=(t,e=1)=>{let s=0;return t=t.map((t=>(f[t]&&(s+=t.length),f[t]||t))),s>=e?n(t):""},O=(t,e=1)=>(e=Math.max(e,t.length-1),i(h(t).map((t=>S(t,e))))),F=(t,e=!0)=>{let s=t.length>1?1:0;return i(t.map((t=>{let i=[];const r=e?t.length():t.length()-1;for(let e=0;e<r;e++)i.push(O(t.substrs[e]||"",s));return n(i)})))},T=(t,e)=>{for(const s of e){if(s.start!=t.start||s.end!=t.end)continue;if(s.substrs.join("")!==t.substrs.join(""))continue;let e=t.parts;const i=t=>{for(const s of e){if(s.start===t.start&&s.substr===t.substr)return!1;if(1!=t.length&&1!=s.length){if(t.start<s.start&&t.end>s.start)return!0;if(s.start<t.start&&s.end>t.start)return!0}}return!1};if(!(s.parts.filter(i).length>0))return!0}return!1};class I{constructor(){this.parts=[],this.substrs=[],this.start=0,this.end=0}add(t){t&&(this.parts.push(t),this.substrs.push(t.substr),this.start=Math.min(t.start,this.start),this.end=Math.max(t.end,this.end))}last(){return this.parts[this.parts.length-1]}length(){return this.parts.length}clone(t,e){let s=new I,i=JSON.parse(JSON.stringify(this.parts)),n=i.pop();for(const t of i)s.add(t);let r=e.substr.substring(0,t-n.start),o=r.length;return s.add({start:n.start,end:n.start+o,length:o,substr:r}),s}}const D=t=>{y(),t=w(t);let e="",s=[new I];for(let i=0;i<t.length;i++){let n=t.substring(i).match(g);const r=t.substring(i,i+1),o=n?n[0]:null;let a=[],l=new Set;for(const t of s){const e=t.last();if(!e||1==e.length||e.end<=i)if(o){const e=o.length;t.add({start:i,end:i+e,length:e,substr:o}),l.add("1")}else t.add({start:i,end:i+1,length:1,substr:r}),l.add("2");else if(o){let s=t.clone(i,e);const n=o.length;s.add({start:i,end:i+n,length:n,substr:o}),a.push(s)}else l.add("3")}if(a.length>0){a=a.sort(((t,e)=>t.length()-e.length()));for(let t of a)T(t,s)||s.push(t)}else if(i>0&&1==l.size&&!l.has("3")){e+=F(s,!1);let t=new I;const i=s[0];i&&t.add(i.last()),s=[t]}}return e+=F(s,!0),e},M=(t,e)=>{if(t)return t[e]},P=(t,e)=>{if(t){for(var s,i=e.split(".");(s=i.shift())&&(t=t[s]););return t}},L=(t,e,s)=>{var i,n;return t?(t+="",null==e.regex||-1===(n=t.search(e.regex))?0:(i=e.string.length/t.length,0===n&&(i+=.5),i*s)):0},B=(t,e)=>{var s=t[e];if("function"==typeof s)return s;s&&!Array.isArray(s)&&(t[e]=[s])},V=(t,e)=>{if(Array.isArray(t))t.forEach(e);else for(var s in t)t.hasOwnProperty(s)&&e(t[s],s)},N=(t,e)=>"number"==typeof t&&"number"==typeof e?t>e?1:t<e?-1:0:(t=w(t+"").toLowerCase())>(e=w(e+"").toLowerCase())?1:e>t?-1:0;
/*! sifter.js | https://github.com/orchidjs/sifter.js | Apache License (v2) */
/*! sifter.js | https://github.com/orchidjs/sifter.js | Apache License (v2) */
class j{constructor(t,e){this.items=void 0,this.settings=void 0,this.items=t,this.settings=e||{diacritics:!0}}tokenize(t,e,s){if(!t||!t.length)return[];const i=[],n=t.split(/\s+/);var r;return s&&(r=new RegExp("^("+Object.keys(s).map(a).join("|")+"):(.*)$")),n.forEach((t=>{let s,n=null,o=null;r&&(s=t.match(r))&&(n=s[1],t=s[2]),t.length>0&&(o=this.settings.diacritics?D(t)||null:a(t),o&&e&&(o="\\b"+o)),i.push({string:t,regex:o?new RegExp(o,"iu"):null,field:n})})),i}getScoreFunction(t,e){var s=this.prepareSearch(t,e);return this._getScoreFunction(s)}_getScoreFunction(t){const e=t.tokens,s=e.length;if(!s)return function(){return 0};const i=t.options.fields,n=t.weights,r=i.length,o=t.getAttrFn;if(!r)return function(){return 1};const a=1===r?function(t,e){const s=i[0].field;return L(o(e,s),t,n[s]||1)}:function(t,e){var s=0;if(t.field){const i=o(e,t.field);!t.regex&&i?s+=1/r:s+=L(i,t,1)}else V(n,((i,n)=>{s+=L(o(e,n),t,i)}));return s/r};return 1===s?function(t){return a(e[0],t)}:"and"===t.options.conjunction?function(t){var i,n=0;for(let s of e){if((i=a(s,t))<=0)return 0;n+=i}return n/s}:function(t){var i=0;return V(e,(e=>{i+=a(e,t)})),i/s}}getSortFunction(t,e){var s=this.prepareSearch(t,e);return this._getSortFunction(s)}_getSortFunction(t){var e,s=[];const i=this,n=t.options,r=!t.query&&n.sort_empty?n.sort_empty:n.sort;if("function"==typeof r)return r.bind(this);const o=function(e,s){return"$score"===e?s.score:t.getAttrFn(i.items[s.id],e)};if(r)for(let e of r)(t.query||"$score"!==e.field)&&s.push(e);if(t.query){e=!0;for(let t of s)if("$score"===t.field){e=!1;break}e&&s.unshift({field:"$score",direction:"desc"})}else s=s.filter((t=>"$score"!==t.field));return s.length?function(t,e){var i,n;for(let r of s)if(n=r.field,i=("desc"===r.direction?-1:1)*N(o(n,t),o(n,e)))return i;return 0}:null}prepareSearch(t,e){const s={};var i=Object.assign({},e);if(B(i,"sort"),B(i,"sort_empty"),i.fields){B(i,"fields");const t=[];i.fields.forEach((e=>{"string"==typeof e&&(e={field:e,weight:1}),t.push(e),s[e.field]="weight"in e?e.weight:1})),i.fields=t}return{options:i,query:t.toLowerCase().trim(),tokens:this.tokenize(t,i.respect_word_boundaries,s),total:0,items:[],weights:s,getAttrFn:i.nesting?P:M}}search(t,e){var s,i,n=this;i=this.prepareSearch(t,e),e=i.options,t=i.query;const r=e.score||n._getScoreFunction(i);t.length?V(n.items,((t,n)=>{s=r(t),(!1===e.filter||s>0)&&i.items.push({score:s,id:n})})):V(n.items,((t,e)=>{i.items.push({score:1,id:e})}));const o=n._getSortFunction(i);return o&&i.items.sort(o),i.total=i.items.length,"number"==typeof e.limit&&(i.items=i.items.slice(0,e.limit)),i}}const R=(t,e)=>{if(Array.isArray(t))t.forEach(e);else for(var s in t)t.hasOwnProperty(s)&&e(t[s],s)},q=t=>{if(t.jquery)return t[0];if(t instanceof HTMLElement)return t;if(z(t)){var e=document.createElement("template");return e.innerHTML=t.trim(),e.content.firstChild}return document.querySelector(t)},z=t=>"string"==typeof t&&t.indexOf("<")>-1,H=t=>t.replace(/['"\\]/g,"\\$&"),U=(t,e)=>{var s=document.createEvent("HTMLEvents");s.initEvent(e,!0,!1),t.dispatchEvent(s)},W=(t,e)=>{Object.assign(t.style,e)},K=(t,...e)=>{var s=Q(e);(t=X(t)).map((t=>{s.map((e=>{t.classList.add(e)}))}))},Y=(t,...e)=>{var s=Q(e);(t=X(t)).map((t=>{s.map((e=>{t.classList.remove(e)}))}))},Q=t=>{var e=[];return R(t,(t=>{"string"==typeof t&&(t=t.trim().split(/[\11\12\14\15\40]/)),Array.isArray(t)&&(e=e.concat(t))})),e.filter(Boolean)},X=t=>(Array.isArray(t)||(t=[t]),t),G=(t,e,s)=>{if(!s||s.contains(t))for(;t&&t.matches;){if(t.matches(e))return t;t=t.parentNode}},Z=(t,e=0)=>e>0?t[t.length-1]:t[0],J=t=>0===Object.keys(t).length,tt=(t,e)=>{if(!t)return-1;e=e||t.nodeName;for(var s=0;t=t.previousElementSibling;)t.matches(e)&&s++;return s},et=(t,e)=>{R(e,((e,s)=>{null==e?t.removeAttribute(s):t.setAttribute(s,""+e)}))},st=(t,e)=>{t.parentNode&&t.parentNode.replaceChild(e,t)},it=(t,e)=>{if(null===e)return;if("string"==typeof e){if(!e.length)return;e=new RegExp(e,"i")}const s=t=>{var s=t.data.match(e);if(s&&t.data.length>0){var i=document.createElement("span");i.className="highlight";var n=t.splitText(s.index);n.splitText(s[0].length);var r=n.cloneNode(!0);return i.appendChild(r),st(n,i),1}return 0},i=t=>{1!==t.nodeType||!t.childNodes||/(script|style)/i.test(t.tagName)||"highlight"===t.className&&"SPAN"===t.tagName||Array.from(t.childNodes).forEach((t=>{n(t)}))},n=t=>3===t.nodeType?s(t):(i(t),0);n(t)},nt=t=>{var e=t.querySelectorAll("span.highlight");Array.prototype.forEach.call(e,(function(t){var e=t.parentNode;e.replaceChild(t.firstChild,t),e.normalize()}))},rt=65,ot=13,at=27,lt=37,ut=38,ct=39,ht=40,dt=8,pt=46,ft=9,gt="undefined"!=typeof navigator&&/Mac/.test(navigator.userAgent)?"metaKey":"ctrlKey";var mt={options:[],optgroups:[],plugins:[],delimiter:",",splitOn:null,persist:!0,diacritics:!0,create:null,createOnBlur:!1,createFilter:null,highlight:!0,openOnFocus:!0,shouldOpen:null,maxOptions:50,maxItems:null,hideSelected:null,duplicates:!1,addPrecedence:!1,selectOnTab:!1,preload:null,allowEmptyOption:!1,loadThrottle:300,loadingClass:"loading",dataAttr:null,optgroupField:"optgroup",valueField:"value",labelField:"text",disabledField:"disabled",optgroupLabelField:"label",optgroupValueField:"value",lockOptgroupOrder:!1,sortField:"$order",searchField:["text"],searchConjunction:"and",mode:null,wrapperClass:"ts-wrapper",controlClass:"ts-control",dropdownClass:"ts-dropdown",dropdownContentClass:"ts-dropdown-content",itemClass:"item",optionClass:"option",dropdownParent:null,controlInput:'<input type="text" autocomplete="off" size="1" />',copyClassesToDropdown:!1,placeholder:null,hidePlaceholder:null,shouldLoad:function(t){return t.length>0},render:{}};const vt=t=>null==t?null:_t(t),_t=t=>"boolean"==typeof t?t?"1":"0":t+"",bt=t=>(t+"").replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;"),yt=(t,e)=>{var s;return function(i,n){var r=this;s&&(r.loading=Math.max(r.loading-1,0),clearTimeout(s)),s=setTimeout((function(){s=null,r.loadedSearches[i]=!0,t.call(r,i,n)}),e)}},kt=(t,e,s)=>{var i,n=t.trigger,r={};for(i of(t.trigger=function(){var s=arguments[0];if(-1===e.indexOf(s))return n.apply(t,arguments);r[s]=arguments},s.apply(t,[]),t.trigger=n,e))i in r&&n.apply(t,r[i])},wt=t=>({start:t.selectionStart||0,length:(t.selectionEnd||0)-(t.selectionStart||0)}),At=(t,e=!1)=>{t&&(t.preventDefault(),e&&t.stopPropagation())},Ct=(t,e,s,i)=>{t.addEventListener(e,s,i)},Et=(t,e)=>!!e&&!!e[t]&&1==(e.altKey?1:0)+(e.ctrlKey?1:0)+(e.shiftKey?1:0)+(e.metaKey?1:0),xt=(t,e)=>{const s=t.getAttribute("id");return s||(t.setAttribute("id",e),e)},St=t=>t.replace(/[\\"']/g,"\\$&"),Ot=(t,e)=>{e&&t.append(e)};function Ft(t,e){var s=Object.assign({},mt,e),i=s.dataAttr,n=s.labelField,r=s.valueField,o=s.disabledField,a=s.optgroupField,l=s.optgroupLabelField,u=s.optgroupValueField,c=t.tagName.toLowerCase(),h=t.getAttribute("placeholder")||t.getAttribute("data-placeholder");if(!h&&!s.allowEmptyOption){let e=t.querySelector('option[value=""]');e&&(h=e.textContent)}var d,p,f,g,m,v,_,b={placeholder:h,options:[],optgroups:[],items:[],maxItems:null},y=()=>{const e=t.getAttribute(i);if(e)b.options=JSON.parse(e),R(b.options,(t=>{b.items.push(t[r])}));else{var o=t.value.trim()||"";if(!s.allowEmptyOption&&!o.length)return;const e=o.split(s.delimiter);R(e,(t=>{const e={};e[n]=t,e[r]=t,b.options.push(e)})),b.items=e}};return"select"===c?(p=b.options,f={},g=1,m=t=>{var e=Object.assign({},t.dataset),s=i&&e[i];return"string"==typeof s&&s.length&&(e=Object.assign(e,JSON.parse(s))),e},v=(t,e)=>{var i=vt(t.value);if(null!=i&&(i||s.allowEmptyOption)){if(f.hasOwnProperty(i)){if(e){var l=f[i][a];l?Array.isArray(l)?l.push(e):f[i][a]=[l,e]:f[i][a]=e}}else{var u=m(t);u[n]=u[n]||t.textContent,u[r]=u[r]||i,u[o]=u[o]||t.disabled,u[a]=u[a]||e,u.$option=t,f[i]=u,p.push(u)}t.selected&&b.items.push(i)}},_=t=>{var e,s;(s=m(t))[l]=s[l]||t.getAttribute("label")||"",s[u]=s[u]||g++,s[o]=s[o]||t.disabled,b.optgroups.push(s),e=s[u],R(t.children,(t=>{v(t,e)}))},b.maxItems=t.hasAttribute("multiple")?null:1,R(t.children,(t=>{"optgroup"===(d=t.tagName.toLowerCase())?_(t):"option"===d&&v(t)}))):y(),Object.assign({},mt,b,e)}var Tt=0;class It extends(s(e)){constructor(t,e){var s;super(),this.control_input=void 0,this.wrapper=void 0,this.dropdown=void 0,this.control=void 0,this.dropdown_content=void 0,this.focus_node=void 0,this.order=0,this.settings=void 0,this.input=void 0,this.tabIndex=void 0,this.is_select_tag=void 0,this.rtl=void 0,this.inputId=void 0,this._destroy=void 0,this.sifter=void 0,this.isOpen=!1,this.isDisabled=!1,this.isRequired=void 0,this.isInvalid=!1,this.isValid=!0,this.isLocked=!1,this.isFocused=!1,this.isInputHidden=!1,this.isSetup=!1,this.ignoreFocus=!1,this.ignoreHover=!1,this.hasOptions=!1,this.currentResults=void 0,this.lastValue="",this.caretPos=0,this.loading=0,this.loadedSearches={},this.activeOption=null,this.activeItems=[],this.optgroups={},this.options={},this.userOptions={},this.items=[],Tt++;var i=q(t);if(i.tomselect)throw new Error("Tom Select already initialized on this element");i.tomselect=this,s=(window.getComputedStyle&&window.getComputedStyle(i,null)).getPropertyValue("direction");const n=Ft(i,e);this.settings=n,this.input=i,this.tabIndex=i.tabIndex||0,this.is_select_tag="select"===i.tagName.toLowerCase(),this.rtl=/rtl/i.test(s),this.inputId=xt(i,"tomselect-"+Tt),this.isRequired=i.required,this.sifter=new j(this.options,{diacritics:n.diacritics}),n.mode=n.mode||(1===n.maxItems?"single":"multi"),"boolean"!=typeof n.hideSelected&&(n.hideSelected="multi"===n.mode),"boolean"!=typeof n.hidePlaceholder&&(n.hidePlaceholder="multi"!==n.mode);var r=n.createFilter;"function"!=typeof r&&("string"==typeof r&&(r=new RegExp(r)),r instanceof RegExp?n.createFilter=t=>r.test(t):n.createFilter=t=>this.settings.duplicates||!this.options[t]),this.initializePlugins(n.plugins),this.setupCallbacks(),this.setupTemplates();const o=q("<div>"),a=q("<div>"),l=this._render("dropdown"),u=q('<div role="listbox" tabindex="-1">'),c=this.input.getAttribute("class")||"",h=n.mode;var d;K(o,n.wrapperClass,c,h),K(a,n.controlClass),Ot(o,a),K(l,n.dropdownClass,h),n.copyClassesToDropdown&&K(l,c),K(u,n.dropdownContentClass),Ot(l,u),q(n.dropdownParent||o).appendChild(l),z(n.controlInput)?(d=q(n.controlInput),V(["autocorrect","autocapitalize","autocomplete"],(t=>{i.getAttribute(t)&&et(d,{[t]:i.getAttribute(t)})})),d.tabIndex=-1,a.appendChild(d),this.focus_node=d):n.controlInput?(d=q(n.controlInput),this.focus_node=d):(d=q("<input/>"),this.focus_node=a),this.wrapper=o,this.dropdown=l,this.dropdown_content=u,this.control=a,this.control_input=d,this.setup()}setup(){const t=this,e=t.settings,s=t.control_input,i=t.dropdown,n=t.dropdown_content,r=t.wrapper,o=t.control,l=t.input,u=t.focus_node,c={passive:!0},h=t.inputId+"-ts-dropdown";et(n,{id:h}),et(u,{role:"combobox","aria-haspopup":"listbox","aria-expanded":"false","aria-controls":h});const d=xt(u,t.inputId+"-ts-control"),p="label[for='"+H(t.inputId)+"']",f=document.querySelector(p),g=t.focus.bind(t);if(f){Ct(f,"click",g),et(f,{for:d});const e=xt(f,t.inputId+"-ts-label");et(u,{"aria-labelledby":e}),et(n,{"aria-labelledby":e})}if(r.style.width=l.style.width,t.plugins.names.length){const e="plugin-"+t.plugins.names.join(" plugin-");K([r,i],e)}(null===e.maxItems||e.maxItems>1)&&t.is_select_tag&&et(l,{multiple:"multiple"}),e.placeholder&&et(s,{placeholder:e.placeholder}),!e.splitOn&&e.delimiter&&(e.splitOn=new RegExp("\\s*"+a(e.delimiter)+"+\\s*")),e.load&&e.loadThrottle&&(e.load=yt(e.load,e.loadThrottle)),t.control_input.type=l.type,Ct(i,"mousemove",(()=>{t.ignoreHover=!1})),Ct(i,"mouseenter",(e=>{var s=G(e.target,"[data-selectable]",i);s&&t.onOptionHover(e,s)}),{capture:!0}),Ct(i,"click",(e=>{const s=G(e.target,"[data-selectable]");s&&(t.onOptionSelect(e,s),At(e,!0))})),Ct(o,"click",(e=>{var i=G(e.target,"[data-ts-item]",o);i&&t.onItemSelect(e,i)?At(e,!0):""==s.value&&(t.onClick(),At(e,!0))})),Ct(u,"keydown",(e=>t.onKeyDown(e))),Ct(s,"keypress",(e=>t.onKeyPress(e))),Ct(s,"input",(e=>t.onInput(e))),Ct(u,"blur",(e=>t.onBlur(e))),Ct(u,"focus",(e=>t.onFocus(e))),Ct(s,"paste",(e=>t.onPaste(e)));const m=e=>{const n=e.composedPath()[0];if(!r.contains(n)&&!i.contains(n))return t.isFocused&&t.blur(),void t.inputState();n==s&&t.isOpen?e.stopPropagation():At(e,!0)},v=()=>{t.isOpen&&t.positionDropdown()};Ct(document,"mousedown",m),Ct(window,"scroll",v,c),Ct(window,"resize",v,c),this._destroy=()=>{document.removeEventListener("mousedown",m),window.removeEventListener("scroll",v),window.removeEventListener("resize",v),f&&f.removeEventListener("click",g)},this.revertSettings={innerHTML:l.innerHTML,tabIndex:l.tabIndex},l.tabIndex=-1,l.insertAdjacentElement("afterend",t.wrapper),t.sync(!1),e.items=[],delete e.optgroups,delete e.options,Ct(l,"invalid",(()=>{t.isValid&&(t.isValid=!1,t.isInvalid=!0,t.refreshState())})),t.updateOriginalInput(),t.refreshItems(),t.close(!1),t.inputState(),t.isSetup=!0,l.disabled?t.disable():t.enable(),t.on("change",this.onChange),K(l,"tomselected","ts-hidden-accessible"),t.trigger("initialize"),!0===e.preload&&t.preload()}setupOptions(t=[],e=[]){this.addOptions(t),V(e,(t=>{this.registerOptionGroup(t)}))}setupTemplates(){var t=this,e=t.settings.labelField,s=t.settings.optgroupLabelField,i={optgroup:t=>{let e=document.createElement("div");return e.className="optgroup",e.appendChild(t.options),e},optgroup_header:(t,e)=>'<div class="optgroup-header">'+e(t[s])+"</div>",option:(t,s)=>"<div>"+s(t[e])+"</div>",item:(t,s)=>"<div>"+s(t[e])+"</div>",option_create:(t,e)=>'<div class="create">Add <strong>'+e(t.input)+"</strong>&hellip;</div>",no_results:()=>'<div class="no-results">No results found</div>',loading:()=>'<div class="spinner"></div>',not_loading:()=>{},dropdown:()=>"<div></div>"};t.settings.render=Object.assign({},i,t.settings.render)}setupCallbacks(){var t,e,s={initialize:"onInitialize",change:"onChange",item_add:"onItemAdd",item_remove:"onItemRemove",item_select:"onItemSelect",clear:"onClear",option_add:"onOptionAdd",option_remove:"onOptionRemove",option_clear:"onOptionClear",optgroup_add:"onOptionGroupAdd",optgroup_remove:"onOptionGroupRemove",optgroup_clear:"onOptionGroupClear",dropdown_open:"onDropdownOpen",dropdown_close:"onDropdownClose",type:"onType",load:"onLoad",focus:"onFocus",blur:"onBlur"};for(t in s)(e=this.settings[s[t]])&&this.on(t,e)}sync(t=!0){const e=this,s=t?Ft(e.input,{delimiter:e.settings.delimiter}):e.settings;e.setupOptions(s.options,s.optgroups),e.setValue(s.items||[],!0),e.lastQuery=null}onClick(){var t=this;if(t.activeItems.length>0)return t.clearActiveItems(),void t.focus();t.isFocused&&t.isOpen?t.blur():t.focus()}onMouseDown(){}onChange(){U(this.input,"input"),U(this.input,"change")}onPaste(t){var e=this;e.isInputHidden||e.isLocked?At(t):e.settings.splitOn&&setTimeout((()=>{var t=e.inputValue();if(t.match(e.settings.splitOn)){var s=t.trim().split(e.settings.splitOn);V(s,(t=>{vt(t)&&(this.options[t]?e.addItem(t):e.createItem(t))}))}}),0)}onKeyPress(t){var e=this;if(!e.isLocked){var s=String.fromCharCode(t.keyCode||t.which);return e.settings.create&&"multi"===e.settings.mode&&s===e.settings.delimiter?(e.createItem(),void At(t)):void 0}At(t)}onKeyDown(t){var e=this;if(e.ignoreHover=!0,e.isLocked)t.keyCode!==ft&&At(t);else{switch(t.keyCode){case rt:if(Et(gt,t)&&""==e.control_input.value)return At(t),void e.selectAll();break;case at:return e.isOpen&&(At(t,!0),e.close()),void e.clearActiveItems();case ht:if(!e.isOpen&&e.hasOptions)e.open();else if(e.activeOption){let t=e.getAdjacent(e.activeOption,1);t&&e.setActiveOption(t)}return void At(t);case ut:if(e.activeOption){let t=e.getAdjacent(e.activeOption,-1);t&&e.setActiveOption(t)}return void At(t);case ot:return void(e.canSelect(e.activeOption)?(e.onOptionSelect(t,e.activeOption),At(t)):(e.settings.create&&e.createItem()||document.activeElement==e.control_input&&e.isOpen)&&At(t));case lt:return void e.advanceSelection(-1,t);case ct:return void e.advanceSelection(1,t);case ft:return void(e.settings.selectOnTab&&(e.canSelect(e.activeOption)&&(e.onOptionSelect(t,e.activeOption),At(t)),e.settings.create&&e.createItem()&&At(t)));case dt:case pt:return void e.deleteSelection(t)}e.isInputHidden&&!Et(gt,t)&&At(t)}}onInput(t){var e=this;if(!e.isLocked){var s=e.inputValue();e.lastValue!==s&&(e.lastValue=s,e.settings.shouldLoad.call(e,s)&&e.load(s),e.refreshOptions(),e.trigger("type",s))}}onOptionHover(t,e){this.ignoreHover||this.setActiveOption(e,!1)}onFocus(t){var e=this,s=e.isFocused;if(e.isDisabled)return e.blur(),void At(t);e.ignoreFocus||(e.isFocused=!0,"focus"===e.settings.preload&&e.preload(),s||e.trigger("focus"),e.activeItems.length||(e.showInput(),e.refreshOptions(!!e.settings.openOnFocus)),e.refreshState())}onBlur(t){if(!1!==document.hasFocus()){var e=this;if(e.isFocused){e.isFocused=!1,e.ignoreFocus=!1;var s=()=>{e.close(),e.setActiveItem(),e.setCaret(e.items.length),e.trigger("blur")};e.settings.create&&e.settings.createOnBlur?e.createItem(null,s):s()}}}onOptionSelect(t,e){var s,i=this;e.parentElement&&e.parentElement.matches("[data-disabled]")||(e.classList.contains("create")?i.createItem(null,(()=>{i.settings.closeAfterSelect&&i.close()})):void 0!==(s=e.dataset.value)&&(i.lastQuery=null,i.addItem(s),i.settings.closeAfterSelect&&i.close(),!i.settings.hideSelected&&t.type&&/click/.test(t.type)&&i.setActiveOption(e)))}canSelect(t){return!!(this.isOpen&&t&&this.dropdown_content.contains(t))}onItemSelect(t,e){var s=this;return!s.isLocked&&"multi"===s.settings.mode&&(At(t),s.setActiveItem(e,t),!0)}canLoad(t){return!!this.settings.load&&!this.loadedSearches.hasOwnProperty(t)}load(t){const e=this;if(!e.canLoad(t))return;K(e.wrapper,e.settings.loadingClass),e.loading++;const s=e.loadCallback.bind(e);e.settings.load.call(e,t,s)}loadCallback(t,e){const s=this;s.loading=Math.max(s.loading-1,0),s.lastQuery=null,s.clearActiveOption(),s.setupOptions(t,e),s.refreshOptions(s.isFocused&&!s.isInputHidden),s.loading||Y(s.wrapper,s.settings.loadingClass),s.trigger("load",t,e)}preload(){var t=this.wrapper.classList;t.contains("preloaded")||(t.add("preloaded"),this.load(""))}setTextboxValue(t=""){var e=this.control_input;e.value!==t&&(e.value=t,U(e,"update"),this.lastValue=t)}getValue(){return this.is_select_tag&&this.input.hasAttribute("multiple")?this.items:this.items.join(this.settings.delimiter)}setValue(t,e){kt(this,e?[]:["change"],(()=>{this.clear(e),this.addItems(t,e)}))}setMaxItems(t){0===t&&(t=null),this.settings.maxItems=t,this.refreshState()}setActiveItem(t,e){var s,i,n,r,o,a,l=this;if("single"!==l.settings.mode){if(!t)return l.clearActiveItems(),void(l.isFocused&&l.showInput());if("click"===(s=e&&e.type.toLowerCase())&&Et("shiftKey",e)&&l.activeItems.length){for(a=l.getLastActive(),(n=Array.prototype.indexOf.call(l.control.children,a))>(r=Array.prototype.indexOf.call(l.control.children,t))&&(o=n,n=r,r=o),i=n;i<=r;i++)t=l.control.children[i],-1===l.activeItems.indexOf(t)&&l.setActiveItemClass(t);At(e)}else"click"===s&&Et(gt,e)||"keydown"===s&&Et("shiftKey",e)?t.classList.contains("active")?l.removeActiveItem(t):l.setActiveItemClass(t):(l.clearActiveItems(),l.setActiveItemClass(t));l.hideInput(),l.isFocused||l.focus()}}setActiveItemClass(t){const e=this,s=e.control.querySelector(".last-active");s&&Y(s,"last-active"),K(t,"active last-active"),e.trigger("item_select",t),-1==e.activeItems.indexOf(t)&&e.activeItems.push(t)}removeActiveItem(t){var e=this.activeItems.indexOf(t);this.activeItems.splice(e,1),Y(t,"active")}clearActiveItems(){Y(this.activeItems,"active"),this.activeItems=[]}setActiveOption(t,e=!0){t!==this.activeOption&&(this.clearActiveOption(),t&&(this.activeOption=t,et(this.focus_node,{"aria-activedescendant":t.getAttribute("id")}),et(t,{"aria-selected":"true"}),K(t,"active"),e&&this.scrollToOption(t)))}scrollToOption(t,e){if(!t)return;const s=this.dropdown_content,i=s.clientHeight,n=s.scrollTop||0,r=t.offsetHeight,o=t.getBoundingClientRect().top-s.getBoundingClientRect().top+n;o+r>i+n?this.scroll(o-i+r,e):o<n&&this.scroll(o,e)}scroll(t,e){const s=this.dropdown_content;e&&(s.style.scrollBehavior=e),s.scrollTop=t,s.style.scrollBehavior=""}clearActiveOption(){this.activeOption&&(Y(this.activeOption,"active"),et(this.activeOption,{"aria-selected":null})),this.activeOption=null,et(this.focus_node,{"aria-activedescendant":null})}selectAll(){const t=this;if("single"===t.settings.mode)return;const e=t.controlChildren();e.length&&(t.hideInput(),t.close(),t.activeItems=e,V(e,(e=>{t.setActiveItemClass(e)})))}inputState(){var t=this;t.control.contains(t.control_input)&&(et(t.control_input,{placeholder:t.settings.placeholder}),t.activeItems.length>0||!t.isFocused&&t.settings.hidePlaceholder&&t.items.length>0?(t.setTextboxValue(),t.isInputHidden=!0):(t.settings.hidePlaceholder&&t.items.length>0&&et(t.control_input,{placeholder:""}),t.isInputHidden=!1),t.wrapper.classList.toggle("input-hidden",t.isInputHidden))}hideInput(){this.inputState()}showInput(){this.inputState()}inputValue(){return this.control_input.value.trim()}focus(){var t=this;t.isDisabled||(t.ignoreFocus=!0,t.control_input.offsetWidth?t.control_input.focus():t.focus_node.focus(),setTimeout((()=>{t.ignoreFocus=!1,t.onFocus()}),0))}blur(){this.focus_node.blur(),this.onBlur()}getScoreFunction(t){return this.sifter.getScoreFunction(t,this.getSearchOptions())}getSearchOptions(){var t=this.settings,e=t.sortField;return"string"==typeof t.sortField&&(e=[{field:t.sortField}]),{fields:t.searchField,conjunction:t.searchConjunction,sort:e,nesting:t.nesting}}search(t){var e,s,i=this,n=this.getSearchOptions();if(i.settings.score&&"function"!=typeof(s=i.settings.score.call(i,t)))throw new Error('Tom Select "score" setting must be a function that returns a function');return t!==i.lastQuery?(i.lastQuery=t,e=i.sifter.search(t,Object.assign(n,{score:s})),i.currentResults=e):e=Object.assign({},i.currentResults),i.settings.hideSelected&&(e.items=e.items.filter((t=>{let e=vt(t.id);return!(e&&-1!==i.items.indexOf(e))}))),e}refreshOptions(t=!0){var e,s,i,n,r,o,a,l,u,c;const h={},d=[];var p=this,f=p.inputValue();const g=f===p.lastQuery||""==f&&null==p.lastQuery;var m=p.search(f),v=null,_=p.settings.shouldOpen||!1,b=p.dropdown_content;for(g&&(v=p.activeOption)&&(u=v.closest("[data-group]")),n=m.items.length,"number"==typeof p.settings.maxOptions&&(n=Math.min(n,p.settings.maxOptions)),n>0&&(_=!0),e=0;e<n;e++){let t=m.items[e];if(!t)continue;let n=t.id,a=p.options[n];if(void 0===a)continue;let l=_t(n),c=p.getOption(l,!0);for(p.settings.hideSelected||c.classList.toggle("selected",p.items.includes(l)),r=a[p.settings.optgroupField]||"",s=0,i=(o=Array.isArray(r)?r:[r])&&o.length;s<i;s++){r=o[s],p.optgroups.hasOwnProperty(r)||(r="");let t=h[r];void 0===t&&(t=document.createDocumentFragment(),d.push(r)),s>0&&(c=c.cloneNode(!0),et(c,{id:a.$id+"-clone-"+s,"aria-selected":null}),c.classList.add("ts-cloned"),Y(c,"active"),p.activeOption&&p.activeOption.dataset.value==n&&u&&u.dataset.group===r.toString()&&(v=c)),t.appendChild(c),h[r]=t}}p.settings.lockOptgroupOrder&&d.sort(((t,e)=>{const s=p.optgroups[t],i=p.optgroups[e];return(s&&s.$order||0)-(i&&i.$order||0)})),a=document.createDocumentFragment(),V(d,(t=>{let e=h[t];if(!e||!e.children.length)return;let s=p.optgroups[t];if(void 0!==s){let t=document.createDocumentFragment(),i=p.render("optgroup_header",s);Ot(t,i),Ot(t,e);let n=p.render("optgroup",{group:s,options:t});Ot(a,n)}else Ot(a,e)})),b.innerHTML="",Ot(b,a),p.settings.highlight&&(nt(b),m.query.length&&m.tokens.length&&V(m.tokens,(t=>{it(b,t.regex)})));var y=t=>{let e=p.render(t,{input:f});return e&&(_=!0,b.insertBefore(e,b.firstChild)),e};if(p.loading?y("loading"):p.settings.shouldLoad.call(p,f)?0===m.items.length&&y("no_results"):y("not_loading"),(l=p.canCreate(f))&&(c=y("option_create")),p.hasOptions=m.items.length>0||l,_){if(m.items.length>0){if(v||"single"!==p.settings.mode||null==p.items[0]||(v=p.getOption(p.items[0])),!b.contains(v)){let t=0;c&&!p.settings.addPrecedence&&(t=1),v=p.selectable()[t]}}else c&&(v=c);t&&!p.isOpen&&(p.open(),p.scrollToOption(v,"auto")),p.setActiveOption(v)}else p.clearActiveOption(),t&&p.isOpen&&p.close(!1)}selectable(){return this.dropdown_content.querySelectorAll("[data-selectable]")}addOption(t,e=!1){const s=this;if(Array.isArray(t))return s.addOptions(t,e),!1;const i=vt(t[s.settings.valueField]);return null!==i&&!s.options.hasOwnProperty(i)&&(t.$order=t.$order||++s.order,t.$id=s.inputId+"-opt-"+t.$order,s.options[i]=t,s.lastQuery=null,e&&(s.userOptions[i]=e,s.trigger("option_add",i,t)),i)}addOptions(t,e=!1){V(t,(t=>{this.addOption(t,e)}))}registerOption(t){return this.addOption(t)}registerOptionGroup(t){var e=vt(t[this.settings.optgroupValueField]);return null!==e&&(t.$order=t.$order||++this.order,this.optgroups[e]=t,e)}addOptionGroup(t,e){var s;e[this.settings.optgroupValueField]=t,(s=this.registerOptionGroup(e))&&this.trigger("optgroup_add",s,e)}removeOptionGroup(t){this.optgroups.hasOwnProperty(t)&&(delete this.optgroups[t],this.clearCache(),this.trigger("optgroup_remove",t))}clearOptionGroups(){this.optgroups={},this.clearCache(),this.trigger("optgroup_clear")}updateOption(t,e){const s=this;var i,n;const r=vt(t),o=vt(e[s.settings.valueField]);if(null===r)return;const a=s.options[r];if(null==a)return;if("string"!=typeof o)throw new Error("Value must be set in option data");const l=s.getOption(r),u=s.getItem(r);if(e.$order=e.$order||a.$order,delete s.options[r],s.uncacheValue(o),s.options[o]=e,l){if(s.dropdown_content.contains(l)){const t=s._render("option",e);st(l,t),s.activeOption===l&&s.setActiveOption(t)}l.remove()}u&&(-1!==(n=s.items.indexOf(r))&&s.items.splice(n,1,o),i=s._render("item",e),u.classList.contains("active")&&K(i,"active"),st(u,i)),s.lastQuery=null}removeOption(t,e){const s=this;t=_t(t),s.uncacheValue(t),delete s.userOptions[t],delete s.options[t],s.lastQuery=null,s.trigger("option_remove",t),s.removeItem(t,e)}clearOptions(t){const e=(t||this.clearFilter).bind(this);this.loadedSearches={},this.userOptions={},this.clearCache();const s={};V(this.options,((t,i)=>{e(t,i)&&(s[i]=t)})),this.options=this.sifter.items=s,this.lastQuery=null,this.trigger("option_clear")}clearFilter(t,e){return this.items.indexOf(e)>=0}getOption(t,e=!1){const s=vt(t);if(null===s)return null;const i=this.options[s];if(null!=i){if(i.$div)return i.$div;if(e)return this._render("option",i)}return null}getAdjacent(t,e,s="option"){var i,n=this;if(!t)return null;i="item"==s?n.controlChildren():n.dropdown_content.querySelectorAll("[data-selectable]");for(let s=0;s<i.length;s++)if(i[s]==t)return e>0?i[s+1]:i[s-1];return null}getItem(t){if("object"==typeof t)return t;var e=vt(t);return null!==e?this.control.querySelector(`[data-value="${St(e)}"]`):null}addItems(t,e){var s=this,i=Array.isArray(t)?t:[t];const n=(i=i.filter((t=>-1===s.items.indexOf(t))))[i.length-1];i.forEach((t=>{s.isPending=t!==n,s.addItem(t,e)}))}addItem(t,e){kt(this,e?[]:["change","dropdown_close"],(()=>{var s,i;const n=this,r=n.settings.mode,o=vt(t);if((!o||-1===n.items.indexOf(o)||("single"===r&&n.close(),"single"!==r&&n.settings.duplicates))&&null!==o&&n.options.hasOwnProperty(o)&&("single"===r&&n.clear(e),"multi"!==r||!n.isFull())){if(s=n._render("item",n.options[o]),n.control.contains(s)&&(s=s.cloneNode(!0)),i=n.isFull(),n.items.splice(n.caretPos,0,o),n.insertAtCaret(s),n.isSetup){if(!n.isPending&&n.settings.hideSelected){let t=n.getOption(o),e=n.getAdjacent(t,1);e&&n.setActiveOption(e)}n.isPending||n.settings.closeAfterSelect||n.refreshOptions(n.isFocused&&"single"!==r),0!=n.settings.closeAfterSelect&&n.isFull()?n.close():n.isPending||n.positionDropdown(),n.trigger("item_add",o,s),n.isPending||n.updateOriginalInput({silent:e})}(!n.isPending||!i&&n.isFull())&&(n.inputState(),n.refreshState())}}))}removeItem(t=null,e){const s=this;if(!(t=s.getItem(t)))return;var i,n;const r=t.dataset.value;i=tt(t),t.remove(),t.classList.contains("active")&&(n=s.activeItems.indexOf(t),s.activeItems.splice(n,1),Y(t,"active")),s.items.splice(i,1),s.lastQuery=null,!s.settings.persist&&s.userOptions.hasOwnProperty(r)&&s.removeOption(r,e),i<s.caretPos&&s.setCaret(s.caretPos-1),s.updateOriginalInput({silent:e}),s.refreshState(),s.positionDropdown(),s.trigger("item_remove",r,t)}createItem(t=null,e=(()=>{})){3===arguments.length&&(e=arguments[2]),"function"!=typeof e&&(e=()=>{});var s,i=this,n=i.caretPos;if(t=t||i.inputValue(),!i.canCreate(t))return e(),!1;i.lock();var r=!1,o=t=>{if(i.unlock(),!t||"object"!=typeof t)return e();var s=vt(t[i.settings.valueField]);if("string"!=typeof s)return e();i.setTextboxValue(),i.addOption(t,!0),i.setCaret(n),i.addItem(s),e(t),r=!0};return s="function"==typeof i.settings.create?i.settings.create.call(this,t,o):{[i.settings.labelField]:t,[i.settings.valueField]:t},r||o(s),!0}refreshItems(){var t=this;t.lastQuery=null,t.isSetup&&t.addItems(t.items),t.updateOriginalInput(),t.refreshState()}refreshState(){const t=this;t.refreshValidityState();const e=t.isFull(),s=t.isLocked;t.wrapper.classList.toggle("rtl",t.rtl);const i=t.wrapper.classList;i.toggle("focus",t.isFocused),i.toggle("disabled",t.isDisabled),i.toggle("required",t.isRequired),i.toggle("invalid",!t.isValid),i.toggle("locked",s),i.toggle("full",e),i.toggle("input-active",t.isFocused&&!t.isInputHidden),i.toggle("dropdown-active",t.isOpen),i.toggle("has-options",J(t.options)),i.toggle("has-items",t.items.length>0)}refreshValidityState(){var t=this;t.input.validity&&(t.isValid=t.input.validity.valid,t.isInvalid=!t.isValid)}isFull(){return null!==this.settings.maxItems&&this.items.length>=this.settings.maxItems}updateOriginalInput(t={}){const e=this;var s,i;const n=e.input.querySelector('option[value=""]');if(e.is_select_tag){const r=[],o=e.input.querySelectorAll("option:checked").length;function a(t,s,i){return t||(t=q('<option value="'+bt(s)+'">'+bt(i)+"</option>")),t!=n&&e.input.append(t),r.push(t),(t!=n||o>0)&&(t.selected=!0),t}e.input.querySelectorAll("option:checked").forEach((t=>{t.selected=!1})),0==e.items.length&&"single"==e.settings.mode?a(n,"",""):e.items.forEach((t=>{s=e.options[t],i=s[e.settings.labelField]||"",r.includes(s.$option)?a(e.input.querySelector(`option[value="${St(t)}"]:not(:checked)`),t,i):s.$option=a(s.$option,t,i)}))}else e.input.value=e.getValue();e.isSetup&&(t.silent||e.trigger("change",e.getValue()))}open(){var t=this;t.isLocked||t.isOpen||"multi"===t.settings.mode&&t.isFull()||(t.isOpen=!0,et(t.focus_node,{"aria-expanded":"true"}),t.refreshState(),W(t.dropdown,{visibility:"hidden",display:"block"}),t.positionDropdown(),W(t.dropdown,{visibility:"visible",display:"block"}),t.focus(),t.trigger("dropdown_open",t.dropdown))}close(t=!0){var e=this,s=e.isOpen;t&&(e.setTextboxValue(),"single"===e.settings.mode&&e.items.length&&e.hideInput()),e.isOpen=!1,et(e.focus_node,{"aria-expanded":"false"}),W(e.dropdown,{display:"none"}),e.settings.hideSelected&&e.clearActiveOption(),e.refreshState(),s&&e.trigger("dropdown_close",e.dropdown)}positionDropdown(){if("body"===this.settings.dropdownParent){var t=this.control,e=t.getBoundingClientRect(),s=t.offsetHeight+e.top+window.scrollY,i=e.left+window.scrollX;W(this.dropdown,{width:e.width+"px",top:s+"px",left:i+"px"})}}clear(t){var e=this;if(e.items.length){var s=e.controlChildren();V(s,(t=>{e.removeItem(t,!0)})),e.showInput(),t||e.updateOriginalInput(),e.trigger("clear")}}insertAtCaret(t){const e=this,s=e.caretPos,i=e.control;i.insertBefore(t,i.children[s]||null),e.setCaret(s+1)}deleteSelection(t){var e,s,i,n,r=this;e=t&&t.keyCode===dt?-1:1,s=wt(r.control_input);const o=[];if(r.activeItems.length)n=Z(r.activeItems,e),i=tt(n),e>0&&i++,V(r.activeItems,(t=>o.push(t)));else if((r.isFocused||"single"===r.settings.mode)&&r.items.length){const t=r.controlChildren();let i;e<0&&0===s.start&&0===s.length?i=t[r.caretPos-1]:e>0&&s.start===r.inputValue().length&&(i=t[r.caretPos]),void 0!==i&&o.push(i)}if(!r.shouldDelete(o,t))return!1;for(At(t,!0),void 0!==i&&r.setCaret(i);o.length;)r.removeItem(o.pop());return r.showInput(),r.positionDropdown(),r.refreshOptions(!1),!0}shouldDelete(t,e){const s=t.map((t=>t.dataset.value));return!(!s.length||"function"==typeof this.settings.onDelete&&!1===this.settings.onDelete(s,e))}advanceSelection(t,e){var s,i,n=this;n.rtl&&(t*=-1),n.inputValue().length||(Et(gt,e)||Et("shiftKey",e)?(i=(s=n.getLastActive(t))?s.classList.contains("active")?n.getAdjacent(s,t,"item"):s:t>0?n.control_input.nextElementSibling:n.control_input.previousElementSibling)&&(i.classList.contains("active")&&n.removeActiveItem(s),n.setActiveItemClass(i)):n.moveCaret(t))}moveCaret(t){}getLastActive(t){let e=this.control.querySelector(".last-active");if(e)return e;var s=this.control.querySelectorAll(".active");return s?Z(s,t):void 0}setCaret(t){this.caretPos=this.items.length}controlChildren(){return Array.from(this.control.querySelectorAll("[data-ts-item]"))}lock(){this.isLocked=!0,this.refreshState()}unlock(){this.isLocked=!1,this.refreshState()}disable(){var t=this;t.input.disabled=!0,t.control_input.disabled=!0,t.focus_node.tabIndex=-1,t.isDisabled=!0,this.close(),t.lock()}enable(){var t=this;t.input.disabled=!1,t.control_input.disabled=!1,t.focus_node.tabIndex=t.tabIndex,t.isDisabled=!1,t.unlock()}destroy(){var t=this,e=t.revertSettings;t.trigger("destroy"),t.off(),t.wrapper.remove(),t.dropdown.remove(),t.input.innerHTML=e.innerHTML,t.input.tabIndex=e.tabIndex,Y(t.input,"tomselected","ts-hidden-accessible"),t._destroy(),delete t.input.tomselect}render(t,e){var s,i;const n=this;if("function"!=typeof this.settings.render[t])return null;if(!(i=n.settings.render[t].call(this,e,bt)))return null;if(i=q(i),"option"===t||"option_create"===t?e[n.settings.disabledField]?et(i,{"aria-disabled":"true"}):et(i,{"data-selectable":""}):"optgroup"===t&&(s=e.group[n.settings.optgroupValueField],et(i,{"data-group":s}),e.group[n.settings.disabledField]&&et(i,{"data-disabled":""})),"option"===t||"item"===t){const s=_t(e[n.settings.valueField]);et(i,{"data-value":s}),"item"===t?(K(i,n.settings.itemClass),et(i,{"data-ts-item":""})):(K(i,n.settings.optionClass),et(i,{role:"option",id:e.$id}),e.$div=i,n.options[s]=e)}return i}_render(t,e){const s=this.render(t,e);if(null==s)throw"HTMLElement expected";return s}clearCache(){V(this.options,(t=>{t.$div&&(t.$div.remove(),delete t.$div)}))}uncacheValue(t){const e=this.getOption(t);e&&e.remove()}canCreate(t){return this.settings.create&&t.length>0&&this.settings.createFilter.call(this,t)}hook(t,e,s){var i=this,n=i[e];i[e]=function(){var e,r;return"after"===t&&(e=n.apply(i,arguments)),r=s.apply(i,arguments),"instead"===t?r:("before"===t&&(e=n.apply(i,arguments)),e)}}}function Dt(){Ct(this.input,"change",(()=>{this.sync()}))}function Mt(){var t=this,e=t.onOptionSelect;t.settings.hideSelected=!1;var s=function(t){setTimeout((()=>{var e=t.querySelector("input");e instanceof HTMLInputElement&&(t.classList.contains("selected")?e.checked=!0:e.checked=!1)}),1)};t.hook("after","setupTemplates",(()=>{var e=t.settings.render.option;t.settings.render.option=(s,i)=>{var n=q(e.call(t,s,i)),r=document.createElement("input");r.addEventListener("click",(function(t){At(t)})),r.type="checkbox";const o=vt(s[t.settings.valueField]);return o&&t.items.indexOf(o)>-1&&(r.checked=!0),n.prepend(r),n}})),t.on("item_remove",(e=>{var i=t.getOption(e);i&&(i.classList.remove("selected"),s(i))})),t.on("item_add",(e=>{var i=t.getOption(e);i&&s(i)})),t.hook("instead","onOptionSelect",((i,n)=>{if(n.classList.contains("selected"))return n.classList.remove("selected"),t.removeItem(n.dataset.value),t.refreshOptions(),void At(i,!0);e.call(t,i,n),s(n)}))}function Pt(t){const e=this,s=Object.assign({className:"clear-button",title:"Clear All",html:t=>`<div class="${t.className}" title="${t.title}">&#10799;</div>`},t);e.on("initialize",(()=>{var t=q(s.html(s));t.addEventListener("click",(t=>{e.isDisabled||(e.clear(),"single"===e.settings.mode&&e.settings.allowEmptyOption&&e.addItem(""),t.preventDefault(),t.stopPropagation())})),e.control.appendChild(t)}))}function Lt(){var t=this;if(!$.fn.sortable)throw new Error('The "drag_drop" plugin requires jQuery UI "sortable".');if("multi"===t.settings.mode){var e=t.lock,s=t.unlock;t.hook("instead","lock",(()=>{var s=$(t.control).data("sortable");return s&&s.disable(),e.call(t)})),t.hook("instead","unlock",(()=>{var e=$(t.control).data("sortable");return e&&e.enable(),s.call(t)})),t.on("initialize",(()=>{var e=$(t.control).sortable({items:"[data-value]",forcePlaceholderSize:!0,disabled:t.isLocked,start:(t,s)=>{s.placeholder.css("width",s.helper.css("width")),e.css({overflow:"visible"})},stop:()=>{e.css({overflow:"hidden"});var s=[];e.children("[data-value]").each((function(){this.dataset.value&&s.push(this.dataset.value)})),t.setValue(s)}})}))}}function Bt(t){const e=this,s=Object.assign({title:"Untitled",headerClass:"dropdown-header",titleRowClass:"dropdown-header-title",labelClass:"dropdown-header-label",closeClass:"dropdown-header-close",html:t=>'<div class="'+t.headerClass+'"><div class="'+t.titleRowClass+'"><span class="'+t.labelClass+'">'+t.title+'</span><a class="'+t.closeClass+'">&times;</a></div></div>'},t);e.on("initialize",(()=>{var t=q(s.html(s)),i=t.querySelector("."+s.closeClass);i&&i.addEventListener("click",(t=>{At(t,!0),e.close()})),e.dropdown.insertBefore(t,e.dropdown.firstChild)}))}function Vt(){var t=this;t.hook("instead","setCaret",(e=>{"single"!==t.settings.mode&&t.control.contains(t.control_input)?(e=Math.max(0,Math.min(t.items.length,e)))==t.caretPos||t.isPending||t.controlChildren().forEach(((s,i)=>{i<e?t.control_input.insertAdjacentElement("beforebegin",s):t.control.appendChild(s)})):e=t.items.length,t.caretPos=e})),t.hook("instead","moveCaret",(e=>{if(!t.isFocused)return;const s=t.getLastActive(e);if(s){const i=tt(s);t.setCaret(e>0?i+1:i),t.setActiveItem(),Y(s,"last-active")}else t.setCaret(t.caretPos+e)}))}function Nt(){const t=this;t.settings.shouldOpen=!0,t.hook("before","setup",(()=>{t.focus_node=t.control,K(t.control_input,"dropdown-input");const e=q('<div class="dropdown-input-wrap">');e.append(t.control_input),t.dropdown.insertBefore(e,t.dropdown.firstChild);const s=q('<input class="items-placeholder" tabindex="-1" />');s.placeholder=t.settings.placeholder||"",t.control.append(s)})),t.on("initialize",(()=>{t.control_input.addEventListener("keydown",(e=>{switch(e.keyCode){case at:return t.isOpen&&(At(e,!0),t.close()),void t.clearActiveItems();case ft:t.focus_node.tabIndex=-1}return t.onKeyDown.call(t,e)})),t.on("blur",(()=>{t.focus_node.tabIndex=t.isDisabled?-1:t.tabIndex})),t.on("dropdown_open",(()=>{t.control_input.focus()}));const e=t.onBlur;t.hook("instead","onBlur",(s=>{if(!s||s.relatedTarget!=t.control_input)return e.call(t)})),Ct(t.control_input,"blur",(()=>t.onBlur())),t.hook("before","close",(()=>{t.isOpen&&t.focus_node.focus({preventScroll:!0})}))}))}function $t(){var t=this;t.on("initialize",(()=>{var e=document.createElement("span"),s=t.control_input;e.style.cssText="position:absolute; top:-99999px; left:-99999px; width:auto; padding:0; white-space:pre; ",t.wrapper.appendChild(e);var i=["letterSpacing","fontSize","fontFamily","fontWeight","textTransform"];for(const t of i)e.style[t]=s.style[t];var n=()=>{e.textContent=s.value,s.style.width=e.clientWidth+"px"};n(),t.on("update item_add item_remove",n),Ct(s,"input",n),Ct(s,"keyup",n),Ct(s,"blur",n),Ct(s,"update",n)}))}function jt(){var t=this,e=t.deleteSelection;this.hook("instead","deleteSelection",(s=>!!t.activeItems.length&&e.call(t,s)))}function Rt(){this.hook("instead","setActiveItem",(()=>{})),this.hook("instead","selectAll",(()=>{}))}function qt(){var t=this,e=t.onKeyDown;t.hook("instead","onKeyDown",(s=>{var i,n,r,o;if(!t.isOpen||s.keyCode!==lt&&s.keyCode!==ct)return e.call(t,s);t.ignoreHover=!0,o=G(t.activeOption,"[data-group]"),i=tt(t.activeOption,"[data-selectable]"),o&&(o=s.keyCode===lt?o.previousSibling:o.nextSibling)&&(n=(r=o.querySelectorAll("[data-selectable]"))[Math.min(r.length-1,i)])&&t.setActiveOption(n)}))}function zt(t){const e=Object.assign({label:"&times;",title:"Remove",className:"remove",append:!0},t);var s=this;if(e.append){var i='<a href="javascript:void(0)" class="'+e.className+'" tabindex="-1" title="'+bt(e.title)+'">'+e.label+"</a>";s.hook("after","setupTemplates",(()=>{var t=s.settings.render.item;s.settings.render.item=(e,n)=>{var r=q(t.call(s,e,n)),o=q(i);return r.appendChild(o),Ct(o,"mousedown",(t=>{At(t,!0)})),Ct(o,"click",(t=>{At(t,!0),s.isLocked||s.shouldDelete([r],t)&&(s.removeItem(r),s.refreshOptions(!1),s.inputState())})),r}}))}}function Ht(t){const e=this,s=Object.assign({text:t=>t[e.settings.labelField]},t);e.on("item_remove",(function(t){if(e.isFocused&&""===e.control_input.value.trim()){var i=e.options[t];i&&e.setTextboxValue(s.text.call(e,i))}}))}function Ut(){const t=this,e=t.canLoad,s=t.clearActiveOption,i=t.loadCallback;var n,r,o={},a=!1,l=[];if(t.settings.shouldLoadMore||(t.settings.shouldLoadMore=()=>{if(n.clientHeight/(n.scrollHeight-n.scrollTop)>.9)return!0;if(t.activeOption){var e=t.selectable();if(Array.from(e).indexOf(t.activeOption)>=e.length-2)return!0}return!1}),!t.settings.firstUrl)throw"virtual_scroll plugin requires a firstUrl() method";t.settings.sortField=[{field:"$order"},{field:"$score"}];const u=e=>!("number"==typeof t.settings.maxOptions&&n.children.length>=t.settings.maxOptions||!(e in o)||!o[e]),c=(e,s)=>t.items.indexOf(s)>=0||l.indexOf(s)>=0;t.setNextUrl=(t,e)=>{o[t]=e},t.getUrl=e=>{if(e in o){const t=o[e];return o[e]=!1,t}return o={},t.settings.firstUrl.call(t,e)},t.hook("instead","clearActiveOption",(()=>{if(!a)return s.call(t)})),t.hook("instead","canLoad",(s=>s in o?u(s):e.call(t,s))),t.hook("instead","loadCallback",((e,s)=>{if(a){if(r){const s=e[0];void 0!==s&&(r.dataset.value=s[t.settings.valueField])}}else t.clearOptions(c);i.call(t,e,s),a=!1})),t.hook("after","refreshOptions",(()=>{const e=t.lastValue;var s;u(e)?(s=t.render("loading_more",{query:e}))&&(s.setAttribute("data-selectable",""),r=s):e in o&&!n.querySelector(".no-results")&&(s=t.render("no_more_results",{query:e})),s&&(K(s,t.settings.optionClass),n.append(s))})),t.on("initialize",(()=>{l=Object.keys(t.options),n=t.dropdown_content,t.settings.render=Object.assign({},{loading_more:()=>'<div class="loading-more-results">Loading more results ... </div>',no_more_results:()=>'<div class="no-more-results">No more results</div>'},t.settings.render),n.addEventListener("scroll",(()=>{t.settings.shouldLoadMore.call(t)&&u(t.lastValue)&&(a||(a=!0,t.load.call(t,t.lastValue)))}))}))}return It.define("change_listener",Dt),It.define("checkbox_options",Mt),It.define("clear_button",Pt),It.define("drag_drop",Lt),It.define("dropdown_header",Bt),It.define("caret_position",Vt),It.define("dropdown_input",Nt),It.define("input_autogrow",$t),It.define("no_backspace_delete",jt),It.define("no_active_items",Rt),It.define("optgroup_columns",qt),It.define("remove_button",zt),It.define("restore_on_backspace",Ht),It.define("virtual_scroll",Ut),It}()}},e={};function s(i){var n=e[i];if(void 0!==n)return n.exports;var r=e[i]={exports:{}};return t[i].call(r.exports,r,r.exports,s),r.exports}s.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return s.d(e,{a:e}),e},s.d=(t,e)=>{for(var i in e)s.o(e,i)&&!s.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},s.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),s.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},(()=>{"use strict";var t={};s.r(t),s.d(t,{afterMain:()=>ut,afterRead:()=>ot,afterWrite:()=>dt,applyStyles:()=>bt,arrow:()=>$t,auto:()=>Y,basePlacements:()=>Q,beforeMain:()=>at,beforeRead:()=>nt,beforeWrite:()=>ct,bottom:()=>U,clippingParents:()=>Z,computeStyles:()=>zt,createPopper:()=>be,createPopperBase:()=>_e,createPopperLite:()=>ye,detectOverflow:()=>re,end:()=>G,eventListeners:()=>Ut,flip:()=>oe,hide:()=>ue,left:()=>K,main:()=>lt,modifierPhases:()=>pt,offset:()=>ce,placements:()=>it,popper:()=>tt,popperGenerator:()=>ve,popperOffsets:()=>he,preventOverflow:()=>de,read:()=>rt,reference:()=>et,right:()=>W,start:()=>X,top:()=>H,variationPlacements:()=>st,viewport:()=>J,write:()=>ht});var e={};s.r(e),s.d(e,{Alert:()=>fs,Button:()=>ms,Carousel:()=>Ys,Collapse:()=>li,Dropdown:()=>Mi,Modal:()=>gn,Offcanvas:()=>Mn,Popover:()=>er,ScrollSpy:()=>dr,Tab:()=>Pr,Toast:()=>Qr,Tooltip:()=>Zn});var i={};s.r(i),s.d(i,{getColor:()=>Zr,hexToRgba:()=>Gr,prefix:()=>Xr});var n=new Map;function r(t){var e=n.get(t);e&&e.destroy()}function o(t){var e=n.get(t);e&&e.update()}var a=null;"undefined"==typeof window?((a=function(t){return t}).destroy=function(t){return t},a.update=function(t){return t}):((a=function(t,e){return t&&Array.prototype.forEach.call(t.length?t:[t],(function(t){return function(t){if(t&&t.nodeName&&"TEXTAREA"===t.nodeName&&!n.has(t)){var e,s=null,i=window.getComputedStyle(t),r=(e=t.value,function(){a({testForHeightReduction:""===e||!t.value.startsWith(e),restoreTextAlign:null}),e=t.value}),o=function(e){t.removeEventListener("autosize:destroy",o),t.removeEventListener("autosize:update",l),t.removeEventListener("input",r),window.removeEventListener("resize",l),Object.keys(e).forEach((function(s){return t.style[s]=e[s]})),n.delete(t)}.bind(t,{height:t.style.height,resize:t.style.resize,textAlign:t.style.textAlign,overflowY:t.style.overflowY,overflowX:t.style.overflowX,wordWrap:t.style.wordWrap});t.addEventListener("autosize:destroy",o),t.addEventListener("autosize:update",l),t.addEventListener("input",r),window.addEventListener("resize",l),t.style.overflowX="hidden",t.style.wordWrap="break-word",n.set(t,{destroy:o,update:l}),l()}function a(e){var n,r,o=e.restoreTextAlign,l=void 0===o?null:o,u=e.testForHeightReduction,c=void 0===u||u,h=i.overflowY;if(0!==t.scrollHeight&&("vertical"===i.resize?t.style.resize="none":"both"===i.resize&&(t.style.resize="horizontal"),c&&(n=function(t){for(var e=[];t&&t.parentNode&&t.parentNode instanceof Element;)t.parentNode.scrollTop&&e.push([t.parentNode,t.parentNode.scrollTop]),t=t.parentNode;return function(){return e.forEach((function(t){var e=t[0],s=t[1];e.style.scrollBehavior="auto",e.scrollTop=s,e.style.scrollBehavior=null}))}}(t),t.style.height=""),r="content-box"===i.boxSizing?t.scrollHeight-(parseFloat(i.paddingTop)+parseFloat(i.paddingBottom)):t.scrollHeight+parseFloat(i.borderTopWidth)+parseFloat(i.borderBottomWidth),"none"!==i.maxHeight&&r>parseFloat(i.maxHeight)?("hidden"===i.overflowY&&(t.style.overflow="scroll"),r=parseFloat(i.maxHeight)):"hidden"!==i.overflowY&&(t.style.overflow="hidden"),t.style.height=r+"px",l&&(t.style.textAlign=l),n&&n(),s!==r&&(t.dispatchEvent(new Event("autosize:resized",{bubbles:!0})),s=r),h!==i.overflow&&!l)){var d=i.textAlign;"hidden"===i.overflow&&(t.style.textAlign="start"===d?"end":"start"),a({restoreTextAlign:d,testForHeightReduction:!0})}}function l(){a({testForHeightReduction:!0,restoreTextAlign:null})}}(t)})),t}).destroy=function(t){return t&&Array.prototype.forEach.call(t.length?t:[t],r),t},a.update=function(t){return t&&Array.prototype.forEach.call(t.length?t:[t],o),t});const l=a,u=document.querySelectorAll('[data-bs-toggle="autosize"]');function c(t){return"string"==typeof t||t instanceof String}function h(t){var e;return"object"==typeof t&&null!=t&&"Object"===(null==t||null==(e=t.constructor)?void 0:e.name)}function d(t,e){return Array.isArray(e)?d(t,((t,s)=>e.includes(s))):Object.entries(t).reduce(((t,s)=>{let[i,n]=s;return e(n,i)&&(t[i]=n),t}),{})}u.length&&u.forEach((function(t){l(t)}));const p="NONE",f="LEFT",g="FORCE_LEFT",m="RIGHT",v="FORCE_RIGHT";function _(t){return t.replace(/([.*+?^=!:${}()|[\]/\\])/g,"\\$1")}function b(t,e){if(e===t)return!0;const s=Array.isArray(e),i=Array.isArray(t);let n;if(s&&i){if(e.length!=t.length)return!1;for(n=0;n<e.length;n++)if(!b(e[n],t[n]))return!1;return!0}if(s!=i)return!1;if(e&&t&&"object"==typeof e&&"object"==typeof t){const s=e instanceof Date,i=t instanceof Date;if(s&&i)return e.getTime()==t.getTime();if(s!=i)return!1;const r=e instanceof RegExp,o=t instanceof RegExp;if(r&&o)return e.toString()==t.toString();if(r!=o)return!1;const a=Object.keys(e);for(n=0;n<a.length;n++)if(!Object.prototype.hasOwnProperty.call(t,a[n]))return!1;for(n=0;n<a.length;n++)if(!b(t[a[n]],e[a[n]]))return!1;return!0}return!(!e||!t||"function"!=typeof e||"function"!=typeof t)&&e.toString()===t.toString()}class y{constructor(t){for(Object.assign(this,t);this.value.slice(0,this.startChangePos)!==this.oldValue.slice(0,this.startChangePos);)--this.oldSelection.start}get startChangePos(){return Math.min(this.cursorPos,this.oldSelection.start)}get insertedCount(){return this.cursorPos-this.startChangePos}get inserted(){return this.value.substr(this.startChangePos,this.insertedCount)}get removedCount(){return Math.max(this.oldSelection.end-this.startChangePos||this.oldValue.length-this.value.length,0)}get removed(){return this.oldValue.substr(this.startChangePos,this.removedCount)}get head(){return this.value.substring(0,this.startChangePos)}get tail(){return this.value.substring(this.startChangePos+this.insertedCount)}get removeDirection(){return!this.removedCount||this.insertedCount?p:this.oldSelection.end!==this.cursorPos&&this.oldSelection.start!==this.cursorPos||this.oldSelection.end!==this.oldSelection.start?f:m}}function k(t,e){return new k.InputMask(t,e)}function w(t){if(null==t)throw new Error("mask property should be defined");return t instanceof RegExp?k.MaskedRegExp:c(t)?k.MaskedPattern:t===Date?k.MaskedDate:t===Number?k.MaskedNumber:Array.isArray(t)||t===Array?k.MaskedDynamic:k.Masked&&t.prototype instanceof k.Masked?t:k.Masked&&t instanceof k.Masked?t.constructor:t instanceof Function?k.MaskedFunction:(console.warn("Mask not found for mask",t),k.Masked)}function A(t){if(!t)throw new Error("Options in not defined");if(k.Masked){if(t.prototype instanceof k.Masked)return{mask:t};const{mask:e,...s}=t instanceof k.Masked?{mask:t}:h(t)&&t.mask instanceof k.Masked?t:{};if(e){const t=e.mask;return{...d(e,((t,e)=>!e.startsWith("_"))),mask:e.constructor,_mask:t,...s}}}return h(t)?{...t}:{mask:t}}function C(t){if(k.Masked&&t instanceof k.Masked)return t;const e=A(t),s=w(e.mask);if(!s)throw new Error("Masked class is not found for provided mask, appropriate module needs to be imported manually before creating mask.");return e.mask===s&&delete e.mask,e._mask&&(e.mask=e._mask,delete e._mask),new s(e)}k.createMask=C;class E{get selectionStart(){let t;try{t=this._unsafeSelectionStart}catch{}return null!=t?t:this.value.length}get selectionEnd(){let t;try{t=this._unsafeSelectionEnd}catch{}return null!=t?t:this.value.length}select(t,e){if(null!=t&&null!=e&&(t!==this.selectionStart||e!==this.selectionEnd))try{this._unsafeSelect(t,e)}catch{}}get isActive(){return!1}}k.MaskElement=E;class x extends E{constructor(t){super(),this.input=t,this._handlers={}}get rootElement(){var t,e,s;return null!=(t=null==(e=(s=this.input).getRootNode)?void 0:e.call(s))?t:document}get isActive(){return this.input===this.rootElement.activeElement}bindEvents(t){Object.keys(t).forEach((e=>this._toggleEventHandler(x.EVENTS_MAP[e],t[e])))}unbindEvents(){Object.keys(this._handlers).forEach((t=>this._toggleEventHandler(t)))}_toggleEventHandler(t,e){this._handlers[t]&&(this.input.removeEventListener(t,this._handlers[t]),delete this._handlers[t]),e&&(this.input.addEventListener(t,e),this._handlers[t]=e)}}x.EVENTS_MAP={selectionChange:"keydown",input:"input",drop:"drop",click:"click",focus:"focus",commit:"blur"},k.HTMLMaskElement=x;class S extends x{constructor(t){super(t),this.input=t,this._handlers={}}get _unsafeSelectionStart(){return null!=this.input.selectionStart?this.input.selectionStart:this.value.length}get _unsafeSelectionEnd(){return this.input.selectionEnd}_unsafeSelect(t,e){this.input.setSelectionRange(t,e)}get value(){return this.input.value}set value(t){this.input.value=t}}k.HTMLMaskElement=x;class O extends x{get _unsafeSelectionStart(){const t=this.rootElement,e=t.getSelection&&t.getSelection(),s=e&&e.anchorOffset,i=e&&e.focusOffset;return null==i||null==s||s<i?s:i}get _unsafeSelectionEnd(){const t=this.rootElement,e=t.getSelection&&t.getSelection(),s=e&&e.anchorOffset,i=e&&e.focusOffset;return null==i||null==s||s>i?s:i}_unsafeSelect(t,e){if(!this.rootElement.createRange)return;const s=this.rootElement.createRange();s.setStart(this.input.firstChild||this.input,t),s.setEnd(this.input.lastChild||this.input,e);const i=this.rootElement,n=i.getSelection&&i.getSelection();n&&(n.removeAllRanges(),n.addRange(s))}get value(){return this.input.textContent||""}set value(t){this.input.textContent=t}}k.HTMLContenteditableMaskElement=O;k.InputMask=class{constructor(t,e){this.el=t instanceof E?t:t.isContentEditable&&"INPUT"!==t.tagName&&"TEXTAREA"!==t.tagName?new O(t):new S(t),this.masked=C(e),this._listeners={},this._value="",this._unmaskedValue="",this._saveSelection=this._saveSelection.bind(this),this._onInput=this._onInput.bind(this),this._onChange=this._onChange.bind(this),this._onDrop=this._onDrop.bind(this),this._onFocus=this._onFocus.bind(this),this._onClick=this._onClick.bind(this),this.alignCursor=this.alignCursor.bind(this),this.alignCursorFriendly=this.alignCursorFriendly.bind(this),this._bindEvents(),this.updateValue(),this._onChange()}maskEquals(t){var e;return null==t||(null==(e=this.masked)?void 0:e.maskEquals(t))}get mask(){return this.masked.mask}set mask(t){if(this.maskEquals(t))return;if(!(t instanceof k.Masked)&&this.masked.constructor===w(t))return void this.masked.updateOptions({mask:t});const e=t instanceof k.Masked?t:C({mask:t});e.unmaskedValue=this.masked.unmaskedValue,this.masked=e}get value(){return this._value}set value(t){this.value!==t&&(this.masked.value=t,this.updateControl(),this.alignCursor())}get unmaskedValue(){return this._unmaskedValue}set unmaskedValue(t){this.unmaskedValue!==t&&(this.masked.unmaskedValue=t,this.updateControl(),this.alignCursor())}get typedValue(){return this.masked.typedValue}set typedValue(t){this.masked.typedValueEquals(t)||(this.masked.typedValue=t,this.updateControl(),this.alignCursor())}get displayValue(){return this.masked.displayValue}_bindEvents(){this.el.bindEvents({selectionChange:this._saveSelection,input:this._onInput,drop:this._onDrop,click:this._onClick,focus:this._onFocus,commit:this._onChange})}_unbindEvents(){this.el&&this.el.unbindEvents()}_fireEvent(t,e){const s=this._listeners[t];s&&s.forEach((t=>t(e)))}get selectionStart(){return this._cursorChanging?this._changingCursorPos:this.el.selectionStart}get cursorPos(){return this._cursorChanging?this._changingCursorPos:this.el.selectionEnd}set cursorPos(t){this.el&&this.el.isActive&&(this.el.select(t,t),this._saveSelection())}_saveSelection(){this.displayValue!==this.el.value&&console.warn("Element value was changed outside of mask. Syncronize mask using `mask.updateValue()` to work properly."),this._selection={start:this.selectionStart,end:this.cursorPos}}updateValue(){this.masked.value=this.el.value,this._value=this.masked.value}updateControl(){const t=this.masked.unmaskedValue,e=this.masked.value,s=this.displayValue,i=this.unmaskedValue!==t||this.value!==e;this._unmaskedValue=t,this._value=e,this.el.value!==s&&(this.el.value=s),i&&this._fireChangeEvents()}updateOptions(t){const{mask:e,...s}=t,i=!this.maskEquals(e),n=!b(this.masked,s);i&&(this.mask=e),n&&this.masked.updateOptions(s),(i||n)&&this.updateControl()}updateCursor(t){null!=t&&(this.cursorPos=t,this._delayUpdateCursor(t))}_delayUpdateCursor(t){this._abortUpdateCursor(),this._changingCursorPos=t,this._cursorChanging=setTimeout((()=>{this.el&&(this.cursorPos=this._changingCursorPos,this._abortUpdateCursor())}),10)}_fireChangeEvents(){this._fireEvent("accept",this._inputEvent),this.masked.isComplete&&this._fireEvent("complete",this._inputEvent)}_abortUpdateCursor(){this._cursorChanging&&(clearTimeout(this._cursorChanging),delete this._cursorChanging)}alignCursor(){this.cursorPos=this.masked.nearestInputPos(this.masked.nearestInputPos(this.cursorPos,f))}alignCursorFriendly(){this.selectionStart===this.cursorPos&&this.alignCursor()}on(t,e){return this._listeners[t]||(this._listeners[t]=[]),this._listeners[t].push(e),this}off(t,e){if(!this._listeners[t])return this;if(!e)return delete this._listeners[t],this;const s=this._listeners[t].indexOf(e);return s>=0&&this._listeners[t].splice(s,1),this}_onInput(t){if(this._inputEvent=t,this._abortUpdateCursor(),!this._selection)return this.updateValue();const e=new y({value:this.el.value,cursorPos:this.cursorPos,oldValue:this.displayValue,oldSelection:this._selection}),s=this.masked.rawInputValue,i=this.masked.splice(e.startChangePos,e.removed.length,e.inserted,e.removeDirection,{input:!0,raw:!0}).offset,n=s===this.masked.rawInputValue?e.removeDirection:p;let r=this.masked.nearestInputPos(e.startChangePos+i,n);n!==p&&(r=this.masked.nearestInputPos(r,p)),this.updateControl(),this.updateCursor(r),delete this._inputEvent}_onChange(){this.displayValue!==this.el.value&&this.updateValue(),this.masked.doCommit(),this.updateControl(),this._saveSelection()}_onDrop(t){t.preventDefault(),t.stopPropagation()}_onFocus(t){this.alignCursorFriendly()}_onClick(t){this.alignCursorFriendly()}destroy(){this._unbindEvents(),this._listeners.length=0,delete this.el}};class F{static normalize(t){return Array.isArray(t)?t:[t,new F]}constructor(t){Object.assign(this,{inserted:"",rawInserted:"",skip:!1,tailShift:0},t)}aggregate(t){return this.rawInserted+=t.rawInserted,this.skip=this.skip||t.skip,this.inserted+=t.inserted,this.tailShift+=t.tailShift,this}get offset(){return this.tailShift+this.inserted.length}}k.ChangeDetails=F;class T{constructor(t,e,s){void 0===t&&(t=""),void 0===e&&(e=0),this.value=t,this.from=e,this.stop=s}toString(){return this.value}extend(t){this.value+=String(t)}appendTo(t){return t.append(this.toString(),{tail:!0}).aggregate(t._appendPlaceholder())}get state(){return{value:this.value,from:this.from,stop:this.stop}}set state(t){Object.assign(this,t)}unshift(t){if(!this.value.length||null!=t&&this.from>=t)return"";const e=this.value[0];return this.value=this.value.slice(1),e}shift(){if(!this.value.length)return"";const t=this.value[this.value.length-1];return this.value=this.value.slice(0,-1),t}}class I{constructor(t){this._value="",this._update({...I.DEFAULTS,...t}),this._initialized=!0}updateOptions(t){Object.keys(t).length&&this.withValueRefresh(this._update.bind(this,t))}_update(t){Object.assign(this,t)}get state(){return{_value:this.value,_rawInputValue:this.rawInputValue}}set state(t){this._value=t._value}reset(){this._value=""}get value(){return this._value}set value(t){this.resolve(t,{input:!0})}resolve(t,e){void 0===e&&(e={input:!0}),this.reset(),this.append(t,e,""),this.doCommit()}get unmaskedValue(){return this.value}set unmaskedValue(t){this.resolve(t,{})}get typedValue(){return this.parse?this.parse(this.value,this):this.unmaskedValue}set typedValue(t){this.format?this.value=this.format(t,this):this.unmaskedValue=String(t)}get rawInputValue(){return this.extractInput(0,this.displayValue.length,{raw:!0})}set rawInputValue(t){this.resolve(t,{raw:!0})}get displayValue(){return this.value}get isComplete(){return!0}get isFilled(){return this.isComplete}nearestInputPos(t,e){return t}totalInputPositions(t,e){return void 0===t&&(t=0),void 0===e&&(e=this.displayValue.length),Math.min(this.displayValue.length,e-t)}extractInput(t,e,s){return void 0===t&&(t=0),void 0===e&&(e=this.displayValue.length),this.displayValue.slice(t,e)}extractTail(t,e){return void 0===t&&(t=0),void 0===e&&(e=this.displayValue.length),new T(this.extractInput(t,e),t)}appendTail(t){return c(t)&&(t=new T(String(t))),t.appendTo(this)}_appendCharRaw(t,e){return t?(this._value+=t,new F({inserted:t,rawInserted:t})):new F}_appendChar(t,e,s){void 0===e&&(e={});const i=this.state;let n;if([t,n]=this.doPrepareChar(t,e),n=n.aggregate(this._appendCharRaw(t,e)),n.inserted){let t,r=!1!==this.doValidate(e);if(r&&null!=s){const e=this.state;!0===this.overwrite&&(t=s.state,s.unshift(this.displayValue.length-n.tailShift));let i=this.appendTail(s);r=i.rawInserted===s.toString(),r&&i.inserted||"shift"!==this.overwrite||(this.state=e,t=s.state,s.shift(),i=this.appendTail(s),r=i.rawInserted===s.toString()),r&&i.inserted&&(this.state=e)}r||(n=new F,this.state=i,s&&t&&(s.state=t))}return n}_appendPlaceholder(){return new F}_appendEager(){return new F}append(t,e,s){if(!c(t))throw new Error("value should be string");const i=c(s)?new T(String(s)):s;let n;null!=e&&e.tail&&(e._beforeTailState=this.state),[t,n]=this.doPrepare(t,e);for(let s=0;s<t.length;++s){const r=this._appendChar(t[s],e,i);if(!r.rawInserted&&!this.doSkipInvalid(t[s],e,i))break;n.aggregate(r)}return(!0===this.eager||"append"===this.eager)&&null!=e&&e.input&&t&&n.aggregate(this._appendEager()),null!=i&&(n.tailShift+=this.appendTail(i).tailShift),n}remove(t,e){return void 0===t&&(t=0),void 0===e&&(e=this.displayValue.length),this._value=this.displayValue.slice(0,t)+this.displayValue.slice(e),new F}withValueRefresh(t){if(this._refreshing||!this._initialized)return t();this._refreshing=!0;const e=this.rawInputValue,s=this.value,i=t();return this.rawInputValue=e,this.value&&this.value!==s&&0===s.indexOf(this.value)&&this.append(s.slice(this.displayValue.length),{},""),delete this._refreshing,i}runIsolated(t){if(this._isolated||!this._initialized)return t(this);this._isolated=!0;const e=this.state,s=t(this);return this.state=e,delete this._isolated,s}doSkipInvalid(t,e,s){return Boolean(this.skipInvalid)}doPrepare(t,e){return void 0===e&&(e={}),F.normalize(this.prepare?this.prepare(t,this,e):t)}doPrepareChar(t,e){return void 0===e&&(e={}),F.normalize(this.prepareChar?this.prepareChar(t,this,e):t)}doValidate(t){return(!this.validate||this.validate(this.value,this,t))&&(!this.parent||this.parent.doValidate(t))}doCommit(){this.commit&&this.commit(this.value,this)}splice(t,e,s,i,n){void 0===i&&(i=p),void 0===n&&(n={input:!0});const r=t+e,o=this.extractTail(r),a=!0===this.eager||"remove"===this.eager;let l;a&&(i=function(t){switch(t){case f:return g;case m:return v;default:return t}}(i),l=this.extractInput(0,r,{raw:!0}));let u=t;const c=new F;if(i!==p&&(u=this.nearestInputPos(t,e>1&&0!==t&&!a?p:i),c.tailShift=u-t),c.aggregate(this.remove(u)),a&&i!==p&&l===this.rawInputValue)if(i===g){let t;for(;l===this.rawInputValue&&(t=this.displayValue.length);)c.aggregate(new F({tailShift:-1})).aggregate(this.remove(t-1))}else i===v&&o.unshift();return c.aggregate(this.append(s,n,o))}maskEquals(t){return this.mask===t}typedValueEquals(t){const e=this.typedValue;return t===e||I.EMPTY_VALUES.includes(t)&&I.EMPTY_VALUES.includes(e)||!!this.format&&this.format(t,this)===this.format(this.typedValue,this)}}I.DEFAULTS={skipInvalid:!0},I.EMPTY_VALUES=[void 0,null,""],k.Masked=I;class D{constructor(t,e){void 0===t&&(t=[]),void 0===e&&(e=0),this.chunks=t,this.from=e}toString(){return this.chunks.map(String).join("")}extend(t){if(!String(t))return;t=c(t)?new T(String(t)):t;const e=this.chunks[this.chunks.length-1],s=e&&(e.stop===t.stop||null==t.stop)&&t.from===e.from+e.toString().length;if(t instanceof T)s?e.extend(t.toString()):this.chunks.push(t);else if(t instanceof D){if(null==t.stop){let e;for(;t.chunks.length&&null==t.chunks[0].stop;)e=t.chunks.shift(),e.from+=t.from,this.extend(e)}t.toString()&&(t.stop=t.blockIndex,this.chunks.push(t))}}appendTo(t){if(!(t instanceof k.MaskedPattern)){return new T(this.toString()).appendTo(t)}const e=new F;for(let s=0;s<this.chunks.length&&!e.skip;++s){const i=this.chunks[s],n=t._mapPosToBlock(t.displayValue.length),r=i.stop;let o;if(null!=r&&(!n||n.index<=r)){if(i instanceof D||t._stops.indexOf(r)>=0){const s=t._appendPlaceholder(r);e.aggregate(s)}o=i instanceof D&&t._blocks[r]}if(o){const s=o.appendTail(i);s.skip=!1,e.aggregate(s),t._value+=s.inserted;const n=i.toString().slice(s.rawInserted.length);n&&e.aggregate(t.append(n,{tail:!0}))}else e.aggregate(t.append(i.toString(),{tail:!0}))}return e}get state(){return{chunks:this.chunks.map((t=>t.state)),from:this.from,stop:this.stop,blockIndex:this.blockIndex}}set state(t){const{chunks:e,...s}=t;Object.assign(this,s),this.chunks=e.map((t=>{const e="chunks"in t?new D:new T;return e.state=t,e}))}unshift(t){if(!this.chunks.length||null!=t&&this.from>=t)return"";const e=null!=t?t-this.from:t;let s=0;for(;s<this.chunks.length;){const t=this.chunks[s],i=t.unshift(e);if(t.toString()){if(!i)break;++s}else this.chunks.splice(s,1);if(i)return i}return""}shift(){if(!this.chunks.length)return"";let t=this.chunks.length-1;for(;0<=t;){const e=this.chunks[t],s=e.shift();if(e.toString()){if(!s)break;--t}else this.chunks.splice(t,1);if(s)return s}return""}}class M{constructor(t,e){this.masked=t,this._log=[];const{offset:s,index:i}=t._mapPosToBlock(e)||(e<0?{index:0,offset:0}:{index:this.masked._blocks.length,offset:0});this.offset=s,this.index=i,this.ok=!1}get block(){return this.masked._blocks[this.index]}get pos(){return this.masked._blockStartPos(this.index)+this.offset}get state(){return{index:this.index,offset:this.offset,ok:this.ok}}set state(t){Object.assign(this,t)}pushState(){this._log.push(this.state)}popState(){const t=this._log.pop();return t&&(this.state=t),t}bindBlock(){this.block||(this.index<0&&(this.index=0,this.offset=0),this.index>=this.masked._blocks.length&&(this.index=this.masked._blocks.length-1,this.offset=this.block.displayValue.length))}_pushLeft(t){for(this.pushState(),this.bindBlock();0<=this.index;--this.index,this.offset=(null==(e=this.block)?void 0:e.displayValue.length)||0){var e;if(t())return this.ok=!0}return this.ok=!1}_pushRight(t){for(this.pushState(),this.bindBlock();this.index<this.masked._blocks.length;++this.index,this.offset=0)if(t())return this.ok=!0;return this.ok=!1}pushLeftBeforeFilled(){return this._pushLeft((()=>{if(!this.block.isFixed&&this.block.value)return this.offset=this.block.nearestInputPos(this.offset,g),0!==this.offset||void 0}))}pushLeftBeforeInput(){return this._pushLeft((()=>{if(!this.block.isFixed)return this.offset=this.block.nearestInputPos(this.offset,f),!0}))}pushLeftBeforeRequired(){return this._pushLeft((()=>{if(!(this.block.isFixed||this.block.isOptional&&!this.block.value))return this.offset=this.block.nearestInputPos(this.offset,f),!0}))}pushRightBeforeFilled(){return this._pushRight((()=>{if(!this.block.isFixed&&this.block.value)return this.offset=this.block.nearestInputPos(this.offset,v),this.offset!==this.block.value.length||void 0}))}pushRightBeforeInput(){return this._pushRight((()=>{if(!this.block.isFixed)return this.offset=this.block.nearestInputPos(this.offset,p),!0}))}pushRightBeforeRequired(){return this._pushRight((()=>{if(!(this.block.isFixed||this.block.isOptional&&!this.block.value))return this.offset=this.block.nearestInputPos(this.offset,p),!0}))}}class P{constructor(t){Object.assign(this,t),this._value="",this.isFixed=!0}get value(){return this._value}get unmaskedValue(){return this.isUnmasking?this.value:""}get rawInputValue(){return this._isRawInput?this.value:""}get displayValue(){return this.value}reset(){this._isRawInput=!1,this._value=""}remove(t,e){return void 0===t&&(t=0),void 0===e&&(e=this._value.length),this._value=this._value.slice(0,t)+this._value.slice(e),this._value||(this._isRawInput=!1),new F}nearestInputPos(t,e){void 0===e&&(e=p);const s=this._value.length;switch(e){case f:case g:return 0;default:return s}}totalInputPositions(t,e){return void 0===t&&(t=0),void 0===e&&(e=this._value.length),this._isRawInput?e-t:0}extractInput(t,e,s){return void 0===t&&(t=0),void 0===e&&(e=this._value.length),void 0===s&&(s={}),s.raw&&this._isRawInput&&this._value.slice(t,e)||""}get isComplete(){return!0}get isFilled(){return Boolean(this._value)}_appendChar(t,e){void 0===e&&(e={});const s=new F;if(this.isFilled)return s;const i=!0===this.eager||"append"===this.eager,n=this.char===t&&(this.isUnmasking||e.input||e.raw)&&(!e.raw||!i)&&!e.tail;return n&&(s.rawInserted=this.char),this._value=s.inserted=this.char,this._isRawInput=n&&(e.raw||e.input),s}_appendEager(){return this._appendChar(this.char,{tail:!0})}_appendPlaceholder(){const t=new F;return this.isFilled||(this._value=t.inserted=this.char),t}extractTail(){return new T("")}appendTail(t){return c(t)&&(t=new T(String(t))),t.appendTo(this)}append(t,e,s){const i=this._appendChar(t[0],e);return null!=s&&(i.tailShift+=this.appendTail(s).tailShift),i}doCommit(){}get state(){return{_value:this._value,_rawInputValue:this.rawInputValue}}set state(t){this._value=t._value,this._isRawInput=Boolean(t._rawInputValue)}}class L{constructor(t){const{parent:e,isOptional:s,placeholderChar:i,displayChar:n,lazy:r,eager:o,...a}=t;this.masked=C(a),Object.assign(this,{parent:e,isOptional:s,placeholderChar:i,displayChar:n,lazy:r,eager:o})}reset(){this.isFilled=!1,this.masked.reset()}remove(t,e){return void 0===t&&(t=0),void 0===e&&(e=this.value.length),0===t&&e>=1?(this.isFilled=!1,this.masked.remove(t,e)):new F}get value(){return this.masked.value||(this.isFilled&&!this.isOptional?this.placeholderChar:"")}get unmaskedValue(){return this.masked.unmaskedValue}get rawInputValue(){return this.masked.rawInputValue}get displayValue(){return this.masked.value&&this.displayChar||this.value}get isComplete(){return Boolean(this.masked.value)||this.isOptional}_appendChar(t,e){if(void 0===e&&(e={}),this.isFilled)return new F;const s=this.masked.state,i=this.masked._appendChar(t,this.currentMaskFlags(e));return i.inserted&&!1===this.doValidate(e)&&(i.inserted=i.rawInserted="",this.masked.state=s),i.inserted||this.isOptional||this.lazy||e.input||(i.inserted=this.placeholderChar),i.skip=!i.inserted&&!this.isOptional,this.isFilled=Boolean(i.inserted),i}append(t,e,s){return this.masked.append(t,this.currentMaskFlags(e),s)}_appendPlaceholder(){const t=new F;return this.isFilled||this.isOptional||(this.isFilled=!0,t.inserted=this.placeholderChar),t}_appendEager(){return new F}extractTail(t,e){return this.masked.extractTail(t,e)}appendTail(t){return this.masked.appendTail(t)}extractInput(t,e,s){return void 0===t&&(t=0),void 0===e&&(e=this.value.length),this.masked.extractInput(t,e,s)}nearestInputPos(t,e){void 0===e&&(e=p);const s=this.value.length,i=Math.min(Math.max(t,0),s);switch(e){case f:case g:return this.isComplete?i:0;case m:case v:return this.isComplete?i:s;default:return i}}totalInputPositions(t,e){return void 0===t&&(t=0),void 0===e&&(e=this.value.length),this.value.slice(t,e).length}doValidate(t){return this.masked.doValidate(this.currentMaskFlags(t))&&(!this.parent||this.parent.doValidate(this.currentMaskFlags(t)))}doCommit(){this.masked.doCommit()}get state(){return{_value:this.value,_rawInputValue:this.rawInputValue,masked:this.masked.state,isFilled:this.isFilled}}set state(t){this.masked.state=t.masked,this.isFilled=t.isFilled}currentMaskFlags(t){var e;return{...t,_beforeTailState:(null==t||null==(e=t._beforeTailState)?void 0:e.masked)||(null==t?void 0:t._beforeTailState)}}}L.DEFAULT_DEFINITIONS={0:/\d/,a:/[\u0041-\u005A\u0061-\u007A\u00AA\u00B5\u00BA\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0\u08A2-\u08AC\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0977\u0979-\u097F\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C33\u0C35-\u0C39\u0C3D\u0C58\u0C59\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D60\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F4\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191C\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19C1-\u19C7\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FCC\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA697\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA78E\uA790-\uA793\uA7A0-\uA7AA\uA7F8-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA80-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uABC0-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/,"*":/./};k.MaskedRegExp=class extends I{updateOptions(t){super.updateOptions(t)}_update(t){const e=t.mask;e&&(t.validate=t=>t.search(e)>=0),super._update(t)}};class B extends I{constructor(t){super({...B.DEFAULTS,...t,definitions:Object.assign({},L.DEFAULT_DEFINITIONS,null==t?void 0:t.definitions)})}updateOptions(t){super.updateOptions(t)}_update(t){t.definitions=Object.assign({},this.definitions,t.definitions),super._update(t),this._rebuildMask()}_rebuildMask(){const t=this.definitions;this._blocks=[],this.exposeBlock=void 0,this._stops=[],this._maskedBlocks={};const e=this.mask;if(!e||!t)return;let s=!1,i=!1;for(let n=0;n<e.length;++n){if(this.blocks){const t=e.slice(n),s=Object.keys(this.blocks).filter((e=>0===t.indexOf(e)));s.sort(((t,e)=>e.length-t.length));const i=s[0];if(i){const{expose:t,...e}=A(this.blocks[i]),s=C({lazy:this.lazy,eager:this.eager,placeholderChar:this.placeholderChar,displayChar:this.displayChar,overwrite:this.overwrite,...e,parent:this});s&&(this._blocks.push(s),t&&(this.exposeBlock=s),this._maskedBlocks[i]||(this._maskedBlocks[i]=[]),this._maskedBlocks[i].push(this._blocks.length-1)),n+=i.length-1;continue}}let r=e[n],o=r in t;if(r===B.STOP_CHAR){this._stops.push(this._blocks.length);continue}if("{"===r||"}"===r){s=!s;continue}if("["===r||"]"===r){i=!i;continue}if(r===B.ESCAPE_CHAR){if(++n,r=e[n],!r)break;o=!1}const a=o?new L({isOptional:i,lazy:this.lazy,eager:this.eager,placeholderChar:this.placeholderChar,displayChar:this.displayChar,...A(t[r]),parent:this}):new P({char:r,eager:this.eager,isUnmasking:s});this._blocks.push(a)}}get state(){return{...super.state,_blocks:this._blocks.map((t=>t.state))}}set state(t){const{_blocks:e,...s}=t;this._blocks.forEach(((t,s)=>t.state=e[s])),super.state=s}reset(){super.reset(),this._blocks.forEach((t=>t.reset()))}get isComplete(){return this.exposeBlock?this.exposeBlock.isComplete:this._blocks.every((t=>t.isComplete))}get isFilled(){return this._blocks.every((t=>t.isFilled))}get isFixed(){return this._blocks.every((t=>t.isFixed))}get isOptional(){return this._blocks.every((t=>t.isOptional))}doCommit(){this._blocks.forEach((t=>t.doCommit())),super.doCommit()}get unmaskedValue(){return this.exposeBlock?this.exposeBlock.unmaskedValue:this._blocks.reduce(((t,e)=>t+e.unmaskedValue),"")}set unmaskedValue(t){if(this.exposeBlock){const e=this.extractTail(this._blockStartPos(this._blocks.indexOf(this.exposeBlock))+this.exposeBlock.displayValue.length);this.exposeBlock.unmaskedValue=t,this.appendTail(e),this.doCommit()}else super.unmaskedValue=t}get value(){return this.exposeBlock?this.exposeBlock.value:this._blocks.reduce(((t,e)=>t+e.value),"")}set value(t){if(this.exposeBlock){const e=this.extractTail(this._blockStartPos(this._blocks.indexOf(this.exposeBlock))+this.exposeBlock.displayValue.length);this.exposeBlock.value=t,this.appendTail(e),this.doCommit()}else super.value=t}get typedValue(){return this.exposeBlock?this.exposeBlock.typedValue:super.typedValue}set typedValue(t){if(this.exposeBlock){const e=this.extractTail(this._blockStartPos(this._blocks.indexOf(this.exposeBlock))+this.exposeBlock.displayValue.length);this.exposeBlock.typedValue=t,this.appendTail(e),this.doCommit()}else super.typedValue=t}get displayValue(){return this._blocks.reduce(((t,e)=>t+e.displayValue),"")}appendTail(t){return super.appendTail(t).aggregate(this._appendPlaceholder())}_appendEager(){var t;const e=new F;let s=null==(t=this._mapPosToBlock(this.displayValue.length))?void 0:t.index;if(null==s)return e;this._blocks[s].isFilled&&++s;for(let t=s;t<this._blocks.length;++t){const s=this._blocks[t]._appendEager();if(!s.inserted)break;e.aggregate(s)}return e}_appendCharRaw(t,e){void 0===e&&(e={});const s=this._mapPosToBlock(this.displayValue.length),i=new F;if(!s)return i;for(let r=s.index;;++r){var n;const s=this._blocks[r];if(!s)break;const o=s._appendChar(t,{...e,_beforeTailState:null==(n=e._beforeTailState)||null==(n=n._blocks)?void 0:n[r]}),a=o.skip;if(i.aggregate(o),a||o.rawInserted)break}return i}extractTail(t,e){void 0===t&&(t=0),void 0===e&&(e=this.displayValue.length);const s=new D;return t===e||this._forEachBlocksInRange(t,e,((t,e,i,n)=>{const r=t.extractTail(i,n);r.stop=this._findStopBefore(e),r.from=this._blockStartPos(e),r instanceof D&&(r.blockIndex=e),s.extend(r)})),s}extractInput(t,e,s){if(void 0===t&&(t=0),void 0===e&&(e=this.displayValue.length),void 0===s&&(s={}),t===e)return"";let i="";return this._forEachBlocksInRange(t,e,((t,e,n,r)=>{i+=t.extractInput(n,r,s)})),i}_findStopBefore(t){let e;for(let s=0;s<this._stops.length;++s){const i=this._stops[s];if(!(i<=t))break;e=i}return e}_appendPlaceholder(t){const e=new F;if(this.lazy&&null==t)return e;const s=this._mapPosToBlock(this.displayValue.length);if(!s)return e;const i=s.index,n=null!=t?t:this._blocks.length;return this._blocks.slice(i,n).forEach((s=>{if(!s.lazy||null!=t){var i;const t=s._appendPlaceholder(null==(i=s._blocks)?void 0:i.length);this._value+=t.inserted,e.aggregate(t)}})),e}_mapPosToBlock(t){let e="";for(let s=0;s<this._blocks.length;++s){const i=this._blocks[s],n=e.length;if(e+=i.displayValue,t<=e.length)return{index:s,offset:t-n}}}_blockStartPos(t){return this._blocks.slice(0,t).reduce(((t,e)=>t+e.displayValue.length),0)}_forEachBlocksInRange(t,e,s){void 0===e&&(e=this.displayValue.length);const i=this._mapPosToBlock(t);if(i){const t=this._mapPosToBlock(e),n=t&&i.index===t.index,r=i.offset,o=t&&n?t.offset:this._blocks[i.index].displayValue.length;if(s(this._blocks[i.index],i.index,r,o),t&&!n){for(let e=i.index+1;e<t.index;++e)s(this._blocks[e],e,0,this._blocks[e].displayValue.length);s(this._blocks[t.index],t.index,0,t.offset)}}}remove(t,e){void 0===t&&(t=0),void 0===e&&(e=this.displayValue.length);const s=super.remove(t,e);return this._forEachBlocksInRange(t,e,((t,e,i,n)=>{s.aggregate(t.remove(i,n))})),s}nearestInputPos(t,e){if(void 0===e&&(e=p),!this._blocks.length)return 0;const s=new M(this,t);if(e===p)return s.pushRightBeforeInput()?s.pos:(s.popState(),s.pushLeftBeforeInput()?s.pos:this.displayValue.length);if(e===f||e===g){if(e===f){if(s.pushRightBeforeFilled(),s.ok&&s.pos===t)return t;s.popState()}if(s.pushLeftBeforeInput(),s.pushLeftBeforeRequired(),s.pushLeftBeforeFilled(),e===f){if(s.pushRightBeforeInput(),s.pushRightBeforeRequired(),s.ok&&s.pos<=t)return s.pos;if(s.popState(),s.ok&&s.pos<=t)return s.pos;s.popState()}return s.ok?s.pos:e===g?0:(s.popState(),s.ok?s.pos:(s.popState(),s.ok?s.pos:0))}return e===m||e===v?(s.pushRightBeforeInput(),s.pushRightBeforeRequired(),s.pushRightBeforeFilled()?s.pos:e===v?this.displayValue.length:(s.popState(),s.ok?s.pos:(s.popState(),s.ok?s.pos:this.nearestInputPos(t,f)))):t}totalInputPositions(t,e){void 0===t&&(t=0),void 0===e&&(e=this.displayValue.length);let s=0;return this._forEachBlocksInRange(t,e,((t,e,i,n)=>{s+=t.totalInputPositions(i,n)})),s}maskedBlock(t){return this.maskedBlocks(t)[0]}maskedBlocks(t){const e=this._maskedBlocks[t];return e?e.map((t=>this._blocks[t])):[]}}B.DEFAULTS={lazy:!0,placeholderChar:"_"},B.STOP_CHAR="`",B.ESCAPE_CHAR="\\",B.InputDefinition=L,B.FixedDefinition=P,k.MaskedPattern=B;class V extends B{get _matchFrom(){return this.maxLength-String(this.from).length}constructor(t){super(t)}updateOptions(t){super.updateOptions(t)}_update(t){const{to:e=this.to||0,from:s=this.from||0,maxLength:i=this.maxLength||0,autofix:n=this.autofix,...r}=t;this.to=e,this.from=s,this.maxLength=Math.max(String(e).length,i),this.autofix=n;const o=String(this.from).padStart(this.maxLength,"0"),a=String(this.to).padStart(this.maxLength,"0");let l=0;for(;l<a.length&&a[l]===o[l];)++l;r.mask=a.slice(0,l).replace(/0/g,"\\0")+"0".repeat(this.maxLength-l),super._update(r)}get isComplete(){return super.isComplete&&Boolean(this.value)}boundaries(t){let e="",s="";const[,i,n]=t.match(/^(\D*)(\d*)(\D*)/)||[];return n&&(e="0".repeat(i.length)+n,s="9".repeat(i.length)+n),e=e.padEnd(this.maxLength,"0"),s=s.padEnd(this.maxLength,"9"),[e,s]}doPrepareChar(t,e){let s;if(void 0===e&&(e={}),[t,s]=super.doPrepareChar(t.replace(/\D/g,""),e),!this.autofix||!t)return[t,s];const i=String(this.from).padStart(this.maxLength,"0"),n=String(this.to).padStart(this.maxLength,"0"),r=this.value+t;if(r.length>this.maxLength)return["",s];const[o,a]=this.boundaries(r);return Number(a)<this.from?[i[r.length-1],s]:Number(o)>this.to?"pad"===this.autofix&&r.length<this.maxLength?["",s.aggregate(this.append(i[r.length-1]+t,e))]:[n[r.length-1],s]:[t,s]}doValidate(t){const e=this.value;if(-1===e.search(/[^0]/)&&e.length<=this._matchFrom)return!0;const[s,i]=this.boundaries(e);return this.from<=Number(i)&&Number(s)<=this.to&&super.doValidate(t)}}k.MaskedRange=V;class N extends B{constructor(t){const{mask:e,pattern:s,...i}={...N.DEFAULTS,...t};super({...i,mask:c(e)?e:s})}updateOptions(t){super.updateOptions(t)}_update(t){const{mask:e,pattern:s,blocks:i,...n}={...N.DEFAULTS,...t},r=Object.assign({},N.GET_DEFAULT_BLOCKS());t.min&&(r.Y.from=t.min.getFullYear()),t.max&&(r.Y.to=t.max.getFullYear()),t.min&&t.max&&r.Y.from===r.Y.to&&(r.m.from=t.min.getMonth()+1,r.m.to=t.max.getMonth()+1,r.m.from===r.m.to&&(r.d.from=t.min.getDate(),r.d.to=t.max.getDate())),Object.assign(r,this.blocks,i),Object.keys(r).forEach((e=>{const s=r[e];!("autofix"in s)&&"autofix"in t&&(s.autofix=t.autofix)})),super._update({...n,mask:c(e)?e:s,blocks:r})}doValidate(t){const e=this.date;return super.doValidate(t)&&(!this.isComplete||this.isDateExist(this.value)&&null!=e&&(null==this.min||this.min<=e)&&(null==this.max||e<=this.max))}isDateExist(t){return this.format(this.parse(t,this),this).indexOf(t)>=0}get date(){return this.typedValue}set date(t){this.typedValue=t}get typedValue(){return this.isComplete?super.typedValue:null}set typedValue(t){super.typedValue=t}maskEquals(t){return t===Date||super.maskEquals(t)}}N.GET_DEFAULT_BLOCKS=()=>({d:{mask:V,from:1,to:31,maxLength:2},m:{mask:V,from:1,to:12,maxLength:2},Y:{mask:V,from:1900,to:9999}}),N.DEFAULTS={mask:Date,pattern:"d{.}`m{.}`Y",format:(t,e)=>{if(!t)return"";return[String(t.getDate()).padStart(2,"0"),String(t.getMonth()+1).padStart(2,"0"),t.getFullYear()].join(".")},parse:(t,e)=>{const[s,i,n]=t.split(".").map(Number);return new Date(n,i-1,s)}},k.MaskedDate=N;class j extends I{constructor(t){super({...j.DEFAULTS,...t}),this.currentMask=void 0}updateOptions(t){super.updateOptions(t)}_update(t){super._update(t),"mask"in t&&(this.exposeMask=void 0,this.compiledMasks=Array.isArray(t.mask)?t.mask.map((t=>{const{expose:e,...s}=A(t),i=C({overwrite:this._overwrite,eager:this._eager,skipInvalid:this._skipInvalid,...s});return e&&(this.exposeMask=i),i})):[])}_appendCharRaw(t,e){void 0===e&&(e={});const s=this._applyDispatch(t,e);return this.currentMask&&s.aggregate(this.currentMask._appendChar(t,this.currentMaskFlags(e))),s}_applyDispatch(t,e,s){void 0===t&&(t=""),void 0===e&&(e={}),void 0===s&&(s="");const i=e.tail&&null!=e._beforeTailState?e._beforeTailState._value:this.value,n=this.rawInputValue,r=e.tail&&null!=e._beforeTailState?e._beforeTailState._rawInputValue:n,o=n.slice(r.length),a=this.currentMask,l=new F,u=null==a?void 0:a.state;if(this.currentMask=this.doDispatch(t,{...e},s),this.currentMask)if(this.currentMask!==a){if(this.currentMask.reset(),r){const t=this.currentMask.append(r,{raw:!0});l.tailShift=t.inserted.length-i.length}o&&(l.tailShift+=this.currentMask.append(o,{raw:!0,tail:!0}).tailShift)}else u&&(this.currentMask.state=u);return l}_appendPlaceholder(){const t=this._applyDispatch();return this.currentMask&&t.aggregate(this.currentMask._appendPlaceholder()),t}_appendEager(){const t=this._applyDispatch();return this.currentMask&&t.aggregate(this.currentMask._appendEager()),t}appendTail(t){const e=new F;return t&&e.aggregate(this._applyDispatch("",{},t)),e.aggregate(this.currentMask?this.currentMask.appendTail(t):super.appendTail(t))}currentMaskFlags(t){var e,s;return{...t,_beforeTailState:(null==(e=t._beforeTailState)?void 0:e.currentMaskRef)===this.currentMask&&(null==(s=t._beforeTailState)?void 0:s.currentMask)||t._beforeTailState}}doDispatch(t,e,s){return void 0===e&&(e={}),void 0===s&&(s=""),this.dispatch(t,this,e,s)}doValidate(t){return super.doValidate(t)&&(!this.currentMask||this.currentMask.doValidate(this.currentMaskFlags(t)))}doPrepare(t,e){void 0===e&&(e={});let[s,i]=super.doPrepare(t,e);if(this.currentMask){let t;[s,t]=super.doPrepare(s,this.currentMaskFlags(e)),i=i.aggregate(t)}return[s,i]}doPrepareChar(t,e){void 0===e&&(e={});let[s,i]=super.doPrepareChar(t,e);if(this.currentMask){let t;[s,t]=super.doPrepareChar(s,this.currentMaskFlags(e)),i=i.aggregate(t)}return[s,i]}reset(){var t;null==(t=this.currentMask)||t.reset(),this.compiledMasks.forEach((t=>t.reset()))}get value(){return this.exposeMask?this.exposeMask.value:this.currentMask?this.currentMask.value:""}set value(t){this.exposeMask?(this.exposeMask.value=t,this.currentMask=this.exposeMask,this._applyDispatch()):super.value=t}get unmaskedValue(){return this.exposeMask?this.exposeMask.unmaskedValue:this.currentMask?this.currentMask.unmaskedValue:""}set unmaskedValue(t){this.exposeMask?(this.exposeMask.unmaskedValue=t,this.currentMask=this.exposeMask,this._applyDispatch()):super.unmaskedValue=t}get typedValue(){return this.exposeMask?this.exposeMask.typedValue:this.currentMask?this.currentMask.typedValue:""}set typedValue(t){if(this.exposeMask)return this.exposeMask.typedValue=t,this.currentMask=this.exposeMask,void this._applyDispatch();let e=String(t);this.currentMask&&(this.currentMask.typedValue=t,e=this.currentMask.unmaskedValue),this.unmaskedValue=e}get displayValue(){return this.currentMask?this.currentMask.displayValue:""}get isComplete(){var t;return Boolean(null==(t=this.currentMask)?void 0:t.isComplete)}get isFilled(){var t;return Boolean(null==(t=this.currentMask)?void 0:t.isFilled)}remove(t,e){const s=new F;return this.currentMask&&s.aggregate(this.currentMask.remove(t,e)).aggregate(this._applyDispatch()),s}get state(){var t;return{...super.state,_rawInputValue:this.rawInputValue,compiledMasks:this.compiledMasks.map((t=>t.state)),currentMaskRef:this.currentMask,currentMask:null==(t=this.currentMask)?void 0:t.state}}set state(t){const{compiledMasks:e,currentMaskRef:s,currentMask:i,...n}=t;e&&this.compiledMasks.forEach(((t,s)=>t.state=e[s])),null!=s&&(this.currentMask=s,this.currentMask.state=i),super.state=n}extractInput(t,e,s){return this.currentMask?this.currentMask.extractInput(t,e,s):""}extractTail(t,e){return this.currentMask?this.currentMask.extractTail(t,e):super.extractTail(t,e)}doCommit(){this.currentMask&&this.currentMask.doCommit(),super.doCommit()}nearestInputPos(t,e){return this.currentMask?this.currentMask.nearestInputPos(t,e):super.nearestInputPos(t,e)}get overwrite(){return this.currentMask?this.currentMask.overwrite:this._overwrite}set overwrite(t){this._overwrite=t}get eager(){return this.currentMask?this.currentMask.eager:this._eager}set eager(t){this._eager=t}get skipInvalid(){return this.currentMask?this.currentMask.skipInvalid:this._skipInvalid}set skipInvalid(t){this._skipInvalid=t}maskEquals(t){return Array.isArray(t)?this.compiledMasks.every(((e,s)=>{if(!t[s])return;const{mask:i,...n}=t[s];return b(e,n)&&e.maskEquals(i)})):super.maskEquals(t)}typedValueEquals(t){var e;return Boolean(null==(e=this.currentMask)?void 0:e.typedValueEquals(t))}}j.DEFAULTS=void 0,j.DEFAULTS={dispatch:(t,e,s,i)=>{if(!e.compiledMasks.length)return;const n=e.rawInputValue,r=e.compiledMasks.map(((r,o)=>{const a=e.currentMask===r,l=a?r.displayValue.length:r.nearestInputPos(r.displayValue.length,g);return r.rawInputValue!==n?(r.reset(),r.append(n,{raw:!0})):a||r.remove(l),r.append(t,e.currentMaskFlags(s)),r.appendTail(i),{index:o,weight:r.rawInputValue.length,totalInputPositions:r.totalInputPositions(0,Math.max(l,r.nearestInputPos(r.displayValue.length,g)))}}));return r.sort(((t,e)=>e.weight-t.weight||e.totalInputPositions-t.totalInputPositions)),e.compiledMasks[r[0].index]}},k.MaskedDynamic=j;k.MaskedEnum=class extends B{constructor(t){super(t)}updateOptions(t){super.updateOptions(t)}_update(t){const{enum:e,...s}=t;if(e){const t=e.map((t=>t.length)),i=Math.min(...t),n=Math.max(...t)-i;s.mask="*".repeat(i),n&&(s.mask+="["+"*".repeat(n)+"]"),this.enum=e}super._update(s)}doValidate(t){return this.enum.some((t=>0===t.indexOf(this.unmaskedValue)))&&super.doValidate(t)}};k.MaskedFunction=class extends I{updateOptions(t){super.updateOptions(t)}_update(t){super._update({...t,validate:t.mask})}};class R extends I{constructor(t){super({...R.DEFAULTS,...t})}updateOptions(t){super.updateOptions(t)}_update(t){super._update(t),this._updateRegExps()}_updateRegExps(){const t="^"+(this.allowNegative?"[+|\\-]?":""),e=(this.scale?"("+_(this.radix)+"\\d{0,"+this.scale+"})?":"")+"$";this._numberRegExp=new RegExp(t+"\\d*"+e),this._mapToRadixRegExp=new RegExp("["+this.mapToRadix.map(_).join("")+"]","g"),this._thousandsSeparatorRegExp=new RegExp(_(this.thousandsSeparator),"g")}_removeThousandsSeparators(t){return t.replace(this._thousandsSeparatorRegExp,"")}_insertThousandsSeparators(t){const e=t.split(this.radix);return e[0]=e[0].replace(/\B(?=(\d{3})+(?!\d))/g,this.thousandsSeparator),e.join(this.radix)}doPrepareChar(t,e){void 0===e&&(e={});const[s,i]=super.doPrepareChar(this._removeThousandsSeparators(this.scale&&this.mapToRadix.length&&(e.input&&e.raw||!e.input&&!e.raw)?t.replace(this._mapToRadixRegExp,this.radix):t),e);return t&&!s&&(i.skip=!0),!s||this.allowPositive||this.value||"-"===s||i.aggregate(this._appendChar("-")),[s,i]}_separatorsCount(t,e){void 0===e&&(e=!1);let s=0;for(let i=0;i<t;++i)this._value.indexOf(this.thousandsSeparator,i)===i&&(++s,e&&(t+=this.thousandsSeparator.length));return s}_separatorsCountFromSlice(t){return void 0===t&&(t=this._value),this._separatorsCount(this._removeThousandsSeparators(t).length,!0)}extractInput(t,e,s){return void 0===t&&(t=0),void 0===e&&(e=this.displayValue.length),[t,e]=this._adjustRangeWithSeparators(t,e),this._removeThousandsSeparators(super.extractInput(t,e,s))}_appendCharRaw(t,e){if(void 0===e&&(e={}),!this.thousandsSeparator)return super._appendCharRaw(t,e);const s=e.tail&&e._beforeTailState?e._beforeTailState._value:this._value,i=this._separatorsCountFromSlice(s);this._value=this._removeThousandsSeparators(this.value);const n=super._appendCharRaw(t,e);this._value=this._insertThousandsSeparators(this._value);const r=e.tail&&e._beforeTailState?e._beforeTailState._value:this._value,o=this._separatorsCountFromSlice(r);return n.tailShift+=(o-i)*this.thousandsSeparator.length,n.skip=!n.rawInserted&&t===this.thousandsSeparator,n}_findSeparatorAround(t){if(this.thousandsSeparator){const e=t-this.thousandsSeparator.length+1,s=this.value.indexOf(this.thousandsSeparator,e);if(s<=t)return s}return-1}_adjustRangeWithSeparators(t,e){const s=this._findSeparatorAround(t);s>=0&&(t=s);const i=this._findSeparatorAround(e);return i>=0&&(e=i+this.thousandsSeparator.length),[t,e]}remove(t,e){void 0===t&&(t=0),void 0===e&&(e=this.displayValue.length),[t,e]=this._adjustRangeWithSeparators(t,e);const s=this.value.slice(0,t),i=this.value.slice(e),n=this._separatorsCount(s.length);this._value=this._insertThousandsSeparators(this._removeThousandsSeparators(s+i));const r=this._separatorsCountFromSlice(s);return new F({tailShift:(r-n)*this.thousandsSeparator.length})}nearestInputPos(t,e){if(!this.thousandsSeparator)return t;switch(e){case p:case f:case g:{const s=this._findSeparatorAround(t-1);if(s>=0){const i=s+this.thousandsSeparator.length;if(t<i||this.value.length<=i||e===g)return s}break}case m:case v:{const e=this._findSeparatorAround(t);if(e>=0)return e+this.thousandsSeparator.length}}return t}doValidate(t){let e=Boolean(this._removeThousandsSeparators(this.value).match(this._numberRegExp));if(e){const t=this.number;e=e&&!isNaN(t)&&(null==this.min||this.min>=0||this.min<=this.number)&&(null==this.max||this.max<=0||this.number<=this.max)}return e&&super.doValidate(t)}doCommit(){if(this.value){const t=this.number;let e=t;null!=this.min&&(e=Math.max(e,this.min)),null!=this.max&&(e=Math.min(e,this.max)),e!==t&&(this.unmaskedValue=this.format(e,this));let s=this.value;this.normalizeZeros&&(s=this._normalizeZeros(s)),this.padFractionalZeros&&this.scale>0&&(s=this._padFractionalZeros(s)),this._value=s}super.doCommit()}_normalizeZeros(t){const e=this._removeThousandsSeparators(t).split(this.radix);return e[0]=e[0].replace(/^(\D*)(0*)(\d*)/,((t,e,s,i)=>e+i)),t.length&&!/\d$/.test(e[0])&&(e[0]=e[0]+"0"),e.length>1&&(e[1]=e[1].replace(/0*$/,""),e[1].length||(e.length=1)),this._insertThousandsSeparators(e.join(this.radix))}_padFractionalZeros(t){if(!t)return t;const e=t.split(this.radix);return e.length<2&&e.push(""),e[1]=e[1].padEnd(this.scale,"0"),e.join(this.radix)}doSkipInvalid(t,e,s){void 0===e&&(e={});const i=0===this.scale&&t!==this.thousandsSeparator&&(t===this.radix||t===R.UNMASKED_RADIX||this.mapToRadix.includes(t));return super.doSkipInvalid(t,e,s)&&!i}get unmaskedValue(){return this._removeThousandsSeparators(this._normalizeZeros(this.value)).replace(this.radix,R.UNMASKED_RADIX)}set unmaskedValue(t){super.unmaskedValue=t}get typedValue(){return this.parse(this.unmaskedValue,this)}set typedValue(t){this.rawInputValue=this.format(t,this).replace(R.UNMASKED_RADIX,this.radix)}get number(){return this.typedValue}set number(t){this.typedValue=t}get allowNegative(){return null!=this.min&&this.min<0||null!=this.max&&this.max<0}get allowPositive(){return null!=this.min&&this.min>0||null!=this.max&&this.max>0}typedValueEquals(t){return(super.typedValueEquals(t)||R.EMPTY_VALUES.includes(t)&&R.EMPTY_VALUES.includes(this.typedValue))&&!(0===t&&""===this.value)}}R.UNMASKED_RADIX=".",R.EMPTY_VALUES=[...I.EMPTY_VALUES,0],R.DEFAULTS={mask:Number,radix:",",thousandsSeparator:"",mapToRadix:[R.UNMASKED_RADIX],min:Number.MIN_SAFE_INTEGER,max:Number.MAX_SAFE_INTEGER,scale:2,normalizeZeros:!0,padFractionalZeros:!1,parse:Number,format:t=>t.toLocaleString("en-US",{useGrouping:!1,maximumFractionDigits:20})},k.MaskedNumber=R;const q={MASKED:"value",UNMASKED:"unmaskedValue",TYPED:"typedValue"};function z(t,e,s){void 0===e&&(e=q.MASKED),void 0===s&&(s=q.MASKED);const i=C(t);return t=>i.runIsolated((i=>(i[e]=t,i[s])))}k.PIPE_TYPE=q,k.createPipe=z,k.pipe=function(t,e,s,i){return z(e,s,i)(t)};try{globalThis.IMask=k}catch{}[].slice.call(document.querySelectorAll("[data-mask]")).map((function(t){return new k(t,{mask:t.dataset.mask,lazy:"true"===t.dataset["mask-visible"]})}));var H="top",U="bottom",W="right",K="left",Y="auto",Q=[H,U,W,K],X="start",G="end",Z="clippingParents",J="viewport",tt="popper",et="reference",st=Q.reduce((function(t,e){return t.concat([e+"-"+X,e+"-"+G])}),[]),it=[].concat(Q,[Y]).reduce((function(t,e){return t.concat([e,e+"-"+X,e+"-"+G])}),[]),nt="beforeRead",rt="read",ot="afterRead",at="beforeMain",lt="main",ut="afterMain",ct="beforeWrite",ht="write",dt="afterWrite",pt=[nt,rt,ot,at,lt,ut,ct,ht,dt];function ft(t){return t?(t.nodeName||"").toLowerCase():null}function gt(t){if(null==t)return window;if("[object Window]"!==t.toString()){var e=t.ownerDocument;return e&&e.defaultView||window}return t}function mt(t){return t instanceof gt(t).Element||t instanceof Element}function vt(t){return t instanceof gt(t).HTMLElement||t instanceof HTMLElement}function _t(t){return"undefined"!=typeof ShadowRoot&&(t instanceof gt(t).ShadowRoot||t instanceof ShadowRoot)}const bt={name:"applyStyles",enabled:!0,phase:"write",fn:function(t){var e=t.state;Object.keys(e.elements).forEach((function(t){var s=e.styles[t]||{},i=e.attributes[t]||{},n=e.elements[t];vt(n)&&ft(n)&&(Object.assign(n.style,s),Object.keys(i).forEach((function(t){var e=i[t];!1===e?n.removeAttribute(t):n.setAttribute(t,!0===e?"":e)})))}))},effect:function(t){var e=t.state,s={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(e.elements.popper.style,s.popper),e.styles=s,e.elements.arrow&&Object.assign(e.elements.arrow.style,s.arrow),function(){Object.keys(e.elements).forEach((function(t){var i=e.elements[t],n=e.attributes[t]||{},r=Object.keys(e.styles.hasOwnProperty(t)?e.styles[t]:s[t]).reduce((function(t,e){return t[e]="",t}),{});vt(i)&&ft(i)&&(Object.assign(i.style,r),Object.keys(n).forEach((function(t){i.removeAttribute(t)})))}))}},requires:["computeStyles"]};function yt(t){return t.split("-")[0]}var kt=Math.max,wt=Math.min,At=Math.round;function Ct(){var t=navigator.userAgentData;return null!=t&&t.brands&&Array.isArray(t.brands)?t.brands.map((function(t){return t.brand+"/"+t.version})).join(" "):navigator.userAgent}function Et(){return!/^((?!chrome|android).)*safari/i.test(Ct())}function xt(t,e,s){void 0===e&&(e=!1),void 0===s&&(s=!1);var i=t.getBoundingClientRect(),n=1,r=1;e&&vt(t)&&(n=t.offsetWidth>0&&At(i.width)/t.offsetWidth||1,r=t.offsetHeight>0&&At(i.height)/t.offsetHeight||1);var o=(mt(t)?gt(t):window).visualViewport,a=!Et()&&s,l=(i.left+(a&&o?o.offsetLeft:0))/n,u=(i.top+(a&&o?o.offsetTop:0))/r,c=i.width/n,h=i.height/r;return{width:c,height:h,top:u,right:l+c,bottom:u+h,left:l,x:l,y:u}}function St(t){var e=xt(t),s=t.offsetWidth,i=t.offsetHeight;return Math.abs(e.width-s)<=1&&(s=e.width),Math.abs(e.height-i)<=1&&(i=e.height),{x:t.offsetLeft,y:t.offsetTop,width:s,height:i}}function Ot(t,e){var s=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(s&&_t(s)){var i=e;do{if(i&&t.isSameNode(i))return!0;i=i.parentNode||i.host}while(i)}return!1}function Ft(t){return gt(t).getComputedStyle(t)}function Tt(t){return["table","td","th"].indexOf(ft(t))>=0}function It(t){return((mt(t)?t.ownerDocument:t.document)||window.document).documentElement}function Dt(t){return"html"===ft(t)?t:t.assignedSlot||t.parentNode||(_t(t)?t.host:null)||It(t)}function Mt(t){return vt(t)&&"fixed"!==Ft(t).position?t.offsetParent:null}function Pt(t){for(var e=gt(t),s=Mt(t);s&&Tt(s)&&"static"===Ft(s).position;)s=Mt(s);return s&&("html"===ft(s)||"body"===ft(s)&&"static"===Ft(s).position)?e:s||function(t){var e=/firefox/i.test(Ct());if(/Trident/i.test(Ct())&&vt(t)&&"fixed"===Ft(t).position)return null;var s=Dt(t);for(_t(s)&&(s=s.host);vt(s)&&["html","body"].indexOf(ft(s))<0;){var i=Ft(s);if("none"!==i.transform||"none"!==i.perspective||"paint"===i.contain||-1!==["transform","perspective"].indexOf(i.willChange)||e&&"filter"===i.willChange||e&&i.filter&&"none"!==i.filter)return s;s=s.parentNode}return null}(t)||e}function Lt(t){return["top","bottom"].indexOf(t)>=0?"x":"y"}function Bt(t,e,s){return kt(t,wt(e,s))}function Vt(t){return Object.assign({},{top:0,right:0,bottom:0,left:0},t)}function Nt(t,e){return e.reduce((function(e,s){return e[s]=t,e}),{})}const $t={name:"arrow",enabled:!0,phase:"main",fn:function(t){var e,s=t.state,i=t.name,n=t.options,r=s.elements.arrow,o=s.modifiersData.popperOffsets,a=yt(s.placement),l=Lt(a),u=[K,W].indexOf(a)>=0?"height":"width";if(r&&o){var c=function(t,e){return Vt("number"!=typeof(t="function"==typeof t?t(Object.assign({},e.rects,{placement:e.placement})):t)?t:Nt(t,Q))}(n.padding,s),h=St(r),d="y"===l?H:K,p="y"===l?U:W,f=s.rects.reference[u]+s.rects.reference[l]-o[l]-s.rects.popper[u],g=o[l]-s.rects.reference[l],m=Pt(r),v=m?"y"===l?m.clientHeight||0:m.clientWidth||0:0,_=f/2-g/2,b=c[d],y=v-h[u]-c[p],k=v/2-h[u]/2+_,w=Bt(b,k,y),A=l;s.modifiersData[i]=((e={})[A]=w,e.centerOffset=w-k,e)}},effect:function(t){var e=t.state,s=t.options.element,i=void 0===s?"[data-popper-arrow]":s;null!=i&&("string"!=typeof i||(i=e.elements.popper.querySelector(i)))&&Ot(e.elements.popper,i)&&(e.elements.arrow=i)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]};function jt(t){return t.split("-")[1]}var Rt={top:"auto",right:"auto",bottom:"auto",left:"auto"};function qt(t){var e,s=t.popper,i=t.popperRect,n=t.placement,r=t.variation,o=t.offsets,a=t.position,l=t.gpuAcceleration,u=t.adaptive,c=t.roundOffsets,h=t.isFixed,d=o.x,p=void 0===d?0:d,f=o.y,g=void 0===f?0:f,m="function"==typeof c?c({x:p,y:g}):{x:p,y:g};p=m.x,g=m.y;var v=o.hasOwnProperty("x"),_=o.hasOwnProperty("y"),b=K,y=H,k=window;if(u){var w=Pt(s),A="clientHeight",C="clientWidth";if(w===gt(s)&&"static"!==Ft(w=It(s)).position&&"absolute"===a&&(A="scrollHeight",C="scrollWidth"),n===H||(n===K||n===W)&&r===G)y=U,g-=(h&&w===k&&k.visualViewport?k.visualViewport.height:w[A])-i.height,g*=l?1:-1;if(n===K||(n===H||n===U)&&r===G)b=W,p-=(h&&w===k&&k.visualViewport?k.visualViewport.width:w[C])-i.width,p*=l?1:-1}var E,x=Object.assign({position:a},u&&Rt),S=!0===c?function(t,e){var s=t.x,i=t.y,n=e.devicePixelRatio||1;return{x:At(s*n)/n||0,y:At(i*n)/n||0}}({x:p,y:g},gt(s)):{x:p,y:g};return p=S.x,g=S.y,l?Object.assign({},x,((E={})[y]=_?"0":"",E[b]=v?"0":"",E.transform=(k.devicePixelRatio||1)<=1?"translate("+p+"px, "+g+"px)":"translate3d("+p+"px, "+g+"px, 0)",E)):Object.assign({},x,((e={})[y]=_?g+"px":"",e[b]=v?p+"px":"",e.transform="",e))}const zt={name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(t){var e=t.state,s=t.options,i=s.gpuAcceleration,n=void 0===i||i,r=s.adaptive,o=void 0===r||r,a=s.roundOffsets,l=void 0===a||a,u={placement:yt(e.placement),variation:jt(e.placement),popper:e.elements.popper,popperRect:e.rects.popper,gpuAcceleration:n,isFixed:"fixed"===e.options.strategy};null!=e.modifiersData.popperOffsets&&(e.styles.popper=Object.assign({},e.styles.popper,qt(Object.assign({},u,{offsets:e.modifiersData.popperOffsets,position:e.options.strategy,adaptive:o,roundOffsets:l})))),null!=e.modifiersData.arrow&&(e.styles.arrow=Object.assign({},e.styles.arrow,qt(Object.assign({},u,{offsets:e.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:l})))),e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-placement":e.placement})},data:{}};var Ht={passive:!0};const Ut={name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(t){var e=t.state,s=t.instance,i=t.options,n=i.scroll,r=void 0===n||n,o=i.resize,a=void 0===o||o,l=gt(e.elements.popper),u=[].concat(e.scrollParents.reference,e.scrollParents.popper);return r&&u.forEach((function(t){t.addEventListener("scroll",s.update,Ht)})),a&&l.addEventListener("resize",s.update,Ht),function(){r&&u.forEach((function(t){t.removeEventListener("scroll",s.update,Ht)})),a&&l.removeEventListener("resize",s.update,Ht)}},data:{}};var Wt={left:"right",right:"left",bottom:"top",top:"bottom"};function Kt(t){return t.replace(/left|right|bottom|top/g,(function(t){return Wt[t]}))}var Yt={start:"end",end:"start"};function Qt(t){return t.replace(/start|end/g,(function(t){return Yt[t]}))}function Xt(t){var e=gt(t);return{scrollLeft:e.pageXOffset,scrollTop:e.pageYOffset}}function Gt(t){return xt(It(t)).left+Xt(t).scrollLeft}function Zt(t){var e=Ft(t),s=e.overflow,i=e.overflowX,n=e.overflowY;return/auto|scroll|overlay|hidden/.test(s+n+i)}function Jt(t){return["html","body","#document"].indexOf(ft(t))>=0?t.ownerDocument.body:vt(t)&&Zt(t)?t:Jt(Dt(t))}function te(t,e){var s;void 0===e&&(e=[]);var i=Jt(t),n=i===(null==(s=t.ownerDocument)?void 0:s.body),r=gt(i),o=n?[r].concat(r.visualViewport||[],Zt(i)?i:[]):i,a=e.concat(o);return n?a:a.concat(te(Dt(o)))}function ee(t){return Object.assign({},t,{left:t.x,top:t.y,right:t.x+t.width,bottom:t.y+t.height})}function se(t,e,s){return e===J?ee(function(t,e){var s=gt(t),i=It(t),n=s.visualViewport,r=i.clientWidth,o=i.clientHeight,a=0,l=0;if(n){r=n.width,o=n.height;var u=Et();(u||!u&&"fixed"===e)&&(a=n.offsetLeft,l=n.offsetTop)}return{width:r,height:o,x:a+Gt(t),y:l}}(t,s)):mt(e)?function(t,e){var s=xt(t,!1,"fixed"===e);return s.top=s.top+t.clientTop,s.left=s.left+t.clientLeft,s.bottom=s.top+t.clientHeight,s.right=s.left+t.clientWidth,s.width=t.clientWidth,s.height=t.clientHeight,s.x=s.left,s.y=s.top,s}(e,s):ee(function(t){var e,s=It(t),i=Xt(t),n=null==(e=t.ownerDocument)?void 0:e.body,r=kt(s.scrollWidth,s.clientWidth,n?n.scrollWidth:0,n?n.clientWidth:0),o=kt(s.scrollHeight,s.clientHeight,n?n.scrollHeight:0,n?n.clientHeight:0),a=-i.scrollLeft+Gt(t),l=-i.scrollTop;return"rtl"===Ft(n||s).direction&&(a+=kt(s.clientWidth,n?n.clientWidth:0)-r),{width:r,height:o,x:a,y:l}}(It(t)))}function ie(t,e,s,i){var n="clippingParents"===e?function(t){var e=te(Dt(t)),s=["absolute","fixed"].indexOf(Ft(t).position)>=0&&vt(t)?Pt(t):t;return mt(s)?e.filter((function(t){return mt(t)&&Ot(t,s)&&"body"!==ft(t)})):[]}(t):[].concat(e),r=[].concat(n,[s]),o=r[0],a=r.reduce((function(e,s){var n=se(t,s,i);return e.top=kt(n.top,e.top),e.right=wt(n.right,e.right),e.bottom=wt(n.bottom,e.bottom),e.left=kt(n.left,e.left),e}),se(t,o,i));return a.width=a.right-a.left,a.height=a.bottom-a.top,a.x=a.left,a.y=a.top,a}function ne(t){var e,s=t.reference,i=t.element,n=t.placement,r=n?yt(n):null,o=n?jt(n):null,a=s.x+s.width/2-i.width/2,l=s.y+s.height/2-i.height/2;switch(r){case H:e={x:a,y:s.y-i.height};break;case U:e={x:a,y:s.y+s.height};break;case W:e={x:s.x+s.width,y:l};break;case K:e={x:s.x-i.width,y:l};break;default:e={x:s.x,y:s.y}}var u=r?Lt(r):null;if(null!=u){var c="y"===u?"height":"width";switch(o){case X:e[u]=e[u]-(s[c]/2-i[c]/2);break;case G:e[u]=e[u]+(s[c]/2-i[c]/2)}}return e}function re(t,e){void 0===e&&(e={});var s=e,i=s.placement,n=void 0===i?t.placement:i,r=s.strategy,o=void 0===r?t.strategy:r,a=s.boundary,l=void 0===a?Z:a,u=s.rootBoundary,c=void 0===u?J:u,h=s.elementContext,d=void 0===h?tt:h,p=s.altBoundary,f=void 0!==p&&p,g=s.padding,m=void 0===g?0:g,v=Vt("number"!=typeof m?m:Nt(m,Q)),_=d===tt?et:tt,b=t.rects.popper,y=t.elements[f?_:d],k=ie(mt(y)?y:y.contextElement||It(t.elements.popper),l,c,o),w=xt(t.elements.reference),A=ne({reference:w,element:b,strategy:"absolute",placement:n}),C=ee(Object.assign({},b,A)),E=d===tt?C:w,x={top:k.top-E.top+v.top,bottom:E.bottom-k.bottom+v.bottom,left:k.left-E.left+v.left,right:E.right-k.right+v.right},S=t.modifiersData.offset;if(d===tt&&S){var O=S[n];Object.keys(x).forEach((function(t){var e=[W,U].indexOf(t)>=0?1:-1,s=[H,U].indexOf(t)>=0?"y":"x";x[t]+=O[s]*e}))}return x}const oe={name:"flip",enabled:!0,phase:"main",fn:function(t){var e=t.state,s=t.options,i=t.name;if(!e.modifiersData[i]._skip){for(var n=s.mainAxis,r=void 0===n||n,o=s.altAxis,a=void 0===o||o,l=s.fallbackPlacements,u=s.padding,c=s.boundary,h=s.rootBoundary,d=s.altBoundary,p=s.flipVariations,f=void 0===p||p,g=s.allowedAutoPlacements,m=e.options.placement,v=yt(m),_=l||(v===m||!f?[Kt(m)]:function(t){if(yt(t)===Y)return[];var e=Kt(t);return[Qt(t),e,Qt(e)]}(m)),b=[m].concat(_).reduce((function(t,s){return t.concat(yt(s)===Y?function(t,e){void 0===e&&(e={});var s=e,i=s.placement,n=s.boundary,r=s.rootBoundary,o=s.padding,a=s.flipVariations,l=s.allowedAutoPlacements,u=void 0===l?it:l,c=jt(i),h=c?a?st:st.filter((function(t){return jt(t)===c})):Q,d=h.filter((function(t){return u.indexOf(t)>=0}));0===d.length&&(d=h);var p=d.reduce((function(e,s){return e[s]=re(t,{placement:s,boundary:n,rootBoundary:r,padding:o})[yt(s)],e}),{});return Object.keys(p).sort((function(t,e){return p[t]-p[e]}))}(e,{placement:s,boundary:c,rootBoundary:h,padding:u,flipVariations:f,allowedAutoPlacements:g}):s)}),[]),y=e.rects.reference,k=e.rects.popper,w=new Map,A=!0,C=b[0],E=0;E<b.length;E++){var x=b[E],S=yt(x),O=jt(x)===X,F=[H,U].indexOf(S)>=0,T=F?"width":"height",I=re(e,{placement:x,boundary:c,rootBoundary:h,altBoundary:d,padding:u}),D=F?O?W:K:O?U:H;y[T]>k[T]&&(D=Kt(D));var M=Kt(D),P=[];if(r&&P.push(I[S]<=0),a&&P.push(I[D]<=0,I[M]<=0),P.every((function(t){return t}))){C=x,A=!1;break}w.set(x,P)}if(A)for(var L=function(t){var e=b.find((function(e){var s=w.get(e);if(s)return s.slice(0,t).every((function(t){return t}))}));if(e)return C=e,"break"},B=f?3:1;B>0;B--){if("break"===L(B))break}e.placement!==C&&(e.modifiersData[i]._skip=!0,e.placement=C,e.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}};function ae(t,e,s){return void 0===s&&(s={x:0,y:0}),{top:t.top-e.height-s.y,right:t.right-e.width+s.x,bottom:t.bottom-e.height+s.y,left:t.left-e.width-s.x}}function le(t){return[H,W,U,K].some((function(e){return t[e]>=0}))}const ue={name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(t){var e=t.state,s=t.name,i=e.rects.reference,n=e.rects.popper,r=e.modifiersData.preventOverflow,o=re(e,{elementContext:"reference"}),a=re(e,{altBoundary:!0}),l=ae(o,i),u=ae(a,n,r),c=le(l),h=le(u);e.modifiersData[s]={referenceClippingOffsets:l,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:h},e.attributes.popper=Object.assign({},e.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":h})}};const ce={name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(t){var e=t.state,s=t.options,i=t.name,n=s.offset,r=void 0===n?[0,0]:n,o=it.reduce((function(t,s){return t[s]=function(t,e,s){var i=yt(t),n=[K,H].indexOf(i)>=0?-1:1,r="function"==typeof s?s(Object.assign({},e,{placement:t})):s,o=r[0],a=r[1];return o=o||0,a=(a||0)*n,[K,W].indexOf(i)>=0?{x:a,y:o}:{x:o,y:a}}(s,e.rects,r),t}),{}),a=o[e.placement],l=a.x,u=a.y;null!=e.modifiersData.popperOffsets&&(e.modifiersData.popperOffsets.x+=l,e.modifiersData.popperOffsets.y+=u),e.modifiersData[i]=o}};const he={name:"popperOffsets",enabled:!0,phase:"read",fn:function(t){var e=t.state,s=t.name;e.modifiersData[s]=ne({reference:e.rects.reference,element:e.rects.popper,strategy:"absolute",placement:e.placement})},data:{}};const de={name:"preventOverflow",enabled:!0,phase:"main",fn:function(t){var e=t.state,s=t.options,i=t.name,n=s.mainAxis,r=void 0===n||n,o=s.altAxis,a=void 0!==o&&o,l=s.boundary,u=s.rootBoundary,c=s.altBoundary,h=s.padding,d=s.tether,p=void 0===d||d,f=s.tetherOffset,g=void 0===f?0:f,m=re(e,{boundary:l,rootBoundary:u,padding:h,altBoundary:c}),v=yt(e.placement),_=jt(e.placement),b=!_,y=Lt(v),k="x"===y?"y":"x",w=e.modifiersData.popperOffsets,A=e.rects.reference,C=e.rects.popper,E="function"==typeof g?g(Object.assign({},e.rects,{placement:e.placement})):g,x="number"==typeof E?{mainAxis:E,altAxis:E}:Object.assign({mainAxis:0,altAxis:0},E),S=e.modifiersData.offset?e.modifiersData.offset[e.placement]:null,O={x:0,y:0};if(w){if(r){var F,T="y"===y?H:K,I="y"===y?U:W,D="y"===y?"height":"width",M=w[y],P=M+m[T],L=M-m[I],B=p?-C[D]/2:0,V=_===X?A[D]:C[D],N=_===X?-C[D]:-A[D],$=e.elements.arrow,j=p&&$?St($):{width:0,height:0},R=e.modifiersData["arrow#persistent"]?e.modifiersData["arrow#persistent"].padding:{top:0,right:0,bottom:0,left:0},q=R[T],z=R[I],Y=Bt(0,A[D],j[D]),Q=b?A[D]/2-B-Y-q-x.mainAxis:V-Y-q-x.mainAxis,G=b?-A[D]/2+B+Y+z+x.mainAxis:N+Y+z+x.mainAxis,Z=e.elements.arrow&&Pt(e.elements.arrow),J=Z?"y"===y?Z.clientTop||0:Z.clientLeft||0:0,tt=null!=(F=null==S?void 0:S[y])?F:0,et=M+G-tt,st=Bt(p?wt(P,M+Q-tt-J):P,M,p?kt(L,et):L);w[y]=st,O[y]=st-M}if(a){var it,nt="x"===y?H:K,rt="x"===y?U:W,ot=w[k],at="y"===k?"height":"width",lt=ot+m[nt],ut=ot-m[rt],ct=-1!==[H,K].indexOf(v),ht=null!=(it=null==S?void 0:S[k])?it:0,dt=ct?lt:ot-A[at]-C[at]-ht+x.altAxis,pt=ct?ot+A[at]+C[at]-ht-x.altAxis:ut,ft=p&&ct?function(t,e,s){var i=Bt(t,e,s);return i>s?s:i}(dt,ot,pt):Bt(p?dt:lt,ot,p?pt:ut);w[k]=ft,O[k]=ft-ot}e.modifiersData[i]=O}},requiresIfExists:["offset"]};function pe(t,e,s){void 0===s&&(s=!1);var i,n,r=vt(e),o=vt(e)&&function(t){var e=t.getBoundingClientRect(),s=At(e.width)/t.offsetWidth||1,i=At(e.height)/t.offsetHeight||1;return 1!==s||1!==i}(e),a=It(e),l=xt(t,o,s),u={scrollLeft:0,scrollTop:0},c={x:0,y:0};return(r||!r&&!s)&&(("body"!==ft(e)||Zt(a))&&(u=(i=e)!==gt(i)&&vt(i)?{scrollLeft:(n=i).scrollLeft,scrollTop:n.scrollTop}:Xt(i)),vt(e)?((c=xt(e,!0)).x+=e.clientLeft,c.y+=e.clientTop):a&&(c.x=Gt(a))),{x:l.left+u.scrollLeft-c.x,y:l.top+u.scrollTop-c.y,width:l.width,height:l.height}}function fe(t){var e=new Map,s=new Set,i=[];function n(t){s.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach((function(t){if(!s.has(t)){var i=e.get(t);i&&n(i)}})),i.push(t)}return t.forEach((function(t){e.set(t.name,t)})),t.forEach((function(t){s.has(t.name)||n(t)})),i}var ge={placement:"bottom",modifiers:[],strategy:"absolute"};function me(){for(var t=arguments.length,e=new Array(t),s=0;s<t;s++)e[s]=arguments[s];return!e.some((function(t){return!(t&&"function"==typeof t.getBoundingClientRect)}))}function ve(t){void 0===t&&(t={});var e=t,s=e.defaultModifiers,i=void 0===s?[]:s,n=e.defaultOptions,r=void 0===n?ge:n;return function(t,e,s){void 0===s&&(s=r);var n,o,a={placement:"bottom",orderedModifiers:[],options:Object.assign({},ge,r),modifiersData:{},elements:{reference:t,popper:e},attributes:{},styles:{}},l=[],u=!1,c={state:a,setOptions:function(s){var n="function"==typeof s?s(a.options):s;h(),a.options=Object.assign({},r,a.options,n),a.scrollParents={reference:mt(t)?te(t):t.contextElement?te(t.contextElement):[],popper:te(e)};var o,u,d=function(t){var e=fe(t);return pt.reduce((function(t,s){return t.concat(e.filter((function(t){return t.phase===s})))}),[])}((o=[].concat(i,a.options.modifiers),u=o.reduce((function(t,e){var s=t[e.name];return t[e.name]=s?Object.assign({},s,e,{options:Object.assign({},s.options,e.options),data:Object.assign({},s.data,e.data)}):e,t}),{}),Object.keys(u).map((function(t){return u[t]}))));return a.orderedModifiers=d.filter((function(t){return t.enabled})),a.orderedModifiers.forEach((function(t){var e=t.name,s=t.options,i=void 0===s?{}:s,n=t.effect;if("function"==typeof n){var r=n({state:a,name:e,instance:c,options:i}),o=function(){};l.push(r||o)}})),c.update()},forceUpdate:function(){if(!u){var t=a.elements,e=t.reference,s=t.popper;if(me(e,s)){a.rects={reference:pe(e,Pt(s),"fixed"===a.options.strategy),popper:St(s)},a.reset=!1,a.placement=a.options.placement,a.orderedModifiers.forEach((function(t){return a.modifiersData[t.name]=Object.assign({},t.data)}));for(var i=0;i<a.orderedModifiers.length;i++)if(!0!==a.reset){var n=a.orderedModifiers[i],r=n.fn,o=n.options,l=void 0===o?{}:o,h=n.name;"function"==typeof r&&(a=r({state:a,options:l,name:h,instance:c})||a)}else a.reset=!1,i=-1}}},update:(n=function(){return new Promise((function(t){c.forceUpdate(),t(a)}))},function(){return o||(o=new Promise((function(t){Promise.resolve().then((function(){o=void 0,t(n())}))}))),o}),destroy:function(){h(),u=!0}};if(!me(t,e))return c;function h(){l.forEach((function(t){return t()})),l=[]}return c.setOptions(s).then((function(t){!u&&s.onFirstUpdate&&s.onFirstUpdate(t)})),c}}var _e=ve(),be=ve({defaultModifiers:[Ut,he,zt,bt,ce,oe,de,$t,ue]}),ye=ve({defaultModifiers:[Ut,he,zt,bt]});
/*!
  * Bootstrap v5.3.1 (https://getbootstrap.com/)
  * Copyright 2011-2023 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
  * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
  */
const ke=new Map,we={set(t,e,s){ke.has(t)||ke.set(t,new Map);const i=ke.get(t);i.has(e)||0===i.size?i.set(e,s):console.error(`Bootstrap doesn't allow more than one instance per element. Bound instance: ${Array.from(i.keys())[0]}.`)},get:(t,e)=>ke.has(t)&&ke.get(t).get(e)||null,remove(t,e){if(!ke.has(t))return;const s=ke.get(t);s.delete(e),0===s.size&&ke.delete(t)}},Ae="transitionend",Ce=t=>(t&&window.CSS&&window.CSS.escape&&(t=t.replace(/#([^\s"#']+)/g,((t,e)=>`#${CSS.escape(e)}`))),t),Ee=t=>{t.dispatchEvent(new Event(Ae))},xe=t=>!(!t||"object"!=typeof t)&&(void 0!==t.jquery&&(t=t[0]),void 0!==t.nodeType),Se=t=>xe(t)?t.jquery?t[0]:t:"string"==typeof t&&t.length>0?document.querySelector(Ce(t)):null,Oe=t=>{if(!xe(t)||0===t.getClientRects().length)return!1;const e="visible"===getComputedStyle(t).getPropertyValue("visibility"),s=t.closest("details:not([open])");if(!s)return e;if(s!==t){const e=t.closest("summary");if(e&&e.parentNode!==s)return!1;if(null===e)return!1}return e},Fe=t=>!t||t.nodeType!==Node.ELEMENT_NODE||(!!t.classList.contains("disabled")||(void 0!==t.disabled?t.disabled:t.hasAttribute("disabled")&&"false"!==t.getAttribute("disabled"))),Te=t=>{if(!document.documentElement.attachShadow)return null;if("function"==typeof t.getRootNode){const e=t.getRootNode();return e instanceof ShadowRoot?e:null}return t instanceof ShadowRoot?t:t.parentNode?Te(t.parentNode):null},Ie=()=>{},De=t=>{t.offsetHeight},Me=()=>window.jQuery&&!document.body.hasAttribute("data-bs-no-jquery")?window.jQuery:null,Pe=[],Le=()=>"rtl"===document.documentElement.dir,Be=t=>{var e;e=()=>{const e=Me();if(e){const s=t.NAME,i=e.fn[s];e.fn[s]=t.jQueryInterface,e.fn[s].Constructor=t,e.fn[s].noConflict=()=>(e.fn[s]=i,t.jQueryInterface)}},"loading"===document.readyState?(Pe.length||document.addEventListener("DOMContentLoaded",(()=>{for(const t of Pe)t()})),Pe.push(e)):e()},Ve=(t,e=[],s=t)=>"function"==typeof t?t(...e):s,Ne=(t,e,s=!0)=>{if(!s)return void Ve(t);const i=(t=>{if(!t)return 0;let{transitionDuration:e,transitionDelay:s}=window.getComputedStyle(t);const i=Number.parseFloat(e),n=Number.parseFloat(s);return i||n?(e=e.split(",")[0],s=s.split(",")[0],1e3*(Number.parseFloat(e)+Number.parseFloat(s))):0})(e)+5;let n=!1;const r=({target:s})=>{s===e&&(n=!0,e.removeEventListener(Ae,r),Ve(t))};e.addEventListener(Ae,r),setTimeout((()=>{n||Ee(e)}),i)},$e=(t,e,s,i)=>{const n=t.length;let r=t.indexOf(e);return-1===r?!s&&i?t[n-1]:t[0]:(r+=s?1:-1,i&&(r=(r+n)%n),t[Math.max(0,Math.min(r,n-1))])},je=/[^.]*(?=\..*)\.|.*/,Re=/\..*/,qe=/::\d+$/,ze={};let He=1;const Ue={mouseenter:"mouseover",mouseleave:"mouseout"},We=new Set(["click","dblclick","mouseup","mousedown","contextmenu","mousewheel","DOMMouseScroll","mouseover","mouseout","mousemove","selectstart","selectend","keydown","keypress","keyup","orientationchange","touchstart","touchmove","touchend","touchcancel","pointerdown","pointermove","pointerup","pointerleave","pointercancel","gesturestart","gesturechange","gestureend","focus","blur","change","reset","select","submit","focusin","focusout","load","unload","beforeunload","resize","move","DOMContentLoaded","readystatechange","error","abort","scroll"]);function Ke(t,e){return e&&`${e}::${He++}`||t.uidEvent||He++}function Ye(t){const e=Ke(t);return t.uidEvent=e,ze[e]=ze[e]||{},ze[e]}function Qe(t,e,s=null){return Object.values(t).find((t=>t.callable===e&&t.delegationSelector===s))}function Xe(t,e,s){const i="string"==typeof e,n=i?s:e||s;let r=ts(t);return We.has(r)||(r=t),[i,n,r]}function Ge(t,e,s,i,n){if("string"!=typeof e||!t)return;let[r,o,a]=Xe(e,s,i);if(e in Ue){const t=t=>function(e){if(!e.relatedTarget||e.relatedTarget!==e.delegateTarget&&!e.delegateTarget.contains(e.relatedTarget))return t.call(this,e)};o=t(o)}const l=Ye(t),u=l[a]||(l[a]={}),c=Qe(u,o,r?s:null);if(c)return void(c.oneOff=c.oneOff&&n);const h=Ke(o,e.replace(je,"")),d=r?function(t,e,s){return function i(n){const r=t.querySelectorAll(e);for(let{target:o}=n;o&&o!==this;o=o.parentNode)for(const a of r)if(a===o)return ss(n,{delegateTarget:o}),i.oneOff&&es.off(t,n.type,e,s),s.apply(o,[n])}}(t,s,o):function(t,e){return function s(i){return ss(i,{delegateTarget:t}),s.oneOff&&es.off(t,i.type,e),e.apply(t,[i])}}(t,o);d.delegationSelector=r?s:null,d.callable=o,d.oneOff=n,d.uidEvent=h,u[h]=d,t.addEventListener(a,d,r)}function Ze(t,e,s,i,n){const r=Qe(e[s],i,n);r&&(t.removeEventListener(s,r,Boolean(n)),delete e[s][r.uidEvent])}function Je(t,e,s,i){const n=e[s]||{};for(const[r,o]of Object.entries(n))r.includes(i)&&Ze(t,e,s,o.callable,o.delegationSelector)}function ts(t){return t=t.replace(Re,""),Ue[t]||t}const es={on(t,e,s,i){Ge(t,e,s,i,!1)},one(t,e,s,i){Ge(t,e,s,i,!0)},off(t,e,s,i){if("string"!=typeof e||!t)return;const[n,r,o]=Xe(e,s,i),a=o!==e,l=Ye(t),u=l[o]||{},c=e.startsWith(".");if(void 0===r){if(c)for(const s of Object.keys(l))Je(t,l,s,e.slice(1));for(const[s,i]of Object.entries(u)){const n=s.replace(qe,"");a&&!e.includes(n)||Ze(t,l,o,i.callable,i.delegationSelector)}}else{if(!Object.keys(u).length)return;Ze(t,l,o,r,n?s:null)}},trigger(t,e,s){if("string"!=typeof e||!t)return null;const i=Me();let n=null,r=!0,o=!0,a=!1;e!==ts(e)&&i&&(n=i.Event(e,s),i(t).trigger(n),r=!n.isPropagationStopped(),o=!n.isImmediatePropagationStopped(),a=n.isDefaultPrevented());const l=ss(new Event(e,{bubbles:r,cancelable:!0}),s);return a&&l.preventDefault(),o&&t.dispatchEvent(l),l.defaultPrevented&&n&&n.preventDefault(),l}};function ss(t,e={}){for(const[s,i]of Object.entries(e))try{t[s]=i}catch(e){Object.defineProperty(t,s,{configurable:!0,get:()=>i})}return t}function is(t){if("true"===t)return!0;if("false"===t)return!1;if(t===Number(t).toString())return Number(t);if(""===t||"null"===t)return null;if("string"!=typeof t)return t;try{return JSON.parse(decodeURIComponent(t))}catch(e){return t}}function ns(t){return t.replace(/[A-Z]/g,(t=>`-${t.toLowerCase()}`))}const rs={setDataAttribute(t,e,s){t.setAttribute(`data-bs-${ns(e)}`,s)},removeDataAttribute(t,e){t.removeAttribute(`data-bs-${ns(e)}`)},getDataAttributes(t){if(!t)return{};const e={},s=Object.keys(t.dataset).filter((t=>t.startsWith("bs")&&!t.startsWith("bsConfig")));for(const i of s){let s=i.replace(/^bs/,"");s=s.charAt(0).toLowerCase()+s.slice(1,s.length),e[s]=is(t.dataset[i])}return e},getDataAttribute:(t,e)=>is(t.getAttribute(`data-bs-${ns(e)}`))};class os{static get Default(){return{}}static get DefaultType(){return{}}static get NAME(){throw new Error('You have to implement the static method "NAME", for each component!')}_getConfig(t){return t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t}_mergeConfigObj(t,e){const s=xe(e)?rs.getDataAttribute(e,"config"):{};return{...this.constructor.Default,..."object"==typeof s?s:{},...xe(e)?rs.getDataAttributes(e):{},..."object"==typeof t?t:{}}}_typeCheckConfig(t,e=this.constructor.DefaultType){for(const[i,n]of Object.entries(e)){const e=t[i],r=xe(e)?"element":null==(s=e)?`${s}`:Object.prototype.toString.call(s).match(/\s([a-z]+)/i)[1].toLowerCase();if(!new RegExp(n).test(r))throw new TypeError(`${this.constructor.NAME.toUpperCase()}: Option "${i}" provided type "${r}" but expected type "${n}".`)}var s}}class as extends os{constructor(t,e){super(),(t=Se(t))&&(this._element=t,this._config=this._getConfig(e),we.set(this._element,this.constructor.DATA_KEY,this))}dispose(){we.remove(this._element,this.constructor.DATA_KEY),es.off(this._element,this.constructor.EVENT_KEY);for(const t of Object.getOwnPropertyNames(this))this[t]=null}_queueCallback(t,e,s=!0){Ne(t,e,s)}_getConfig(t){return t=this._mergeConfigObj(t,this._element),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}static getInstance(t){return we.get(Se(t),this.DATA_KEY)}static getOrCreateInstance(t,e={}){return this.getInstance(t)||new this(t,"object"==typeof e?e:null)}static get VERSION(){return"5.3.1"}static get DATA_KEY(){return`bs.${this.NAME}`}static get EVENT_KEY(){return`.${this.DATA_KEY}`}static eventName(t){return`${t}${this.EVENT_KEY}`}}const ls=t=>{let e=t.getAttribute("data-bs-target");if(!e||"#"===e){let s=t.getAttribute("href");if(!s||!s.includes("#")&&!s.startsWith("."))return null;s.includes("#")&&!s.startsWith("#")&&(s=`#${s.split("#")[1]}`),e=s&&"#"!==s?s.trim():null}return Ce(e)},us={find:(t,e=document.documentElement)=>[].concat(...Element.prototype.querySelectorAll.call(e,t)),findOne:(t,e=document.documentElement)=>Element.prototype.querySelector.call(e,t),children:(t,e)=>[].concat(...t.children).filter((t=>t.matches(e))),parents(t,e){const s=[];let i=t.parentNode.closest(e);for(;i;)s.push(i),i=i.parentNode.closest(e);return s},prev(t,e){let s=t.previousElementSibling;for(;s;){if(s.matches(e))return[s];s=s.previousElementSibling}return[]},next(t,e){let s=t.nextElementSibling;for(;s;){if(s.matches(e))return[s];s=s.nextElementSibling}return[]},focusableChildren(t){const e=["a","button","input","textarea","select","details","[tabindex]",'[contenteditable="true"]'].map((t=>`${t}:not([tabindex^="-"])`)).join(",");return this.find(e,t).filter((t=>!Fe(t)&&Oe(t)))},getSelectorFromElement(t){const e=ls(t);return e&&us.findOne(e)?e:null},getElementFromSelector(t){const e=ls(t);return e?us.findOne(e):null},getMultipleElementsFromSelector(t){const e=ls(t);return e?us.find(e):[]}},cs=(t,e="hide")=>{const s=`click.dismiss${t.EVENT_KEY}`,i=t.NAME;es.on(document,s,`[data-bs-dismiss="${i}"]`,(function(s){if(["A","AREA"].includes(this.tagName)&&s.preventDefault(),Fe(this))return;const n=us.getElementFromSelector(this)||this.closest(`.${i}`);t.getOrCreateInstance(n)[e]()}))},hs=".bs.alert",ds=`close${hs}`,ps=`closed${hs}`;class fs extends as{static get NAME(){return"alert"}close(){if(es.trigger(this._element,ds).defaultPrevented)return;this._element.classList.remove("show");const t=this._element.classList.contains("fade");this._queueCallback((()=>this._destroyElement()),this._element,t)}_destroyElement(){this._element.remove(),es.trigger(this._element,ps),this.dispose()}static jQueryInterface(t){return this.each((function(){const e=fs.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}cs(fs,"close"),Be(fs);const gs='[data-bs-toggle="button"]';class ms extends as{static get NAME(){return"button"}toggle(){this._element.setAttribute("aria-pressed",this._element.classList.toggle("active"))}static jQueryInterface(t){return this.each((function(){const e=ms.getOrCreateInstance(this);"toggle"===t&&e[t]()}))}}es.on(document,"click.bs.button.data-api",gs,(t=>{t.preventDefault();const e=t.target.closest(gs);ms.getOrCreateInstance(e).toggle()})),Be(ms);const vs=".bs.swipe",_s=`touchstart${vs}`,bs=`touchmove${vs}`,ys=`touchend${vs}`,ks=`pointerdown${vs}`,ws=`pointerup${vs}`,As={endCallback:null,leftCallback:null,rightCallback:null},Cs={endCallback:"(function|null)",leftCallback:"(function|null)",rightCallback:"(function|null)"};class Es extends os{constructor(t,e){super(),this._element=t,t&&Es.isSupported()&&(this._config=this._getConfig(e),this._deltaX=0,this._supportPointerEvents=Boolean(window.PointerEvent),this._initEvents())}static get Default(){return As}static get DefaultType(){return Cs}static get NAME(){return"swipe"}dispose(){es.off(this._element,vs)}_start(t){this._supportPointerEvents?this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX):this._deltaX=t.touches[0].clientX}_end(t){this._eventIsPointerPenTouch(t)&&(this._deltaX=t.clientX-this._deltaX),this._handleSwipe(),Ve(this._config.endCallback)}_move(t){this._deltaX=t.touches&&t.touches.length>1?0:t.touches[0].clientX-this._deltaX}_handleSwipe(){const t=Math.abs(this._deltaX);if(t<=40)return;const e=t/this._deltaX;this._deltaX=0,e&&Ve(e>0?this._config.rightCallback:this._config.leftCallback)}_initEvents(){this._supportPointerEvents?(es.on(this._element,ks,(t=>this._start(t))),es.on(this._element,ws,(t=>this._end(t))),this._element.classList.add("pointer-event")):(es.on(this._element,_s,(t=>this._start(t))),es.on(this._element,bs,(t=>this._move(t))),es.on(this._element,ys,(t=>this._end(t))))}_eventIsPointerPenTouch(t){return this._supportPointerEvents&&("pen"===t.pointerType||"touch"===t.pointerType)}static isSupported(){return"ontouchstart"in document.documentElement||navigator.maxTouchPoints>0}}const xs=".bs.carousel",Ss=".data-api",Os="next",Fs="prev",Ts="left",Is="right",Ds=`slide${xs}`,Ms=`slid${xs}`,Ps=`keydown${xs}`,Ls=`mouseenter${xs}`,Bs=`mouseleave${xs}`,Vs=`dragstart${xs}`,Ns=`load${xs}${Ss}`,$s=`click${xs}${Ss}`,js="carousel",Rs="active",qs=".active",zs=".carousel-item",Hs=qs+zs,Us={ArrowLeft:Is,ArrowRight:Ts},Ws={interval:5e3,keyboard:!0,pause:"hover",ride:!1,touch:!0,wrap:!0},Ks={interval:"(number|boolean)",keyboard:"boolean",pause:"(string|boolean)",ride:"(boolean|string)",touch:"boolean",wrap:"boolean"};class Ys extends as{constructor(t,e){super(t,e),this._interval=null,this._activeElement=null,this._isSliding=!1,this.touchTimeout=null,this._swipeHelper=null,this._indicatorsElement=us.findOne(".carousel-indicators",this._element),this._addEventListeners(),this._config.ride===js&&this.cycle()}static get Default(){return Ws}static get DefaultType(){return Ks}static get NAME(){return"carousel"}next(){this._slide(Os)}nextWhenVisible(){!document.hidden&&Oe(this._element)&&this.next()}prev(){this._slide(Fs)}pause(){this._isSliding&&Ee(this._element),this._clearInterval()}cycle(){this._clearInterval(),this._updateInterval(),this._interval=setInterval((()=>this.nextWhenVisible()),this._config.interval)}_maybeEnableCycle(){this._config.ride&&(this._isSliding?es.one(this._element,Ms,(()=>this.cycle())):this.cycle())}to(t){const e=this._getItems();if(t>e.length-1||t<0)return;if(this._isSliding)return void es.one(this._element,Ms,(()=>this.to(t)));const s=this._getItemIndex(this._getActive());if(s===t)return;const i=t>s?Os:Fs;this._slide(i,e[t])}dispose(){this._swipeHelper&&this._swipeHelper.dispose(),super.dispose()}_configAfterMerge(t){return t.defaultInterval=t.interval,t}_addEventListeners(){this._config.keyboard&&es.on(this._element,Ps,(t=>this._keydown(t))),"hover"===this._config.pause&&(es.on(this._element,Ls,(()=>this.pause())),es.on(this._element,Bs,(()=>this._maybeEnableCycle()))),this._config.touch&&Es.isSupported()&&this._addTouchEventListeners()}_addTouchEventListeners(){for(const t of us.find(".carousel-item img",this._element))es.on(t,Vs,(t=>t.preventDefault()));const t={leftCallback:()=>this._slide(this._directionToOrder(Ts)),rightCallback:()=>this._slide(this._directionToOrder(Is)),endCallback:()=>{"hover"===this._config.pause&&(this.pause(),this.touchTimeout&&clearTimeout(this.touchTimeout),this.touchTimeout=setTimeout((()=>this._maybeEnableCycle()),500+this._config.interval))}};this._swipeHelper=new Es(this._element,t)}_keydown(t){if(/input|textarea/i.test(t.target.tagName))return;const e=Us[t.key];e&&(t.preventDefault(),this._slide(this._directionToOrder(e)))}_getItemIndex(t){return this._getItems().indexOf(t)}_setActiveIndicatorElement(t){if(!this._indicatorsElement)return;const e=us.findOne(qs,this._indicatorsElement);e.classList.remove(Rs),e.removeAttribute("aria-current");const s=us.findOne(`[data-bs-slide-to="${t}"]`,this._indicatorsElement);s&&(s.classList.add(Rs),s.setAttribute("aria-current","true"))}_updateInterval(){const t=this._activeElement||this._getActive();if(!t)return;const e=Number.parseInt(t.getAttribute("data-bs-interval"),10);this._config.interval=e||this._config.defaultInterval}_slide(t,e=null){if(this._isSliding)return;const s=this._getActive(),i=t===Os,n=e||$e(this._getItems(),s,i,this._config.wrap);if(n===s)return;const r=this._getItemIndex(n),o=e=>es.trigger(this._element,e,{relatedTarget:n,direction:this._orderToDirection(t),from:this._getItemIndex(s),to:r});if(o(Ds).defaultPrevented)return;if(!s||!n)return;const a=Boolean(this._interval);this.pause(),this._isSliding=!0,this._setActiveIndicatorElement(r),this._activeElement=n;const l=i?"carousel-item-start":"carousel-item-end",u=i?"carousel-item-next":"carousel-item-prev";n.classList.add(u),De(n),s.classList.add(l),n.classList.add(l);this._queueCallback((()=>{n.classList.remove(l,u),n.classList.add(Rs),s.classList.remove(Rs,u,l),this._isSliding=!1,o(Ms)}),s,this._isAnimated()),a&&this.cycle()}_isAnimated(){return this._element.classList.contains("slide")}_getActive(){return us.findOne(Hs,this._element)}_getItems(){return us.find(zs,this._element)}_clearInterval(){this._interval&&(clearInterval(this._interval),this._interval=null)}_directionToOrder(t){return Le()?t===Ts?Fs:Os:t===Ts?Os:Fs}_orderToDirection(t){return Le()?t===Fs?Ts:Is:t===Fs?Is:Ts}static jQueryInterface(t){return this.each((function(){const e=Ys.getOrCreateInstance(this,t);if("number"!=typeof t){if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}else e.to(t)}))}}es.on(document,$s,"[data-bs-slide], [data-bs-slide-to]",(function(t){const e=us.getElementFromSelector(this);if(!e||!e.classList.contains(js))return;t.preventDefault();const s=Ys.getOrCreateInstance(e),i=this.getAttribute("data-bs-slide-to");return i?(s.to(i),void s._maybeEnableCycle()):"next"===rs.getDataAttribute(this,"slide")?(s.next(),void s._maybeEnableCycle()):(s.prev(),void s._maybeEnableCycle())})),es.on(window,Ns,(()=>{const t=us.find('[data-bs-ride="carousel"]');for(const e of t)Ys.getOrCreateInstance(e)})),Be(Ys);const Qs=".bs.collapse",Xs=`show${Qs}`,Gs=`shown${Qs}`,Zs=`hide${Qs}`,Js=`hidden${Qs}`,ti=`click${Qs}.data-api`,ei="show",si="collapse",ii="collapsing",ni=`:scope .${si} .${si}`,ri='[data-bs-toggle="collapse"]',oi={parent:null,toggle:!0},ai={parent:"(null|element)",toggle:"boolean"};class li extends as{constructor(t,e){super(t,e),this._isTransitioning=!1,this._triggerArray=[];const s=us.find(ri);for(const t of s){const e=us.getSelectorFromElement(t),s=us.find(e).filter((t=>t===this._element));null!==e&&s.length&&this._triggerArray.push(t)}this._initializeChildren(),this._config.parent||this._addAriaAndCollapsedClass(this._triggerArray,this._isShown()),this._config.toggle&&this.toggle()}static get Default(){return oi}static get DefaultType(){return ai}static get NAME(){return"collapse"}toggle(){this._isShown()?this.hide():this.show()}show(){if(this._isTransitioning||this._isShown())return;let t=[];if(this._config.parent&&(t=this._getFirstLevelChildren(".collapse.show, .collapse.collapsing").filter((t=>t!==this._element)).map((t=>li.getOrCreateInstance(t,{toggle:!1})))),t.length&&t[0]._isTransitioning)return;if(es.trigger(this._element,Xs).defaultPrevented)return;for(const e of t)e.hide();const e=this._getDimension();this._element.classList.remove(si),this._element.classList.add(ii),this._element.style[e]=0,this._addAriaAndCollapsedClass(this._triggerArray,!0),this._isTransitioning=!0;const s=`scroll${e[0].toUpperCase()+e.slice(1)}`;this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(ii),this._element.classList.add(si,ei),this._element.style[e]="",es.trigger(this._element,Gs)}),this._element,!0),this._element.style[e]=`${this._element[s]}px`}hide(){if(this._isTransitioning||!this._isShown())return;if(es.trigger(this._element,Zs).defaultPrevented)return;const t=this._getDimension();this._element.style[t]=`${this._element.getBoundingClientRect()[t]}px`,De(this._element),this._element.classList.add(ii),this._element.classList.remove(si,ei);for(const t of this._triggerArray){const e=us.getElementFromSelector(t);e&&!this._isShown(e)&&this._addAriaAndCollapsedClass([t],!1)}this._isTransitioning=!0;this._element.style[t]="",this._queueCallback((()=>{this._isTransitioning=!1,this._element.classList.remove(ii),this._element.classList.add(si),es.trigger(this._element,Js)}),this._element,!0)}_isShown(t=this._element){return t.classList.contains(ei)}_configAfterMerge(t){return t.toggle=Boolean(t.toggle),t.parent=Se(t.parent),t}_getDimension(){return this._element.classList.contains("collapse-horizontal")?"width":"height"}_initializeChildren(){if(!this._config.parent)return;const t=this._getFirstLevelChildren(ri);for(const e of t){const t=us.getElementFromSelector(e);t&&this._addAriaAndCollapsedClass([e],this._isShown(t))}}_getFirstLevelChildren(t){const e=us.find(ni,this._config.parent);return us.find(t,this._config.parent).filter((t=>!e.includes(t)))}_addAriaAndCollapsedClass(t,e){if(t.length)for(const s of t)s.classList.toggle("collapsed",!e),s.setAttribute("aria-expanded",e)}static jQueryInterface(t){const e={};return"string"==typeof t&&/show|hide/.test(t)&&(e.toggle=!1),this.each((function(){const s=li.getOrCreateInstance(this,e);if("string"==typeof t){if(void 0===s[t])throw new TypeError(`No method named "${t}"`);s[t]()}}))}}es.on(document,ti,ri,(function(t){("A"===t.target.tagName||t.delegateTarget&&"A"===t.delegateTarget.tagName)&&t.preventDefault();for(const t of us.getMultipleElementsFromSelector(this))li.getOrCreateInstance(t,{toggle:!1}).toggle()})),Be(li);const ui="dropdown",ci=".bs.dropdown",hi=".data-api",di="ArrowUp",pi="ArrowDown",fi=`hide${ci}`,gi=`hidden${ci}`,mi=`show${ci}`,vi=`shown${ci}`,_i=`click${ci}${hi}`,bi=`keydown${ci}${hi}`,yi=`keyup${ci}${hi}`,ki="show",wi='[data-bs-toggle="dropdown"]:not(.disabled):not(:disabled)',Ai=`${wi}.${ki}`,Ci=".dropdown-menu",Ei=Le()?"top-end":"top-start",xi=Le()?"top-start":"top-end",Si=Le()?"bottom-end":"bottom-start",Oi=Le()?"bottom-start":"bottom-end",Fi=Le()?"left-start":"right-start",Ti=Le()?"right-start":"left-start",Ii={autoClose:!0,boundary:"clippingParents",display:"dynamic",offset:[0,2],popperConfig:null,reference:"toggle"},Di={autoClose:"(boolean|string)",boundary:"(string|element)",display:"string",offset:"(array|string|function)",popperConfig:"(null|object|function)",reference:"(string|element|object)"};class Mi extends as{constructor(t,e){super(t,e),this._popper=null,this._parent=this._element.parentNode,this._menu=us.next(this._element,Ci)[0]||us.prev(this._element,Ci)[0]||us.findOne(Ci,this._parent),this._inNavbar=this._detectNavbar()}static get Default(){return Ii}static get DefaultType(){return Di}static get NAME(){return ui}toggle(){return this._isShown()?this.hide():this.show()}show(){if(Fe(this._element)||this._isShown())return;const t={relatedTarget:this._element};if(!es.trigger(this._element,mi,t).defaultPrevented){if(this._createPopper(),"ontouchstart"in document.documentElement&&!this._parent.closest(".navbar-nav"))for(const t of[].concat(...document.body.children))es.on(t,"mouseover",Ie);this._element.focus(),this._element.setAttribute("aria-expanded",!0),this._menu.classList.add(ki),this._element.classList.add(ki),es.trigger(this._element,vi,t)}}hide(){if(Fe(this._element)||!this._isShown())return;const t={relatedTarget:this._element};this._completeHide(t)}dispose(){this._popper&&this._popper.destroy(),super.dispose()}update(){this._inNavbar=this._detectNavbar(),this._popper&&this._popper.update()}_completeHide(t){if(!es.trigger(this._element,fi,t).defaultPrevented){if("ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))es.off(t,"mouseover",Ie);this._popper&&this._popper.destroy(),this._menu.classList.remove(ki),this._element.classList.remove(ki),this._element.setAttribute("aria-expanded","false"),rs.removeDataAttribute(this._menu,"popper"),es.trigger(this._element,gi,t)}}_getConfig(t){if("object"==typeof(t=super._getConfig(t)).reference&&!xe(t.reference)&&"function"!=typeof t.reference.getBoundingClientRect)throw new TypeError(`${ui.toUpperCase()}: Option "reference" provided type "object" without a required "getBoundingClientRect" method.`);return t}_createPopper(){if(void 0===t)throw new TypeError("Bootstrap's dropdowns require Popper (https://popper.js.org)");let e=this._element;"parent"===this._config.reference?e=this._parent:xe(this._config.reference)?e=Se(this._config.reference):"object"==typeof this._config.reference&&(e=this._config.reference);const s=this._getPopperConfig();this._popper=be(e,this._menu,s)}_isShown(){return this._menu.classList.contains(ki)}_getPlacement(){const t=this._parent;if(t.classList.contains("dropend"))return Fi;if(t.classList.contains("dropstart"))return Ti;if(t.classList.contains("dropup-center"))return"top";if(t.classList.contains("dropdown-center"))return"bottom";const e="end"===getComputedStyle(this._menu).getPropertyValue("--bs-position").trim();return t.classList.contains("dropup")?e?xi:Ei:e?Oi:Si}_detectNavbar(){return null!==this._element.closest(".navbar")}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_getPopperConfig(){const t={placement:this._getPlacement(),modifiers:[{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"offset",options:{offset:this._getOffset()}}]};return(this._inNavbar||"static"===this._config.display)&&(rs.setDataAttribute(this._menu,"popper","static"),t.modifiers=[{name:"applyStyles",enabled:!1}]),{...t,...Ve(this._config.popperConfig,[t])}}_selectMenuItem({key:t,target:e}){const s=us.find(".dropdown-menu .dropdown-item:not(.disabled):not(:disabled)",this._menu).filter((t=>Oe(t)));s.length&&$e(s,e,t===pi,!s.includes(e)).focus()}static jQueryInterface(t){return this.each((function(){const e=Mi.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}static clearMenus(t){if(2===t.button||"keyup"===t.type&&"Tab"!==t.key)return;const e=us.find(Ai);for(const s of e){const e=Mi.getInstance(s);if(!e||!1===e._config.autoClose)continue;const i=t.composedPath(),n=i.includes(e._menu);if(i.includes(e._element)||"inside"===e._config.autoClose&&!n||"outside"===e._config.autoClose&&n)continue;if(e._menu.contains(t.target)&&("keyup"===t.type&&"Tab"===t.key||/input|select|option|textarea|form/i.test(t.target.tagName)))continue;const r={relatedTarget:e._element};"click"===t.type&&(r.clickEvent=t),e._completeHide(r)}}static dataApiKeydownHandler(t){const e=/input|textarea/i.test(t.target.tagName),s="Escape"===t.key,i=[di,pi].includes(t.key);if(!i&&!s)return;if(e&&!s)return;t.preventDefault();const n=this.matches(wi)?this:us.prev(this,wi)[0]||us.next(this,wi)[0]||us.findOne(wi,t.delegateTarget.parentNode),r=Mi.getOrCreateInstance(n);if(i)return t.stopPropagation(),r.show(),void r._selectMenuItem(t);r._isShown()&&(t.stopPropagation(),r.hide(),n.focus())}}es.on(document,bi,wi,Mi.dataApiKeydownHandler),es.on(document,bi,Ci,Mi.dataApiKeydownHandler),es.on(document,_i,Mi.clearMenus),es.on(document,yi,Mi.clearMenus),es.on(document,_i,wi,(function(t){t.preventDefault(),Mi.getOrCreateInstance(this).toggle()})),Be(Mi);const Pi="backdrop",Li="show",Bi=`mousedown.bs.${Pi}`,Vi={className:"modal-backdrop",clickCallback:null,isAnimated:!1,isVisible:!0,rootElement:"body"},Ni={className:"string",clickCallback:"(function|null)",isAnimated:"boolean",isVisible:"boolean",rootElement:"(element|string)"};class $i extends os{constructor(t){super(),this._config=this._getConfig(t),this._isAppended=!1,this._element=null}static get Default(){return Vi}static get DefaultType(){return Ni}static get NAME(){return Pi}show(t){if(!this._config.isVisible)return void Ve(t);this._append();const e=this._getElement();this._config.isAnimated&&De(e),e.classList.add(Li),this._emulateAnimation((()=>{Ve(t)}))}hide(t){this._config.isVisible?(this._getElement().classList.remove(Li),this._emulateAnimation((()=>{this.dispose(),Ve(t)}))):Ve(t)}dispose(){this._isAppended&&(es.off(this._element,Bi),this._element.remove(),this._isAppended=!1)}_getElement(){if(!this._element){const t=document.createElement("div");t.className=this._config.className,this._config.isAnimated&&t.classList.add("fade"),this._element=t}return this._element}_configAfterMerge(t){return t.rootElement=Se(t.rootElement),t}_append(){if(this._isAppended)return;const t=this._getElement();this._config.rootElement.append(t),es.on(t,Bi,(()=>{Ve(this._config.clickCallback)})),this._isAppended=!0}_emulateAnimation(t){Ne(t,this._getElement(),this._config.isAnimated)}}const ji=".bs.focustrap",Ri=`focusin${ji}`,qi=`keydown.tab${ji}`,zi="backward",Hi={autofocus:!0,trapElement:null},Ui={autofocus:"boolean",trapElement:"element"};class Wi extends os{constructor(t){super(),this._config=this._getConfig(t),this._isActive=!1,this._lastTabNavDirection=null}static get Default(){return Hi}static get DefaultType(){return Ui}static get NAME(){return"focustrap"}activate(){this._isActive||(this._config.autofocus&&this._config.trapElement.focus(),es.off(document,ji),es.on(document,Ri,(t=>this._handleFocusin(t))),es.on(document,qi,(t=>this._handleKeydown(t))),this._isActive=!0)}deactivate(){this._isActive&&(this._isActive=!1,es.off(document,ji))}_handleFocusin(t){const{trapElement:e}=this._config;if(t.target===document||t.target===e||e.contains(t.target))return;const s=us.focusableChildren(e);0===s.length?e.focus():this._lastTabNavDirection===zi?s[s.length-1].focus():s[0].focus()}_handleKeydown(t){"Tab"===t.key&&(this._lastTabNavDirection=t.shiftKey?zi:"forward")}}const Ki=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",Yi=".sticky-top",Qi="padding-right",Xi="margin-right";class Gi{constructor(){this._element=document.body}getWidth(){const t=document.documentElement.clientWidth;return Math.abs(window.innerWidth-t)}hide(){const t=this.getWidth();this._disableOverFlow(),this._setElementAttributes(this._element,Qi,(e=>e+t)),this._setElementAttributes(Ki,Qi,(e=>e+t)),this._setElementAttributes(Yi,Xi,(e=>e-t))}reset(){this._resetElementAttributes(this._element,"overflow"),this._resetElementAttributes(this._element,Qi),this._resetElementAttributes(Ki,Qi),this._resetElementAttributes(Yi,Xi)}isOverflowing(){return this.getWidth()>0}_disableOverFlow(){this._saveInitialAttribute(this._element,"overflow"),this._element.style.overflow="hidden"}_setElementAttributes(t,e,s){const i=this.getWidth();this._applyManipulationCallback(t,(t=>{if(t!==this._element&&window.innerWidth>t.clientWidth+i)return;this._saveInitialAttribute(t,e);const n=window.getComputedStyle(t).getPropertyValue(e);t.style.setProperty(e,`${s(Number.parseFloat(n))}px`)}))}_saveInitialAttribute(t,e){const s=t.style.getPropertyValue(e);s&&rs.setDataAttribute(t,e,s)}_resetElementAttributes(t,e){this._applyManipulationCallback(t,(t=>{const s=rs.getDataAttribute(t,e);null!==s?(rs.removeDataAttribute(t,e),t.style.setProperty(e,s)):t.style.removeProperty(e)}))}_applyManipulationCallback(t,e){if(xe(t))e(t);else for(const s of us.find(t,this._element))e(s)}}const Zi=".bs.modal",Ji=`hide${Zi}`,tn=`hidePrevented${Zi}`,en=`hidden${Zi}`,sn=`show${Zi}`,nn=`shown${Zi}`,rn=`resize${Zi}`,on=`click.dismiss${Zi}`,an=`mousedown.dismiss${Zi}`,ln=`keydown.dismiss${Zi}`,un=`click${Zi}.data-api`,cn="modal-open",hn="show",dn="modal-static",pn={backdrop:!0,focus:!0,keyboard:!0},fn={backdrop:"(boolean|string)",focus:"boolean",keyboard:"boolean"};class gn extends as{constructor(t,e){super(t,e),this._dialog=us.findOne(".modal-dialog",this._element),this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._isShown=!1,this._isTransitioning=!1,this._scrollBar=new Gi,this._addEventListeners()}static get Default(){return pn}static get DefaultType(){return fn}static get NAME(){return"modal"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown||this._isTransitioning)return;es.trigger(this._element,sn,{relatedTarget:t}).defaultPrevented||(this._isShown=!0,this._isTransitioning=!0,this._scrollBar.hide(),document.body.classList.add(cn),this._adjustDialog(),this._backdrop.show((()=>this._showElement(t))))}hide(){if(!this._isShown||this._isTransitioning)return;es.trigger(this._element,Ji).defaultPrevented||(this._isShown=!1,this._isTransitioning=!0,this._focustrap.deactivate(),this._element.classList.remove(hn),this._queueCallback((()=>this._hideModal()),this._element,this._isAnimated()))}dispose(){es.off(window,Zi),es.off(this._dialog,Zi),this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}handleUpdate(){this._adjustDialog()}_initializeBackDrop(){return new $i({isVisible:Boolean(this._config.backdrop),isAnimated:this._isAnimated()})}_initializeFocusTrap(){return new Wi({trapElement:this._element})}_showElement(t){document.body.contains(this._element)||document.body.append(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.scrollTop=0;const e=us.findOne(".modal-body",this._dialog);e&&(e.scrollTop=0),De(this._element),this._element.classList.add(hn);this._queueCallback((()=>{this._config.focus&&this._focustrap.activate(),this._isTransitioning=!1,es.trigger(this._element,nn,{relatedTarget:t})}),this._dialog,this._isAnimated())}_addEventListeners(){es.on(this._element,ln,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():this._triggerBackdropTransition())})),es.on(window,rn,(()=>{this._isShown&&!this._isTransitioning&&this._adjustDialog()})),es.on(this._element,an,(t=>{es.one(this._element,on,(e=>{this._element===t.target&&this._element===e.target&&("static"!==this._config.backdrop?this._config.backdrop&&this.hide():this._triggerBackdropTransition())}))}))}_hideModal(){this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._isTransitioning=!1,this._backdrop.hide((()=>{document.body.classList.remove(cn),this._resetAdjustments(),this._scrollBar.reset(),es.trigger(this._element,en)}))}_isAnimated(){return this._element.classList.contains("fade")}_triggerBackdropTransition(){if(es.trigger(this._element,tn).defaultPrevented)return;const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._element.style.overflowY;"hidden"===e||this._element.classList.contains(dn)||(t||(this._element.style.overflowY="hidden"),this._element.classList.add(dn),this._queueCallback((()=>{this._element.classList.remove(dn),this._queueCallback((()=>{this._element.style.overflowY=e}),this._dialog)}),this._dialog),this._element.focus())}_adjustDialog(){const t=this._element.scrollHeight>document.documentElement.clientHeight,e=this._scrollBar.getWidth(),s=e>0;if(s&&!t){const t=Le()?"paddingLeft":"paddingRight";this._element.style[t]=`${e}px`}if(!s&&t){const t=Le()?"paddingRight":"paddingLeft";this._element.style[t]=`${e}px`}}_resetAdjustments(){this._element.style.paddingLeft="",this._element.style.paddingRight=""}static jQueryInterface(t,e){return this.each((function(){const s=gn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===s[t])throw new TypeError(`No method named "${t}"`);s[t](e)}}))}}es.on(document,un,'[data-bs-toggle="modal"]',(function(t){const e=us.getElementFromSelector(this);["A","AREA"].includes(this.tagName)&&t.preventDefault(),es.one(e,sn,(t=>{t.defaultPrevented||es.one(e,en,(()=>{Oe(this)&&this.focus()}))}));const s=us.findOne(".modal.show");s&&gn.getInstance(s).hide();gn.getOrCreateInstance(e).toggle(this)})),cs(gn),Be(gn);const mn=".bs.offcanvas",vn=".data-api",_n=`load${mn}${vn}`,bn="show",yn="showing",kn="hiding",wn=".offcanvas.show",An=`show${mn}`,Cn=`shown${mn}`,En=`hide${mn}`,xn=`hidePrevented${mn}`,Sn=`hidden${mn}`,On=`resize${mn}`,Fn=`click${mn}${vn}`,Tn=`keydown.dismiss${mn}`,In={backdrop:!0,keyboard:!0,scroll:!1},Dn={backdrop:"(boolean|string)",keyboard:"boolean",scroll:"boolean"};class Mn extends as{constructor(t,e){super(t,e),this._isShown=!1,this._backdrop=this._initializeBackDrop(),this._focustrap=this._initializeFocusTrap(),this._addEventListeners()}static get Default(){return In}static get DefaultType(){return Dn}static get NAME(){return"offcanvas"}toggle(t){return this._isShown?this.hide():this.show(t)}show(t){if(this._isShown)return;if(es.trigger(this._element,An,{relatedTarget:t}).defaultPrevented)return;this._isShown=!0,this._backdrop.show(),this._config.scroll||(new Gi).hide(),this._element.setAttribute("aria-modal",!0),this._element.setAttribute("role","dialog"),this._element.classList.add(yn);this._queueCallback((()=>{this._config.scroll&&!this._config.backdrop||this._focustrap.activate(),this._element.classList.add(bn),this._element.classList.remove(yn),es.trigger(this._element,Cn,{relatedTarget:t})}),this._element,!0)}hide(){if(!this._isShown)return;if(es.trigger(this._element,En).defaultPrevented)return;this._focustrap.deactivate(),this._element.blur(),this._isShown=!1,this._element.classList.add(kn),this._backdrop.hide();this._queueCallback((()=>{this._element.classList.remove(bn,kn),this._element.removeAttribute("aria-modal"),this._element.removeAttribute("role"),this._config.scroll||(new Gi).reset(),es.trigger(this._element,Sn)}),this._element,!0)}dispose(){this._backdrop.dispose(),this._focustrap.deactivate(),super.dispose()}_initializeBackDrop(){const t=Boolean(this._config.backdrop);return new $i({className:"offcanvas-backdrop",isVisible:t,isAnimated:!0,rootElement:this._element.parentNode,clickCallback:t?()=>{"static"!==this._config.backdrop?this.hide():es.trigger(this._element,xn)}:null})}_initializeFocusTrap(){return new Wi({trapElement:this._element})}_addEventListeners(){es.on(this._element,Tn,(t=>{"Escape"===t.key&&(this._config.keyboard?this.hide():es.trigger(this._element,xn))}))}static jQueryInterface(t){return this.each((function(){const e=Mn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}es.on(document,Fn,'[data-bs-toggle="offcanvas"]',(function(t){const e=us.getElementFromSelector(this);if(["A","AREA"].includes(this.tagName)&&t.preventDefault(),Fe(this))return;es.one(e,Sn,(()=>{Oe(this)&&this.focus()}));const s=us.findOne(wn);s&&s!==e&&Mn.getInstance(s).hide();Mn.getOrCreateInstance(e).toggle(this)})),es.on(window,_n,(()=>{for(const t of us.find(wn))Mn.getOrCreateInstance(t).show()})),es.on(window,On,(()=>{for(const t of us.find("[aria-modal][class*=show][class*=offcanvas-]"))"fixed"!==getComputedStyle(t).position&&Mn.getOrCreateInstance(t).hide()})),cs(Mn),Be(Mn);const Pn={"*":["class","dir","id","lang","role",/^aria-[\w-]*$/i],a:["target","href","title","rel"],area:[],b:[],br:[],col:[],code:[],div:[],em:[],hr:[],h1:[],h2:[],h3:[],h4:[],h5:[],h6:[],i:[],img:["src","srcset","alt","title","width","height"],li:[],ol:[],p:[],pre:[],s:[],small:[],span:[],sub:[],sup:[],strong:[],u:[],ul:[]},Ln=new Set(["background","cite","href","itemtype","longdesc","poster","src","xlink:href"]),Bn=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:/?#]*(?:[/?#]|$))/i,Vn=(t,e)=>{const s=t.nodeName.toLowerCase();return e.includes(s)?!Ln.has(s)||Boolean(Bn.test(t.nodeValue)):e.filter((t=>t instanceof RegExp)).some((t=>t.test(s)))};const Nn={allowList:Pn,content:{},extraClass:"",html:!1,sanitize:!0,sanitizeFn:null,template:"<div></div>"},$n={allowList:"object",content:"object",extraClass:"(string|function)",html:"boolean",sanitize:"boolean",sanitizeFn:"(null|function)",template:"string"},jn={entry:"(string|element|function|null)",selector:"(string|element)"};class Rn extends os{constructor(t){super(),this._config=this._getConfig(t)}static get Default(){return Nn}static get DefaultType(){return $n}static get NAME(){return"TemplateFactory"}getContent(){return Object.values(this._config.content).map((t=>this._resolvePossibleFunction(t))).filter(Boolean)}hasContent(){return this.getContent().length>0}changeContent(t){return this._checkContent(t),this._config.content={...this._config.content,...t},this}toHtml(){const t=document.createElement("div");t.innerHTML=this._maybeSanitize(this._config.template);for(const[e,s]of Object.entries(this._config.content))this._setContent(t,s,e);const e=t.children[0],s=this._resolvePossibleFunction(this._config.extraClass);return s&&e.classList.add(...s.split(" ")),e}_typeCheckConfig(t){super._typeCheckConfig(t),this._checkContent(t.content)}_checkContent(t){for(const[e,s]of Object.entries(t))super._typeCheckConfig({selector:e,entry:s},jn)}_setContent(t,e,s){const i=us.findOne(s,t);i&&((e=this._resolvePossibleFunction(e))?xe(e)?this._putElementInTemplate(Se(e),i):this._config.html?i.innerHTML=this._maybeSanitize(e):i.textContent=e:i.remove())}_maybeSanitize(t){return this._config.sanitize?function(t,e,s){if(!t.length)return t;if(s&&"function"==typeof s)return s(t);const i=(new window.DOMParser).parseFromString(t,"text/html"),n=[].concat(...i.body.querySelectorAll("*"));for(const t of n){const s=t.nodeName.toLowerCase();if(!Object.keys(e).includes(s)){t.remove();continue}const i=[].concat(...t.attributes),n=[].concat(e["*"]||[],e[s]||[]);for(const e of i)Vn(e,n)||t.removeAttribute(e.nodeName)}return i.body.innerHTML}(t,this._config.allowList,this._config.sanitizeFn):t}_resolvePossibleFunction(t){return Ve(t,[this])}_putElementInTemplate(t,e){if(this._config.html)return e.innerHTML="",void e.append(t);e.textContent=t.textContent}}const qn=new Set(["sanitize","allowList","sanitizeFn"]),zn="fade",Hn="show",Un=".modal",Wn="hide.bs.modal",Kn="hover",Yn="focus",Qn={AUTO:"auto",TOP:"top",RIGHT:Le()?"left":"right",BOTTOM:"bottom",LEFT:Le()?"right":"left"},Xn={allowList:Pn,animation:!0,boundary:"clippingParents",container:!1,customClass:"",delay:0,fallbackPlacements:["top","right","bottom","left"],html:!1,offset:[0,6],placement:"top",popperConfig:null,sanitize:!0,sanitizeFn:null,selector:!1,template:'<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',title:"",trigger:"hover focus"},Gn={allowList:"object",animation:"boolean",boundary:"(string|element)",container:"(string|element|boolean)",customClass:"(string|function)",delay:"(number|object)",fallbackPlacements:"array",html:"boolean",offset:"(array|string|function)",placement:"(string|function)",popperConfig:"(null|object|function)",sanitize:"boolean",sanitizeFn:"(null|function)",selector:"(string|boolean)",template:"string",title:"(string|element|function)",trigger:"string"};class Zn extends as{constructor(e,s){if(void 0===t)throw new TypeError("Bootstrap's tooltips require Popper (https://popper.js.org)");super(e,s),this._isEnabled=!0,this._timeout=0,this._isHovered=null,this._activeTrigger={},this._popper=null,this._templateFactory=null,this._newContent=null,this.tip=null,this._setListeners(),this._config.selector||this._fixTitle()}static get Default(){return Xn}static get DefaultType(){return Gn}static get NAME(){return"tooltip"}enable(){this._isEnabled=!0}disable(){this._isEnabled=!1}toggleEnabled(){this._isEnabled=!this._isEnabled}toggle(){this._isEnabled&&(this._activeTrigger.click=!this._activeTrigger.click,this._isShown()?this._leave():this._enter())}dispose(){clearTimeout(this._timeout),es.off(this._element.closest(Un),Wn,this._hideModalHandler),this._element.getAttribute("data-bs-original-title")&&this._element.setAttribute("title",this._element.getAttribute("data-bs-original-title")),this._disposePopper(),super.dispose()}show(){if("none"===this._element.style.display)throw new Error("Please use show on visible elements");if(!this._isWithContent()||!this._isEnabled)return;const t=es.trigger(this._element,this.constructor.eventName("show")),e=(Te(this._element)||this._element.ownerDocument.documentElement).contains(this._element);if(t.defaultPrevented||!e)return;this._disposePopper();const s=this._getTipElement();this._element.setAttribute("aria-describedby",s.getAttribute("id"));const{container:i}=this._config;if(this._element.ownerDocument.documentElement.contains(this.tip)||(i.append(s),es.trigger(this._element,this.constructor.eventName("inserted"))),this._popper=this._createPopper(s),s.classList.add(Hn),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))es.on(t,"mouseover",Ie);this._queueCallback((()=>{es.trigger(this._element,this.constructor.eventName("shown")),!1===this._isHovered&&this._leave(),this._isHovered=!1}),this.tip,this._isAnimated())}hide(){if(!this._isShown())return;if(es.trigger(this._element,this.constructor.eventName("hide")).defaultPrevented)return;if(this._getTipElement().classList.remove(Hn),"ontouchstart"in document.documentElement)for(const t of[].concat(...document.body.children))es.off(t,"mouseover",Ie);this._activeTrigger.click=!1,this._activeTrigger[Yn]=!1,this._activeTrigger[Kn]=!1,this._isHovered=null;this._queueCallback((()=>{this._isWithActiveTrigger()||(this._isHovered||this._disposePopper(),this._element.removeAttribute("aria-describedby"),es.trigger(this._element,this.constructor.eventName("hidden")))}),this.tip,this._isAnimated())}update(){this._popper&&this._popper.update()}_isWithContent(){return Boolean(this._getTitle())}_getTipElement(){return this.tip||(this.tip=this._createTipElement(this._newContent||this._getContentForTemplate())),this.tip}_createTipElement(t){const e=this._getTemplateFactory(t).toHtml();if(!e)return null;e.classList.remove(zn,Hn),e.classList.add(`bs-${this.constructor.NAME}-auto`);const s=(t=>{do{t+=Math.floor(1e6*Math.random())}while(document.getElementById(t));return t})(this.constructor.NAME).toString();return e.setAttribute("id",s),this._isAnimated()&&e.classList.add(zn),e}setContent(t){this._newContent=t,this._isShown()&&(this._disposePopper(),this.show())}_getTemplateFactory(t){return this._templateFactory?this._templateFactory.changeContent(t):this._templateFactory=new Rn({...this._config,content:t,extraClass:this._resolvePossibleFunction(this._config.customClass)}),this._templateFactory}_getContentForTemplate(){return{".tooltip-inner":this._getTitle()}}_getTitle(){return this._resolvePossibleFunction(this._config.title)||this._element.getAttribute("data-bs-original-title")}_initializeOnDelegatedTarget(t){return this.constructor.getOrCreateInstance(t.delegateTarget,this._getDelegateConfig())}_isAnimated(){return this._config.animation||this.tip&&this.tip.classList.contains(zn)}_isShown(){return this.tip&&this.tip.classList.contains(Hn)}_createPopper(t){const e=Ve(this._config.placement,[this,t,this._element]),s=Qn[e.toUpperCase()];return be(this._element,t,this._getPopperConfig(s))}_getOffset(){const{offset:t}=this._config;return"string"==typeof t?t.split(",").map((t=>Number.parseInt(t,10))):"function"==typeof t?e=>t(e,this._element):t}_resolvePossibleFunction(t){return Ve(t,[this._element])}_getPopperConfig(t){const e={placement:t,modifiers:[{name:"flip",options:{fallbackPlacements:this._config.fallbackPlacements}},{name:"offset",options:{offset:this._getOffset()}},{name:"preventOverflow",options:{boundary:this._config.boundary}},{name:"arrow",options:{element:`.${this.constructor.NAME}-arrow`}},{name:"preSetPlacement",enabled:!0,phase:"beforeMain",fn:t=>{this._getTipElement().setAttribute("data-popper-placement",t.state.placement)}}]};return{...e,...Ve(this._config.popperConfig,[e])}}_setListeners(){const t=this._config.trigger.split(" ");for(const e of t)if("click"===e)es.on(this._element,this.constructor.eventName("click"),this._config.selector,(t=>{this._initializeOnDelegatedTarget(t).toggle()}));else if("manual"!==e){const t=e===Kn?this.constructor.eventName("mouseenter"):this.constructor.eventName("focusin"),s=e===Kn?this.constructor.eventName("mouseleave"):this.constructor.eventName("focusout");es.on(this._element,t,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusin"===t.type?Yn:Kn]=!0,e._enter()})),es.on(this._element,s,this._config.selector,(t=>{const e=this._initializeOnDelegatedTarget(t);e._activeTrigger["focusout"===t.type?Yn:Kn]=e._element.contains(t.relatedTarget),e._leave()}))}this._hideModalHandler=()=>{this._element&&this.hide()},es.on(this._element.closest(Un),Wn,this._hideModalHandler)}_fixTitle(){const t=this._element.getAttribute("title");t&&(this._element.getAttribute("aria-label")||this._element.textContent.trim()||this._element.setAttribute("aria-label",t),this._element.setAttribute("data-bs-original-title",t),this._element.removeAttribute("title"))}_enter(){this._isShown()||this._isHovered?this._isHovered=!0:(this._isHovered=!0,this._setTimeout((()=>{this._isHovered&&this.show()}),this._config.delay.show))}_leave(){this._isWithActiveTrigger()||(this._isHovered=!1,this._setTimeout((()=>{this._isHovered||this.hide()}),this._config.delay.hide))}_setTimeout(t,e){clearTimeout(this._timeout),this._timeout=setTimeout(t,e)}_isWithActiveTrigger(){return Object.values(this._activeTrigger).includes(!0)}_getConfig(t){const e=rs.getDataAttributes(this._element);for(const t of Object.keys(e))qn.has(t)&&delete e[t];return t={...e,..."object"==typeof t&&t?t:{}},t=this._mergeConfigObj(t),t=this._configAfterMerge(t),this._typeCheckConfig(t),t}_configAfterMerge(t){return t.container=!1===t.container?document.body:Se(t.container),"number"==typeof t.delay&&(t.delay={show:t.delay,hide:t.delay}),"number"==typeof t.title&&(t.title=t.title.toString()),"number"==typeof t.content&&(t.content=t.content.toString()),t}_getDelegateConfig(){const t={};for(const[e,s]of Object.entries(this._config))this.constructor.Default[e]!==s&&(t[e]=s);return t.selector=!1,t.trigger="manual",t}_disposePopper(){this._popper&&(this._popper.destroy(),this._popper=null),this.tip&&(this.tip.remove(),this.tip=null)}static jQueryInterface(t){return this.each((function(){const e=Zn.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}Be(Zn);const Jn={...Zn.Default,content:"",offset:[0,8],placement:"right",template:'<div class="popover" role="tooltip"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',trigger:"click"},tr={...Zn.DefaultType,content:"(null|string|element|function)"};class er extends Zn{static get Default(){return Jn}static get DefaultType(){return tr}static get NAME(){return"popover"}_isWithContent(){return this._getTitle()||this._getContent()}_getContentForTemplate(){return{".popover-header":this._getTitle(),".popover-body":this._getContent()}}_getContent(){return this._resolvePossibleFunction(this._config.content)}static jQueryInterface(t){return this.each((function(){const e=er.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t]()}}))}}Be(er);const sr=".bs.scrollspy",ir=`activate${sr}`,nr=`click${sr}`,rr=`load${sr}.data-api`,or="active",ar="[href]",lr=".nav-link",ur=`${lr}, .nav-item > ${lr}, .list-group-item`,cr={offset:null,rootMargin:"0px 0px -25%",smoothScroll:!1,target:null,threshold:[.1,.5,1]},hr={offset:"(number|null)",rootMargin:"string",smoothScroll:"boolean",target:"element",threshold:"array"};class dr extends as{constructor(t,e){super(t,e),this._targetLinks=new Map,this._observableSections=new Map,this._rootElement="visible"===getComputedStyle(this._element).overflowY?null:this._element,this._activeTarget=null,this._observer=null,this._previousScrollData={visibleEntryTop:0,parentScrollTop:0},this.refresh()}static get Default(){return cr}static get DefaultType(){return hr}static get NAME(){return"scrollspy"}refresh(){this._initializeTargetsAndObservables(),this._maybeEnableSmoothScroll(),this._observer?this._observer.disconnect():this._observer=this._getNewObserver();for(const t of this._observableSections.values())this._observer.observe(t)}dispose(){this._observer.disconnect(),super.dispose()}_configAfterMerge(t){return t.target=Se(t.target)||document.body,t.rootMargin=t.offset?`${t.offset}px 0px -30%`:t.rootMargin,"string"==typeof t.threshold&&(t.threshold=t.threshold.split(",").map((t=>Number.parseFloat(t)))),t}_maybeEnableSmoothScroll(){this._config.smoothScroll&&(es.off(this._config.target,nr),es.on(this._config.target,nr,ar,(t=>{const e=this._observableSections.get(t.target.hash);if(e){t.preventDefault();const s=this._rootElement||window,i=e.offsetTop-this._element.offsetTop;if(s.scrollTo)return void s.scrollTo({top:i,behavior:"smooth"});s.scrollTop=i}})))}_getNewObserver(){const t={root:this._rootElement,threshold:this._config.threshold,rootMargin:this._config.rootMargin};return new IntersectionObserver((t=>this._observerCallback(t)),t)}_observerCallback(t){const e=t=>this._targetLinks.get(`#${t.target.id}`),s=t=>{this._previousScrollData.visibleEntryTop=t.target.offsetTop,this._process(e(t))},i=(this._rootElement||document.documentElement).scrollTop,n=i>=this._previousScrollData.parentScrollTop;this._previousScrollData.parentScrollTop=i;for(const r of t){if(!r.isIntersecting){this._activeTarget=null,this._clearActiveClass(e(r));continue}const t=r.target.offsetTop>=this._previousScrollData.visibleEntryTop;if(n&&t){if(s(r),!i)return}else n||t||s(r)}}_initializeTargetsAndObservables(){this._targetLinks=new Map,this._observableSections=new Map;const t=us.find(ar,this._config.target);for(const e of t){if(!e.hash||Fe(e))continue;const t=us.findOne(decodeURI(e.hash),this._element);Oe(t)&&(this._targetLinks.set(decodeURI(e.hash),e),this._observableSections.set(e.hash,t))}}_process(t){this._activeTarget!==t&&(this._clearActiveClass(this._config.target),this._activeTarget=t,t.classList.add(or),this._activateParents(t),es.trigger(this._element,ir,{relatedTarget:t}))}_activateParents(t){if(t.classList.contains("dropdown-item"))us.findOne(".dropdown-toggle",t.closest(".dropdown")).classList.add(or);else for(const e of us.parents(t,".nav, .list-group"))for(const t of us.prev(e,ur))t.classList.add(or)}_clearActiveClass(t){t.classList.remove(or);const e=us.find(`${ar}.${or}`,t);for(const t of e)t.classList.remove(or)}static jQueryInterface(t){return this.each((function(){const e=dr.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}es.on(window,rr,(()=>{for(const t of us.find('[data-bs-spy="scroll"]'))dr.getOrCreateInstance(t)})),Be(dr);const pr=".bs.tab",fr=`hide${pr}`,gr=`hidden${pr}`,mr=`show${pr}`,vr=`shown${pr}`,_r=`click${pr}`,br=`keydown${pr}`,yr=`load${pr}`,kr="ArrowLeft",wr="ArrowRight",Ar="ArrowUp",Cr="ArrowDown",Er="Home",xr="End",Sr="active",Or="fade",Fr="show",Tr=":not(.dropdown-toggle)",Ir='[data-bs-toggle="tab"], [data-bs-toggle="pill"], [data-bs-toggle="list"]',Dr=`${`.nav-link${Tr}, .list-group-item${Tr}, [role="tab"]${Tr}`}, ${Ir}`,Mr=`.${Sr}[data-bs-toggle="tab"], .${Sr}[data-bs-toggle="pill"], .${Sr}[data-bs-toggle="list"]`;class Pr extends as{constructor(t){super(t),this._parent=this._element.closest('.list-group, .nav, [role="tablist"]'),this._parent&&(this._setInitialAttributes(this._parent,this._getChildren()),es.on(this._element,br,(t=>this._keydown(t))))}static get NAME(){return"tab"}show(){const t=this._element;if(this._elemIsActive(t))return;const e=this._getActiveElem(),s=e?es.trigger(e,fr,{relatedTarget:t}):null;es.trigger(t,mr,{relatedTarget:e}).defaultPrevented||s&&s.defaultPrevented||(this._deactivate(e,t),this._activate(t,e))}_activate(t,e){if(!t)return;t.classList.add(Sr),this._activate(us.getElementFromSelector(t));this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.removeAttribute("tabindex"),t.setAttribute("aria-selected",!0),this._toggleDropDown(t,!0),es.trigger(t,vr,{relatedTarget:e})):t.classList.add(Fr)}),t,t.classList.contains(Or))}_deactivate(t,e){if(!t)return;t.classList.remove(Sr),t.blur(),this._deactivate(us.getElementFromSelector(t));this._queueCallback((()=>{"tab"===t.getAttribute("role")?(t.setAttribute("aria-selected",!1),t.setAttribute("tabindex","-1"),this._toggleDropDown(t,!1),es.trigger(t,gr,{relatedTarget:e})):t.classList.remove(Fr)}),t,t.classList.contains(Or))}_keydown(t){if(![kr,wr,Ar,Cr,Er,xr].includes(t.key))return;t.stopPropagation(),t.preventDefault();const e=this._getChildren().filter((t=>!Fe(t)));let s;if([Er,xr].includes(t.key))s=e[t.key===Er?0:e.length-1];else{const i=[wr,Cr].includes(t.key);s=$e(e,t.target,i,!0)}s&&(s.focus({preventScroll:!0}),Pr.getOrCreateInstance(s).show())}_getChildren(){return us.find(Dr,this._parent)}_getActiveElem(){return this._getChildren().find((t=>this._elemIsActive(t)))||null}_setInitialAttributes(t,e){this._setAttributeIfNotExists(t,"role","tablist");for(const t of e)this._setInitialAttributesOnChild(t)}_setInitialAttributesOnChild(t){t=this._getInnerElement(t);const e=this._elemIsActive(t),s=this._getOuterElement(t);t.setAttribute("aria-selected",e),s!==t&&this._setAttributeIfNotExists(s,"role","presentation"),e||t.setAttribute("tabindex","-1"),this._setAttributeIfNotExists(t,"role","tab"),this._setInitialAttributesOnTargetPanel(t)}_setInitialAttributesOnTargetPanel(t){const e=us.getElementFromSelector(t);e&&(this._setAttributeIfNotExists(e,"role","tabpanel"),t.id&&this._setAttributeIfNotExists(e,"aria-labelledby",`${t.id}`))}_toggleDropDown(t,e){const s=this._getOuterElement(t);if(!s.classList.contains("dropdown"))return;const i=(t,i)=>{const n=us.findOne(t,s);n&&n.classList.toggle(i,e)};i(".dropdown-toggle",Sr),i(".dropdown-menu",Fr),s.setAttribute("aria-expanded",e)}_setAttributeIfNotExists(t,e,s){t.hasAttribute(e)||t.setAttribute(e,s)}_elemIsActive(t){return t.classList.contains(Sr)}_getInnerElement(t){return t.matches(Dr)?t:us.findOne(Dr,t)}_getOuterElement(t){return t.closest(".nav-item, .list-group-item")||t}static jQueryInterface(t){return this.each((function(){const e=Pr.getOrCreateInstance(this);if("string"==typeof t){if(void 0===e[t]||t.startsWith("_")||"constructor"===t)throw new TypeError(`No method named "${t}"`);e[t]()}}))}}es.on(document,_r,Ir,(function(t){["A","AREA"].includes(this.tagName)&&t.preventDefault(),Fe(this)||Pr.getOrCreateInstance(this).show()})),es.on(window,yr,(()=>{for(const t of us.find(Mr))Pr.getOrCreateInstance(t)})),Be(Pr);const Lr=".bs.toast",Br=`mouseover${Lr}`,Vr=`mouseout${Lr}`,Nr=`focusin${Lr}`,$r=`focusout${Lr}`,jr=`hide${Lr}`,Rr=`hidden${Lr}`,qr=`show${Lr}`,zr=`shown${Lr}`,Hr="hide",Ur="show",Wr="showing",Kr={animation:"boolean",autohide:"boolean",delay:"number"},Yr={animation:!0,autohide:!0,delay:5e3};class Qr extends as{constructor(t,e){super(t,e),this._timeout=null,this._hasMouseInteraction=!1,this._hasKeyboardInteraction=!1,this._setListeners()}static get Default(){return Yr}static get DefaultType(){return Kr}static get NAME(){return"toast"}show(){if(es.trigger(this._element,qr).defaultPrevented)return;this._clearTimeout(),this._config.animation&&this._element.classList.add("fade");this._element.classList.remove(Hr),De(this._element),this._element.classList.add(Ur,Wr),this._queueCallback((()=>{this._element.classList.remove(Wr),es.trigger(this._element,zr),this._maybeScheduleHide()}),this._element,this._config.animation)}hide(){if(!this.isShown())return;if(es.trigger(this._element,jr).defaultPrevented)return;this._element.classList.add(Wr),this._queueCallback((()=>{this._element.classList.add(Hr),this._element.classList.remove(Wr,Ur),es.trigger(this._element,Rr)}),this._element,this._config.animation)}dispose(){this._clearTimeout(),this.isShown()&&this._element.classList.remove(Ur),super.dispose()}isShown(){return this._element.classList.contains(Ur)}_maybeScheduleHide(){this._config.autohide&&(this._hasMouseInteraction||this._hasKeyboardInteraction||(this._timeout=setTimeout((()=>{this.hide()}),this._config.delay)))}_onInteraction(t,e){switch(t.type){case"mouseover":case"mouseout":this._hasMouseInteraction=e;break;case"focusin":case"focusout":this._hasKeyboardInteraction=e}if(e)return void this._clearTimeout();const s=t.relatedTarget;this._element===s||this._element.contains(s)||this._maybeScheduleHide()}_setListeners(){es.on(this._element,Br,(t=>this._onInteraction(t,!0))),es.on(this._element,Vr,(t=>this._onInteraction(t,!1))),es.on(this._element,Nr,(t=>this._onInteraction(t,!0))),es.on(this._element,$r,(t=>this._onInteraction(t,!1)))}_clearTimeout(){clearTimeout(this._timeout),this._timeout=null}static jQueryInterface(t){return this.each((function(){const e=Qr.getOrCreateInstance(this,t);if("string"==typeof t){if(void 0===e[t])throw new TypeError(`No method named "${t}"`);e[t](this)}}))}}cs(Qr),Be(Qr),[].slice.call(document.querySelectorAll('[data-bs-toggle="dropdown"]')).map((function(t){let e={boundary:"viewport"===t.getAttribute("data-bs-boundary")?document.querySelector(".btn"):"clippingParents"};return new Mi(t,e)})),[].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]')).map((function(t){let e={delay:{show:50,hide:50},html:"true"===t.getAttribute("data-bs-html")??!1,placement:t.getAttribute("data-bs-placement")??"auto"};return new Zn(t,e)})),[].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]')).map((function(t){let e={delay:{show:50,hide:50},html:"true"===t.getAttribute("data-bs-html")??!1,placement:t.getAttribute("data-bs-placement")??"auto"};return new er(t,e)}));s(6080);(()=>{const t=window.location.hash;if(t){[].slice.call(document.querySelectorAll('[data-bs-toggle="tab"]')).filter((e=>e.hash===t)).map((t=>{new Pr(t).show()}))}})(),[].slice.call(document.querySelectorAll('[data-bs-toggle="toast"]')).map((function(t){return new Qr(t)}));const Xr="tblr-",Gr=(t,e)=>{const s=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(t);return s?`rgba(${parseInt(s[1],16)}, ${parseInt(s[2],16)}, ${parseInt(s[3],16)}, ${e})`:null},Zr=(t,e=1)=>{const s=getComputedStyle(document.body).getPropertyValue(`--${Xr}${t}`).trim();return 1!==e?Gr(s,e):s};globalThis.bootstrap=e,globalThis.tabler=i;var Jr=s(4183),to=s.n(Jr),eo=s(4865),so=s.n(eo);function io(t){var e=document.createElement("style");e.textContent="\n        #nprogress {\n          pointer-events: none;\n        }\n\n        #nprogress .bar {\n          background: ".concat(t,";\n\n          position: fixed;\n          z-index: 1031;\n          top: 0;\n          left: 0;\n\n          width: 100%;\n          height: 2px;\n        }\n\n        #nprogress .peg {\n          display: block;\n          position: absolute;\n          right: 0px;\n          width: 100px;\n          height: 100%;\n          box-shadow: 0 0 10px ").concat(t,", 0 0 5px ").concat(t,";\n          opacity: 1.0;\n\n          -webkit-transform: rotate(3deg) translate(0px, -4px);\n              -ms-transform: rotate(3deg) translate(0px, -4px);\n                  transform: rotate(3deg) translate(0px, -4px);\n        }\n\n        #nprogress .spinner {\n          display: block;\n          position: fixed;\n          z-index: 1031;\n          top: 15px;\n          right: 15px;\n        }\n\n        #nprogress .spinner-icon {\n          width: 18px;\n          height: 18px;\n          box-sizing: border-box;\n\n          border: solid 2px transparent;\n          border-top-color: ").concat(t,";\n          border-left-color: ").concat(t,";\n          border-radius: 50%;\n\n          -webkit-animation: nprogress-spinner 400ms linear infinite;\n                  animation: nprogress-spinner 400ms linear infinite;\n        }\n\n        .nprogress-custom-parent {\n          overflow: hidden;\n          position: relative;\n        }\n\n        .nprogress-custom-parent #nprogress .spinner,\n        .nprogress-custom-parent #nprogress .bar {\n          position: absolute;\n        }\n\n        @-webkit-keyframes nprogress-spinner {\n          0%   { -webkit-transform: rotate(0deg); }\n          100% { -webkit-transform: rotate(360deg); }\n        }\n        @keyframes nprogress-spinner {\n          0%   { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n    "),document.head.appendChild(e)}io("#007bff");s(7330);!function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.delay,s=void 0===e?250:e,i=t.color,n=void 0===i?"var(--bb-primary)":i,r=t.includeCSS,o=void 0===r||r,a=t.showSpinner,l=void 0!==a&&a;$(document).on("ajaxSend",(function(){return so().inc(s)})),$(document).on("ajaxStop",(function(){return so().done()})),$httpClient.beforeSend((function(){return so().inc(s)})),$httpClient.completed((function(){return so().done()})),so().configure({showSpinner:l}),o&&io(n)}({showSpinner:!0}),window.TomSelect=to()})()})();
<?php

namespace Botble\Base\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class DomainRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! preg_match('/^(?:[a-z0-9](?:[a-z0-9-æ<PERSON><PERSON>]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/iu', $value)) {
            $fail(trans('core/base::base.validation.domain'));
        }
    }
}

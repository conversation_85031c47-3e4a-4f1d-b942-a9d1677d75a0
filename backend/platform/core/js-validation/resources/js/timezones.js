/*!
 * Laravel Javascript Validation
 *
 * https://github.com/proengsoft/laravel-jsvalidation
 *
 * Timezone Helper functions used by validators
 *
 * Copyright (c) 2017 Proengsoft
 * Released under the MIT license
 */

$.extend(true, laravelValidation, {
    helpers: {
        /**
         * Check if the specified timezone is valid.
         *
         * @param value
         * @returns {boolean}
         */
        isTimezone: function (value) {
            var timezones = {
                africa: [
                    'abidjan',
                    'accra',
                    'addis_ababa',
                    'algiers',
                    'asmara',
                    'bamako',
                    'bangui',
                    'banjul',
                    'bissau',
                    'blantyre',
                    'brazzaville',
                    'bujumbura',
                    'cairo',
                    'casablanca',
                    'ceuta',
                    'conakry',
                    'dakar',
                    'dar_es_salaam',
                    'djibouti',
                    'douala',
                    'el_aaiun',
                    'freetown',
                    'gaborone',
                    'harare',
                    'johannesburg',
                    'juba',
                    'kampala',
                    'khartoum',
                    'kigali',
                    'kinshasa',
                    'lagos',
                    'libreville',
                    'lome',
                    'luanda',
                    'lubumbashi',
                    'lusaka',
                    'malabo',
                    'maputo',
                    'maseru',
                    'mbabane',
                    'mogadishu',
                    'monrovia',
                    'nairobi',
                    'ndjamena',
                    'niamey',
                    'nouakchott',
                    'ouagadougou',
                    'porto-novo',
                    'sao_tome',
                    'tripoli',
                    'tunis',
                    'windhoek',
                ],
                america: [
                    'adak',
                    'anchorage',
                    'anguilla',
                    'antigua',
                    'araguaina',
                    'argentina/buenos_aires',
                    'argentina/catamarca',
                    'argentina/cordoba',
                    'argentina/jujuy',
                    'argentina/la_rioja',
                    'argentina/mendoza',
                    'argentina/rio_gallegos',
                    'argentina/salta',
                    'argentina/san_juan',
                    'argentina/san_luis',
                    'argentina/tucuman',
                    'argentina/ushuaia',
                    'aruba',
                    'asuncion',
                    'atikokan',
                    'bahia',
                    'bahia_banderas',
                    'barbados',
                    'belem',
                    'belize',
                    'blanc-sablon',
                    'boa_vista',
                    'bogota',
                    'boise',
                    'cambridge_bay',
                    'campo_grande',
                    'cancun',
                    'caracas',
                    'cayenne',
                    'cayman',
                    'chicago',
                    'chihuahua',
                    'costa_rica',
                    'creston',
                    'cuiaba',
                    'curacao',
                    'danmarkshavn',
                    'dawson',
                    'dawson_creek',
                    'denver',
                    'detroit',
                    'dominica',
                    'edmonton',
                    'eirunepe',
                    'el_salvador',
                    'fortaleza',
                    'glace_bay',
                    'godthab',
                    'goose_bay',
                    'grand_turk',
                    'grenada',
                    'guadeloupe',
                    'guatemala',
                    'guayaquil',
                    'guyana',
                    'halifax',
                    'havana',
                    'hermosillo',
                    'indiana/indianapolis',
                    'indiana/knox',
                    'indiana/marengo',
                    'indiana/petersburg',
                    'indiana/tell_city',
                    'indiana/vevay',
                    'indiana/vincennes',
                    'indiana/winamac',
                    'inuvik',
                    'iqaluit',
                    'jamaica',
                    'juneau',
                    'kentucky/louisville',
                    'kentucky/monticello',
                    'kralendijk',
                    'la_paz',
                    'lima',
                    'los_angeles',
                    'lower_princes',
                    'maceio',
                    'managua',
                    'manaus',
                    'marigot',
                    'martinique',
                    'matamoros',
                    'mazatlan',
                    'menominee',
                    'merida',
                    'metlakatla',
                    'mexico_city',
                    'miquelon',
                    'moncton',
                    'monterrey',
                    'montevideo',
                    'montreal',
                    'montserrat',
                    'nassau',
                    'new_york',
                    'nipigon',
                    'nome',
                    'noronha',
                    'north_dakota/beulah',
                    'north_dakota/center',
                    'north_dakota/new_salem',
                    'ojinaga',
                    'panama',
                    'pangnirtung',
                    'paramaribo',
                    'phoenix',
                    'port-au-prince',
                    'port_of_spain',
                    'porto_velho',
                    'puerto_rico',
                    'rainy_river',
                    'rankin_inlet',
                    'recife',
                    'regina',
                    'resolute',
                    'rio_branco',
                    'santa_isabel',
                    'santarem',
                    'santiago',
                    'santo_domingo',
                    'sao_paulo',
                    'scoresbysund',
                    'shiprock',
                    'sitka',
                    'st_barthelemy',
                    'st_johns',
                    'st_kitts',
                    'st_lucia',
                    'st_thomas',
                    'st_vincent',
                    'swift_current',
                    'tegucigalpa',
                    'thule',
                    'thunder_bay',
                    'tijuana',
                    'toronto',
                    'tortola',
                    'vancouver',
                    'whitehorse',
                    'winnipeg',
                    'yakutat',
                    'yellowknife',
                ],
                antarctica: [
                    'casey',
                    'davis',
                    'dumontdurville',
                    'macquarie',
                    'mawson',
                    'mcmurdo',
                    'palmer',
                    'rothera',
                    'south_pole',
                    'syowa',
                    'vostok',
                ],
                arctic: ['longyearbyen'],
                asia: [
                    'aden',
                    'almaty',
                    'amman',
                    'anadyr',
                    'aqtau',
                    'aqtobe',
                    'ashgabat',
                    'baghdad',
                    'bahrain',
                    'baku',
                    'bangkok',
                    'beirut',
                    'bishkek',
                    'brunei',
                    'choibalsan',
                    'chongqing',
                    'colombo',
                    'damascus',
                    'dhaka',
                    'dili',
                    'dubai',
                    'dushanbe',
                    'gaza',
                    'harbin',
                    'hebron',
                    'ho_chi_minh',
                    'hong_kong',
                    'hovd',
                    'irkutsk',
                    'jakarta',
                    'jayapura',
                    'jerusalem',
                    'kabul',
                    'kamchatka',
                    'karachi',
                    'kashgar',
                    'kathmandu',
                    'khandyga',
                    'kolkata',
                    'krasnoyarsk',
                    'kuala_lumpur',
                    'kuching',
                    'kuwait',
                    'macau',
                    'magadan',
                    'makassar',
                    'manila',
                    'muscat',
                    'nicosia',
                    'novokuznetsk',
                    'novosibirsk',
                    'omsk',
                    'oral',
                    'phnom_penh',
                    'pontianak',
                    'pyongyang',
                    'qatar',
                    'qyzylorda',
                    'rangoon',
                    'riyadh',
                    'sakhalin',
                    'samarkand',
                    'seoul',
                    'shanghai',
                    'singapore',
                    'taipei',
                    'tashkent',
                    'tbilisi',
                    'tehran',
                    'thimphu',
                    'tokyo',
                    'ulaanbaatar',
                    'urumqi',
                    'ust-nera',
                    'vientiane',
                    'vladivostok',
                    'yakutsk',
                    'yekaterinburg',
                    'yerevan',
                ],
                atlantic: [
                    'azores',
                    'bermuda',
                    'canary',
                    'cape_verde',
                    'faroe',
                    'madeira',
                    'reykjavik',
                    'south_georgia',
                    'st_helena',
                    'stanley',
                ],
                australia: [
                    'adelaide',
                    'brisbane',
                    'broken_hill',
                    'currie',
                    'darwin',
                    'eucla',
                    'hobart',
                    'lindeman',
                    'lord_howe',
                    'melbourne',
                    'perth',
                    'sydney',
                ],
                europe: [
                    'amsterdam',
                    'andorra',
                    'athens',
                    'belgrade',
                    'berlin',
                    'bratislava',
                    'brussels',
                    'bucharest',
                    'budapest',
                    'busingen',
                    'chisinau',
                    'copenhagen',
                    'dublin',
                    'gibraltar',
                    'guernsey',
                    'helsinki',
                    'isle_of_man',
                    'istanbul',
                    'jersey',
                    'kaliningrad',
                    'kiev',
                    'lisbon',
                    'ljubljana',
                    'london',
                    'luxembourg',
                    'madrid',
                    'malta',
                    'mariehamn',
                    'minsk',
                    'monaco',
                    'moscow',
                    'oslo',
                    'paris',
                    'podgorica',
                    'prague',
                    'riga',
                    'rome',
                    'samara',
                    'san_marino',
                    'sarajevo',
                    'simferopol',
                    'skopje',
                    'sofia',
                    'stockholm',
                    'tallinn',
                    'tirane',
                    'uzhgorod',
                    'vaduz',
                    'vatican',
                    'vienna',
                    'vilnius',
                    'volgograd',
                    'warsaw',
                    'zagreb',
                    'zaporozhye',
                    'zurich',
                ],
                indian: [
                    'antananarivo',
                    'chagos',
                    'christmas',
                    'cocos',
                    'comoro',
                    'kerguelen',
                    'mahe',
                    'maldives',
                    'mauritius',
                    'mayotte',
                    'reunion',
                ],
                pacific: [
                    'apia',
                    'auckland',
                    'chatham',
                    'chuuk',
                    'easter',
                    'efate',
                    'enderbury',
                    'fakaofo',
                    'fiji',
                    'funafuti',
                    'galapagos',
                    'gambier',
                    'guadalcanal',
                    'guam',
                    'honolulu',
                    'johnston',
                    'kiritimati',
                    'kosrae',
                    'kwajalein',
                    'majuro',
                    'marquesas',
                    'midway',
                    'nauru',
                    'niue',
                    'norfolk',
                    'noumea',
                    'pago_pago',
                    'palau',
                    'pitcairn',
                    'pohnpei',
                    'port_moresby',
                    'rarotonga',
                    'saipan',
                    'tahiti',
                    'tarawa',
                    'tongatapu',
                    'wake',
                    'wallis',
                ],
                utc: [''],
            }

            var tzparts = value.split('/', 2)
            var continent = tzparts[0].toLowerCase()
            var city = ''
            if (tzparts[1]) {
                city = tzparts[1].toLowerCase()
            }

            return (
                continent in timezones &&
                (timezones[continent].length === 0 || timezones[continent].indexOf(city) !== -1)
            )
        },
    },
})

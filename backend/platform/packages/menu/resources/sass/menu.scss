.dd-item {
    position: relative;
}

.dd-item,
.dd-empty,
.dd-placeholder {
    margin: 10px 0;
}

.dd-placeholder {
    padding: 0;
    min-height: 30px;
    background: rgba(var(--bb-info-rgb), 0.1);
    border: var(--bb-card-border-width) dashed var(--bb-border-color);
    box-sizing: border-box;
    display: block;
    position: relative;
    font-size: 13px;
    line-height: 20px;
}

.dd3-content {
    width: 350px;
    border: 0 none;
    height: 40px;
    padding: 10px;
    background: var(--bb-bg-surface);
    color: var(--bb-body-color);
}

.dd3-handle {
    position: absolute;
    margin: 0;
    left: 0;
    top: 0;
    cursor: move;
    width: 363px;
    text-indent: 100%;
    white-space: nowrap;
    overflow: hidden;
    border: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);
    background: transparent;
    height: 40px;
    border-radius: var(--bb-border-radius);

    &:hover {
        background-color: transparent;
    }
}

.dd-item {
    > .item-details {
        border: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);
        border-top: 0 none;
        display: none;
        margin-bottom: 5px;
        padding: 10px 15px;
        max-width: 363px;
    }
}

.dd3-handle:before {
    display: none;
}

.dd3-content {
    .show-item-details {
        background-color: var(--bb-body-bg);
        left: 319px;
        position: absolute;
        text-align: center;
        top: 1px;
        height: 38px;
        width: 43px;
        color: var(--bb-secondary);
        border-left: var(--bb-border-width) var(--bb-border-style) var(--bb-border-color);
        border-bottom-right-radius: var(--bb-border-radius);
        border-top-right-radius: var(--bb-border-radius);
        display: inline-flex;
        justify-content: center;
        align-items: center;

        &.active {
            .icon {
                transform: rotate(180deg);
            }
        }
    }
}

.dd-item {
    &.active {
        > .item-details {
            background: var(--bb-bg-surface);
            display: block;

            > label {
                float: left;
                width: 100%;
                display: block;
            }

            > div.text-end {
                float: none;
                clear: both;

                .btn {
                    margin-left: 7px;
                }
            }
        }

        > .dd3-handle {
            border-bottom-right-radius: 0;
            border-bottom-left-radius: 0;
        }
    }

    > .item-details {
        .form-group {
            align-items: center;
            display: flex;

            > .control-label {
                width: 90px;
            }

            > .form-control:not(select),
            > .ui-select-wrapper {
                width: 228px;
            }

            > .ui-select-wrapper {
                margin-bottom: 0;
            }
        }
    }
}

.core-menu-structure {
    .card {
        .card-header {
            a {
                color: var(--bb-card-title-color);

                .btn-action {
                    height: 1.5rem;
                }

                &[aria-expanded='true'] {
                    .btn-action {
                        span.icon-sm {
                            rotate: 180deg;
                        }
                    }
                }
            }
        }
    }
}

.box-links-for-menu {
    .list-item {
        padding-left: 0;

        .list-item {
            border: 0 none;
            max-height: none;
            overflow: visible;
            padding: 0 0 0 20px;
        }

        li {
            list-style: none;
            position: relative;
            margin-bottom: 5px;

            label.form-check {
                margin-bottom: 0.75rem;
            }

            ul {
                padding-left: 20px;
            }

            .checker {
                margin-top: -25px;
            }

            a {
                display: inline-block;
                padding-left: 20px;
                width: 100%;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                -ms-text-overflow: ellipsis;
            }
        }
    }
}

.form-save-menu {
    #menu_name {
        margin-bottom: 5px;
    }
}

.dd3-content {
    > span.text {
        &.float-start {
            display: inline-block;
            max-width: 200px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            -ms-text-overflow: ellipsis;
        }

        &.float-end {
            margin-right: 25px;
            display: inline-block;
            max-width: 85px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            -ms-text-overflow: ellipsis;
        }
    }
}

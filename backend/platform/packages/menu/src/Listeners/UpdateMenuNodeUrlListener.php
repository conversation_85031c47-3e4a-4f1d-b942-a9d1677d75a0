<?php

namespace Bo<PERSON>ble\Menu\Listeners;

use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Bo<PERSON>ble\Base\Models\BaseModel;
use Bo<PERSON>ble\Base\Supports\RepositoryHelper;
use Botble\Menu\Facades\Menu;
use Botble\Menu\Models\MenuNode;
use Bo<PERSON>ble\Slug\Events\UpdatedSlugEvent;
use Exception;

class UpdateMenuNodeUrlListener
{
    public function handle(UpdatedSlugEvent $event): void
    {
        if (
            ! $event->data instanceof BaseModel ||
            ! in_array($event->data::class, Menu::getMenuOptionModels())
        ) {
            return;
        }

        try {
            $query = MenuNode::query()
                ->where([
                    'reference_id' => $event->data->getKey(),
                    'reference_type' => $event->data::class,
                ]);

            $nodes = RepositoryHelper::applyBeforeExecuteQuery($query, $event->data)->get();

            foreach ($nodes as $node) {
                $newUrl = str_replace(url(''), '', $node->reference->url);
                if ($node->url != $newUrl) {
                    $node->url = $newUrl;
                    $node->save();
                }
            }
        } catch (Exception $exception) {
            BaseHelper::logError($exception);
        }
    }
}

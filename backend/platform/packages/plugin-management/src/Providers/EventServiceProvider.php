<?php

namespace <PERSON><PERSON><PERSON>\PluginManagement\Providers;

use Bo<PERSON>ble\Base\Events\SeederPrepared;
use Bo<PERSON>ble\Base\Events\SystemUpdateDBMigrated;
use Bo<PERSON>ble\Base\Events\SystemUpdatePublished;
use Bo<PERSON>ble\Base\Listeners\ClearDashboardMenuCaches;
use Bo<PERSON>ble\PluginManagement\Events\ActivatedPluginEvent;
use Bo<PERSON>ble\PluginManagement\Listeners\ActivateAllPlugins;
use Botble\PluginManagement\Listeners\ClearPluginCaches;
use Botble\PluginManagement\Listeners\CoreUpdatePluginsDB;
use Botble\PluginManagement\Listeners\PublishPluginAssets;
use Illuminate\Contracts\Database\Events\MigrationEvent;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        MigrationEvent::class => [
            ClearPluginCaches::class,
        ],
        SystemUpdateDBMigrated::class => [
            CoreUpdatePluginsDB::class,
        ],
        SystemUpdatePublished::class => [
            PublishPluginAssets::class,
        ],
        SeederPrepared::class => [
            ActivateAllPlugins::class,
        ],
        ActivatedPluginEvent::class => [
            ClearDashboardMenuCaches::class,
        ],
    ];
}

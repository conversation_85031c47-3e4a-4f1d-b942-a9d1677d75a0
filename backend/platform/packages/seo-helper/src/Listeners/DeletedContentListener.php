<?php

namespace Bo<PERSON><PERSON>\SeoHelper\Listeners;

use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Botble\Base\Facades\BaseHelper;
use Botble\SeoHelper\Facades\SeoHelper;
use Exception;

class DeletedContentListener
{
    public function handle(DeletedContentEvent $event): void
    {
        try {
            SeoHelper::deleteMetaData($event->screen, $event->data);
        } catch (Exception $exception) {
            BaseHelper::logError($exception);
        }
    }
}

(()=>{"use strict";$((function(){$(document).on("change",".shortcode-tabs-quantity-select",(function(){var a=$(this),t=parseInt(a.val())||1,e=a.data("key");a.val(t);var n=a.closest(".shortcode-tabs-field-wrapper");n.length||(n=a.closest(".shortcode-admin-config")),n.find(".shortcode-template").first().clone().removeClass("shortcode-template");for(var o=1;o<=a.data("max");o++){var d=e?n.find("[data-tab-id=".concat(e,"_").concat(o,"]")):n.find("[data-tab-id=".concat(o,"]"));o<=t?d.is(":visible")||(d.slideDown(),d.find("[data-name]").map((function(a,t){return $(t).prop("name",$(t).data("name"))}))):(d.slideUp(),d.find("[name]").map((function(a,t){$(t).data("name",$(t).prop("name")),$(t).removeProp("name")})))}}))}))})();
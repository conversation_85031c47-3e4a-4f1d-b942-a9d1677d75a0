<?php

namespace Bo<PERSON>ble\Sitemap\Http\Controllers;

use Bo<PERSON>ble\Base\Services\ClearCacheService;
use Bo<PERSON>ble\Setting\Http\Controllers\SettingController;
use Bo<PERSON>ble\Sitemap\Forms\Settings\SitemapSettingForm;
use Bo<PERSON>ble\Sitemap\Http\Requests\SitemapSettingRequest;

class SitemapSettingController extends SettingController
{
    public function edit()
    {
        $this->pageTitle(trans('packages/sitemap::sitemap.settings.title'));

        return SitemapSettingForm::create()->renderForm();
    }

    public function update(SitemapSettingRequest $request)
    {
        // Check if sitemap_items_per_page has changed
        $oldItemsPerPage = setting('sitemap_items_per_page');
        $newItemsPerPage = $request->input('sitemap_items_per_page');

        $response = $this->performUpdate($request->validated());

        // Clear sitemap cache if sitemap_enabled or sitemap_items_per_page has changed
        if ($request->has('sitemap_enabled') || ($oldItemsPerPage != $newItemsPerPage && $newItemsPerPage)) {
            // Use the new centralized method to clear all sitemap caches
            ClearCacheService::make()->clearFrameworkCache();
        }

        return $response->withUpdatedSuccessMessage();
    }
}

<?php

namespace Bo<PERSON><PERSON>\Slug\Listeners;

use <PERSON><PERSON>ble\Base\Contracts\BaseModel;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use <PERSON><PERSON>ble\Slug\Facades\SlugHelper;
use Bo<PERSON>ble\Slug\Models\Slug;

class DeletedContentListener
{
    public function handle(DeletedContentEvent $event): void
    {
        if ($event->data instanceof BaseModel && SlugHelper::isSupportedModel($event->data::class)) {
            Slug::query()->where([
                'reference_id' => $event->data->getKey(),
                'reference_type' => $event->data::class,
            ])->delete();
        }
    }
}

<?php

namespace Bo<PERSON><PERSON>\Slug\Providers;

use Botble\Base\Supports\ServiceProvider;
use Bo<PERSON>ble\Slug\Commands\ChangeSlugPrefixCommand;

class CommandServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (! $this->app->runningInConsole()) {
            return;
        }

        $this->commands([
            ChangeSlugPrefixCommand::class,
        ]);
    }
}

<?php

namespace <PERSON><PERSON><PERSON>\Slug\Providers;

use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use <PERSON><PERSON>ble\Base\Events\FinishedSeederEvent;
use Bo<PERSON>ble\Base\Events\SeederPrepared;
use Bo<PERSON>ble\Base\Events\UpdatedContentEvent;
use <PERSON><PERSON>ble\Slug\Listeners\CreatedContentListener;
use Bo<PERSON>ble\Slug\Listeners\CreateMissingSlug;
use Bo<PERSON>ble\Slug\Listeners\DeletedContentListener;
use Bo<PERSON>ble\Slug\Listeners\TruncateSlug;
use Bo<PERSON>ble\Slug\Listeners\UpdatedContentListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        UpdatedContentEvent::class => [
            UpdatedContentListener::class,
        ],
        CreatedContentEvent::class => [
            CreatedContentListener::class,
        ],
        DeletedContentEvent::class => [
            DeletedContentListener::class,
        ],
        SeederPrepared::class => [
            TruncateSlug::class,
        ],
        FinishedSeederEvent::class => [
            CreateMissingSlug::class,
        ],
    ];
}

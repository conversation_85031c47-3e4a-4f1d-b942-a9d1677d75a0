<?php

if (! defined('THEME_FRONT_FOOTER')) {
    define('THEME_FRONT_FOOTER', 'theme-front-footer');
}

if (! defined('THEME_FRONT_HEADER')) {
    define('THEME_FRONT_HEADER', 'theme-front-header');
}

if (! defined('THEME_FRONT_BODY')) {
    define('THEME_FRONT_BODY', 'theme-front-body');
}

if (! defined('THEME_MODULE_SCREEN_NAME')) {
    define('THEME_MODULE_SCREEN_NAME', 'theme');
}

if (! defined('THEME_OPTIONS_MODULE_SCREEN_NAME')) {
    define('THEME_OPTIONS_MODULE_SCREEN_NAME', 'theme-options');
}

if (! defined('THEME_OPTIONS_ACTION_META_BOXES')) {
    define('THEME_OPTIONS_ACTION_META_BOXES', 'theme-options-action-meta-boxes');
}

if (! defined('RENDERING_THEME_OPTIONS_PAGE')) {
    define('RENDERING_THEME_OPTIONS_PAGE', 'rendering-theme-options-page');
}

<?php

namespace Bo<PERSON>ble\Theme\Http\Controllers;

use Bo<PERSON><PERSON>\Setting\Http\Controllers\SettingController;
use Bo<PERSON>ble\Theme\Forms\Settings\WebsiteTrackingSettingForm;
use Bo<PERSON>ble\Theme\Http\Requests\WebsiteTrackingSettingRequest;

class WebsiteTrackingSettingController extends SettingController
{
    public function edit()
    {
        $this->pageTitle(trans('packages/theme::theme.settings.website_tracking.title'));

        return WebsiteTrackingSettingForm::create()->renderForm();
    }

    public function update(WebsiteTrackingSettingRequest $request)
    {
        return $this->performUpdate(
            $request->validated()
        )->withUpdatedSuccessMessage();
    }
}

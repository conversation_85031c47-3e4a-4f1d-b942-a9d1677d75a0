<?php

return [
    'name' => 'Widgets',
    'description' => 'Manage your system widgets',
    'create' => 'New widget',
    'edit' => 'Edit widget',
    'delete' => 'Delete',
    'available' => 'Available Widgets',
    'usage_instruction' => 'To activate a widget drag and drop it to a sidebar. To deactivate a widget, open it in sidebar and click delete button.',
    'number_tag_display' => 'Number tags will be display',
    'number_post_display' => 'Number posts will be display',
    'select_menu' => 'Select Menu',
    'widget_text' => 'Text',
    'widget_text_description' => 'Arbitrary text or HTML.',
    'widget_recent_post' => 'Recent Posts',
    'widget_recent_post_description' => 'Recent posts widget.',
    'widget_custom_menu' => 'Custom Menu',
    'widget_custom_menu_description' => 'Add a custom menu to your widget area.',
    'widget_tag' => 'Tags',
    'widget_tag_description' => 'Popular tags',
    'widget_menu' => 'Simple Menu',
    'widget_menu_description' => 'Add a simple menu to your widget area.',
    'widget_menu_label' => 'Label',
    'widget_menu_attributes' => 'Attributes',
    'widget_menu_url' => 'URL',
    'widget_menu_is_open_new_tab' => 'Is open new tab?',
    'save_success' => 'Save widget successfully!',
    'delete_success' => 'Delete widget successfully!',
    'primary_sidebar_name' => 'Primary sidebar',
    'primary_sidebar_description' => 'Primary sidebar section',
    'drag_widget_to_sidebar' => 'Drag and drop widgets to this area.',
    'home_page_name' => 'Home page',
    'home_page_description' => 'Home page section',
    'footer' => 'Footer',
    'footer_description' => 'Footer section',
];

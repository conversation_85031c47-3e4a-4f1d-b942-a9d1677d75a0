(()=>{function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t(e)}function e(t,e){for(var n=0;n<e.length;n++){var a=e[n];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,o(a.key),a)}}function o(e){var o=function(e,o){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var a=n.call(e,o||"default");if("object"!=t(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===o?String:Number)(e)}(e,"string");return"symbol"==t(o)?o:o+""}var n=function(){return t=function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)},(o=[{key:"init",value:function(){var t=$("#table-backups");t.on("click",".deleteDialog",(function(t){t.preventDefault(),$(".delete-crud-entry").data("section",$(t.currentTarget).data("section")),$(".modal-confirm-delete").modal("show")})),t.on("click",".restoreBackup",(function(t){t.preventDefault(),$("#restore-backup-button").data("section",$(t.currentTarget).data("section")),$("#restore-backup-modal").modal("show")})),$(".delete-crud-entry").on("click",(function(e){e.preventDefault(),$(".modal-confirm-delete").modal("hide");var o=$(e.currentTarget).data("section");$httpClient.make().delete(o).then((function(e){var n=e.data;t.find("tbody tr").length<=1&&t.load(window.location.href+" #table-backups > *"),t.find('button[data-section="'.concat(o,'"]')).closest("tr").remove(),Botble.showSuccess(n.message)}))})),$("#restore-backup-button").on("click",(function(t){t.preventDefault();var e=$(t.currentTarget);Botble.showButtonLoading(e),$httpClient.make().get(e.data("section")).then((function(t){var o=t.data;e.closest(".modal").modal("hide"),Botble.showSuccess(o.message),window.location.reload()})).finally((function(){Botble.hideButtonLoading(e)}))})),$(document).on("click","#generate_backup",(function(t){t.preventDefault(),$("#name").val(""),$("#description").val(""),$("#create-backup-modal").modal("show")})),$("#create-backup-modal").on("click","#create-backup-button",(function(e){e.preventDefault();var o=$(e.currentTarget),n=o.closest("form");$httpClient.make().withButtonLoading(o).post(n.prop("action"),new FormData(n[0])).then((function(e){var o=e.data;t.find(".no-backup-row").remove(),t.find("tbody").append(o.data),Botble.showSuccess(o.message)})).finally((function(){o.closest(".modal").modal("hide")}))}))}}])&&e(t.prototype,o),n&&e(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t;var t,o,n}();$((function(){(new n).init()}))})();
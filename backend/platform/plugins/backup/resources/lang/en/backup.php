<?php

return [
    'name' => 'Backup',
    'backup_description' => 'Backup database and uploads folder.',
    'create_backup_success' => 'Backup has been successfully created',
    'delete_backup_success' => 'Backup has been successfully deleted',
    'restore_backup_success' => 'Backup has been successfully restored',
    'generate_btn' => 'Generate backup',
    'create' => 'Create a backup',
    'restore' => 'Restore a backup',
    'create_btn' => 'Create',
    'restore_btn' => 'Restore',
    'restore_confirm_msg' => 'Do you really want to restore this revision?',
    'download_uploads_folder' => 'Download backup of "uploads" folder',
    'download_database' => 'Download database backup',
    'restore_tooltip' => 'Restore this backup',
    'demo_alert' => 'Hi guest, if you see demo site is destroyed, please help us by <a href=":link">going here</a> and restore this demo site to the latest revision! Thank you so much!',
    'menu_name' => 'Backups',
    'size' => 'Size',
    'no_backups' => 'There is no backup now.',
    'proc_open_disabled_error' => 'The PHP function <code>proc_open()</code> is currently disabled, preventing the system from creating a backup. Please contact your hosting provider to enable this function.',
    'database_backup_not_existed' => 'Database backup does not exist.',
    'uploads_folder_backup_not_existed' => 'The backup for "uploads" folder does not exist.',
    'important_message1' => 'This simple backup feature is ideal for website having less than 1GB of data. A quick and easy way to create backups.',
    'important_message2' => 'For larger websites with over 1GB of images or files, consider using the backup features provided by your hosting or VPS provider.',
    'important_message3' => 'To back up your database, the PHP function <code>proc_open()</code> or <code>system()</code> must be enabled. Contact your hosting provider to enable these functions if needed.',
    'important_message4' => 'It is not a full backup, only uploaded files and database are included.',
    'important_message_pgsql1' => '<strong>PostgreSQL</strong> database backups are currently unavailable through the web interface, because PostgreSQL does not allow direct password entry during database exports.',
    'important_message_pgsql2' => 'You can run the command <code>php artisan cms:backup:create {name}</code> to generate the backup or <code>php artisan cms:backup:restore</code> to restore the latest backup.',
    'cannot_restore_database' => 'Cannot restore database. The database backup is missing!',
    'database_driver_not_supported' => 'Database driver is not supported.',
    'backup_only_db' => 'Backup only database',
    'backup_only_db_helper' => 'Backup only database without uploaded files. This is useful when you want to backup the database only.',
];

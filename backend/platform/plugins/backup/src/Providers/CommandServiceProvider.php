<?php

namespace Bo<PERSON><PERSON>\Backup\Providers;

use Bo<PERSON><PERSON>\Backup\Commands\BackupCleanCommand;
use Bo<PERSON>ble\Backup\Commands\BackupCreateCommand;
use Bo<PERSON>ble\Backup\Commands\BackupListCommand;
use Bo<PERSON>ble\Backup\Commands\BackupRemoveCommand;
use Botble\Backup\Commands\BackupRestoreCommand;
use Botble\Base\Supports\ServiceProvider;

class CommandServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (! $this->app->runningInConsole()) {
            return;
        }

        $this->commands([
            BackupCreateCommand::class,
            BackupRestoreCommand::class,
            BackupRemoveCommand::class,
            BackupListCommand::class,
            BackupCleanCommand::class,
        ]);
    }
}

# Author Stats Widget - Fixes Applied

## Vấn đề gốc
- Lỗi `SQLSTATE[42S22]: Column not found: 1054 Unknown column 'name' in 'field list'`
- Widget không hoạt động do sai cấu trúc database

## Nguyên nhân
1. **Cột `name` không tồn tại**: Bảng `users` có `first_name` và `last_name` thay vì `name`
2. **Sử dụng sai model**: Cần sử dụng PostCustom models thay vì Blog models gốc
3. **Views tracking**: Cần sử dụng `post_scores` view thay vì `posts.views`

## Các sửa đổi đã thực hiện

### 1. AuthorStatsController.php
- **Thêm import**: `CSlant\Blog\PostCustom\Models\PostScore` và `PostCustom`
- **Sửa query users**: Sử dụng `first_name`, `last_name` thay vì `name`
- **Thêm fallback logic**: Nếu `post_scores` không tồn tại, fallback về `posts.views`
- **Error handling**: Wrap tất cả queries trong try-catch
- **Cập nhật getWidgetAuthorStats**: Thêm fallback và error handling
- **Cập nhật getAuthorDetailedStats**: Sử dụng PostCustom model với fallback

### 2. Views
- **author-stats/index.blade.php**: 
  - Sửa hiển thị tên: `trim($author->first_name . ' ' . $author->last_name)`
  - Bỏ `posts_count` vì không có trong query mới
- **author-stats/detailed-stats.blade.php**:
  - Sử dụng `$post->postScore->views` thay vì `$post->views`
  - Thêm null check cho postScore relationship
- **widgets/author-stats.blade.php**:
  - Thêm hiển thị lỗi nếu có
  - Cải thiện error handling

### 3. Fallback Strategy
```php
// Thử post_scores trước
try {
    $totalViews = DB::table('post_scores')
        ->join('posts', 'post_scores.id', '=', 'posts.id')
        ->where('posts.author_type', 'Botble\ACL\Models\User')
        ->sum('post_scores.views');
} catch (\Exception $e) {
    // Fallback về posts.views
    $totalViews = DB::table('posts')
        ->where('author_type', 'Botble\ACL\Models\User')
        ->sum('views');
}
```

### 4. Name Handling
```php
// Xử lý tên từ first_name + last_name
$topAuthorName = $user ? trim(($user->first_name ?? '') . ' ' . ($user->last_name ?? '')) : 'N/A';
if (empty(trim($topAuthorName))) {
    $topAuthorName = $user->email ?? 'N/A';
}
```

## Kết quả
- ✅ Widget không còn lỗi SQL
- ✅ Hiển thị đúng tên tác giả
- ✅ Sử dụng đúng views tracking từ PostScore
- ✅ Có fallback nếu PostScore không khả dụng
- ✅ Error handling toàn diện
- ✅ Tương thích với cả PostCustom và Blog models gốc

## Cách test
1. Truy cập admin dashboard
2. Kiểm tra widget "Thống kê tác giả"
3. Truy cập `/admin/blog/author-stats`
4. Kiểm tra filter và thống kê chi tiết

## Lưu ý
- Widget sẽ tự động detect và sử dụng PostScore nếu có
- Nếu PostScore không có, sẽ fallback về posts.views
- Tên tác giả được tạo từ first_name + last_name
- Nếu tên trống, sẽ hiển thị email
- Tất cả lỗi đều được handle gracefully

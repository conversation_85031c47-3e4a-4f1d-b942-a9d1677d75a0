# Thống kê tác giả - Author Statistics

## <PERSON><PERSON> tả
Tính năng thống kê tác giả cho phép xem lượt xem tổng của tất cả bài viết của một tác giả với khả năng tuỳ chỉnh khoảng thời gian.

## Tính năng chính

### 1. Widget Dashboard
- Hiển thị tổng quan thống kê tác giả trên dashboard admin
- Thông tin: T<PERSON><PERSON> tá<PERSON> gi<PERSON>, T<PERSON><PERSON> lư<PERSON> xem, <PERSON>á<PERSON> giả hàng đầu
- Liên kết nhanh đến trang thống kê chi tiết

### 2. Trang thống kê chi tiết
- <PERSON><PERSON> lọc theo tác giả và khoảng thời gian
- Bảng thống kê tổng quan tất cả tác giả
- Thống kê chi tiết cho tác gi<PERSON> được chọn
- <PERSON><PERSON><PERSON><PERSON> đồ lượt xem theo tháng
- <PERSON><PERSON> sách bài viết của tác giả

### 3. <PERSON><PERSON> lọc thời gian
- T<PERSON><PERSON> cả thời gian
- Hôm nay
- Tuần này
- Tháng này
- Năm này
- Tùy chỉnh (chọn ngày bắt đầu và kết thúc)

## Cách sử dụng

### Truy cập thống kê
1. Đăng nhập vào admin panel
2. Vào menu **Blog > Thống kê tác giả**
3. Hoặc click vào widget "Thống kê tác giả" trên dashboard

### Lọc dữ liệu
1. Chọn tác giả cần xem thống kê (hoặc để trống để xem tất cả)
2. Chọn khoảng thời gian:
   - Chọn preset (hôm nay, tuần này, tháng này, năm này)
   - Hoặc chọn "Tùy chỉnh" và nhập ngày cụ thể
3. Click "Lọc" để áp dụng

### Xem chi tiết tác giả
1. Trong bảng thống kê, click "Chi tiết" ở cột hành động
2. Xem thông tin chi tiết:
   - Tổng bài viết, lượt xem, trung bình lượt xem
   - Biểu đồ lượt xem theo tháng
   - Danh sách tất cả bài viết của tác giả

## Cấu trúc file

### Controllers
- `AuthorStatsController.php` - Xử lý logic thống kê

### Views
- `author-stats/index.blade.php` - Trang thống kê chính
- `author-stats/detailed-stats.blade.php` - Chi tiết thống kê tác giả
- `widgets/author-stats.blade.php` - Widget dashboard

### Assets
- `public/css/author-stats.css` - Styling
- `public/js/author-stats.js` - JavaScript tương tác

### Language
- `resources/lang/vi/author-stats.php` - Tiếng Việt
- `resources/lang/en/author-stats.php` - Tiếng Anh

### Routes
- `GET /admin/blog/author-stats` - Trang thống kê chính
- `GET /admin/blog/posts/widgets/author-stats` - Widget data

## Quyền truy cập
- Cần quyền `posts.index` để truy cập tính năng
- Tác giả được tính từ bảng `users` (không phải `members`)

## Lưu ý kỹ thuật
- Sử dụng relationship `author_type = 'Botble\ACL\Models\User'`
- Hỗ trợ Chart.js cho biểu đồ
- Responsive design cho mobile
- AJAX loading cho widget dashboard

## Cài đặt
Tính năng đã được tích hợp sẵn vào blog plugin. Không cần cài đặt thêm.

## Hỗ trợ
Liên hệ team phát triển nếu có vấn đề hoặc cần hỗ trợ thêm tính năng.

.author-stats-widget {
    padding: 1rem;
}

.author-stats-widget .icon-wrapper {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.author-stats-summary-cards .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.author-stats-summary-cards .card:hover {
    transform: translateY(-2px);
}

.author-stats-table .avatar {
    width: 32px;
    height: 32px;
}

.author-stats-table .avatar-initial {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
}

.author-stats-filter-form {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.author-stats-chart-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.author-stats-detailed .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

.author-stats-detailed .badge {
    font-size: 0.75rem;
}

.author-stats-no-data {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.author-stats-no-data i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .author-stats-filter-form .row > div {
        margin-bottom: 1rem;
    }
    
    .author-stats-summary-cards .col-md-3 {
        margin-bottom: 1rem;
    }
    
    .author-stats-table {
        font-size: 0.875rem;
    }
}

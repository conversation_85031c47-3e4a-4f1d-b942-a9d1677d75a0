.author-stats-widget {
    padding: 1rem;
}

.author-stats-widget .icon-wrapper {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Widget Content Styles */
.widget-content {
    min-height: 300px;
    padding: 1rem;
}

.widget-content .icon-wrapper {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.widget-content h6 {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.widget-content h4 {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0;
}

.widget-content .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.widget-content .btn {
    font-size: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 600;
}

.author-stats-summary-cards .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease-in-out;
}

.author-stats-summary-cards .card:hover {
    transform: translateY(-2px);
}

.author-stats-table .avatar {
    width: 32px;
    height: 32px;
}

.author-stats-table .avatar-initial {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
}

/* Fix for table column widths */
.author-stats-table th,
.author-stats-table td {
    white-space: nowrap;
    vertical-align: middle;
}

.author-stats-table .author-name-col {
    min-width: 200px;
}

.author-stats-table .email-col {
    min-width: 180px;
}

.author-stats-table .number-col {
    width: 100px;
    text-align: center;
}

.author-stats-table .action-col {
    width: 120px;
    text-align: center;
}

/* Fix for badges */
.author-stats-table .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    min-width: 50px;
    display: inline-block;
    color: #fff;
}

/* Fix for buttons */
.author-stats-table .btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.author-stats-table .btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    line-height: 1.2;
}

.author-stats-filter-form {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    margin-bottom: 2rem;
}

.author-stats-chart-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.author-stats-detailed .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

.author-stats-detailed .badge {
    font-size: 0.75rem;
    color: #fff;
}

.author-stats-no-data {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.author-stats-no-data i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Fix for detailed stats table */
.author-stats-detailed .table {
    margin-bottom: 0;
}

.author-stats-detailed .table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    padding: 0.75rem;
}

.author-stats-detailed .table td {
    padding: 0.75rem;
    vertical-align: middle;
}

.author-stats-detailed .post-title {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.author-stats-detailed .post-description {
    max-width: 250px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    color: #6c757d;
    font-size: 0.875rem;
}

.author-stats-detailed .btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    margin: 0 1px;
}

/* Chart container improvements */
.author-stats-chart-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 1rem;
}

.author-stats-chart-container canvas {
    max-height: 400px;
}

/* Mobile responsive improvements */
@media (max-width: 992px) {
    .author-stats-summary-cards .col-md-2 {
        margin-bottom: 1rem;
    }

    .author-stats-summary-cards .card .card-body {
        padding: 1rem 0.75rem;
    }

    .author-stats-summary-cards .card h3 {
        font-size: 1.5rem;
    }

    .author-stats-summary-cards .card h6 {
        font-size: 0.75rem;
    }
}

@media (max-width: 768px) {
    .author-stats-filter-form .row > div {
        margin-bottom: 1rem;
    }

    .author-stats-summary-cards .col-md-2 {
        margin-bottom: 1rem;
    }

    .author-stats-table {
        font-size: 0.875rem;
    }

    .author-stats-table .author-name-col,
    .author-stats-table .email-col {
        min-width: auto;
    }

    .author-stats-table .number-col {
        width: 80px;
    }

    .author-stats-table .action-col {
        width: 100px;
    }

    .author-stats-detailed .post-title {
        max-width: 200px;
    }

    .author-stats-detailed .post-description {
        max-width: 150px;
    }

    /* Stack widget stats vertically on mobile */
    .author-stats-widget .row .col-6 {
        margin-bottom: 0.75rem;
    }

    .author-stats-widget .icon-wrapper {
        width: 35px;
        height: 35px;
    }

    .author-stats-widget h4 {
        font-size: 1.25rem;
    }

    .author-stats-widget h6 {
        font-size: 0.75rem;
    }
}

$(document).ready(function() {
    'use strict';

    // Period select change handler
    $('#period-select').on('change', function() {
        const isCustom = $(this).val() === 'custom';
        $('#start-date-group').toggle(isCustom);
        $('#end-date-group').toggle(isCustom);
        
        if (!isCustom) {
            $('input[name="start_date"]').val('');
            $('input[name="end_date"]').val('');
        }
    });

    // Initialize date inputs
    const today = new Date().toISOString().split('T')[0];
    $('input[type="date"]').attr('max', today);

    // Form validation
    $('form').on('submit', function(e) {
        const period = $('#period-select').val();
        const startDate = $('input[name="start_date"]').val();
        const endDate = $('input[name="end_date"]').val();

        if (period === 'custom') {
            if (!startDate || !endDate) {
                e.preventDefault();
                alert('Vui lòng chọn cả ngày bắt đầu và ngày kết thúc');
                return false;
            }

            if (new Date(startDate) > new Date(endDate)) {
                e.preventDefault();
                alert('Ngày bắt đầu không thể lớn hơn ngày kết thúc');
                return false;
            }
        }
    });

    // Table row hover effect
    $('.author-stats-table tbody tr').hover(
        function() {
            $(this).addClass('table-active');
        },
        function() {
            $(this).removeClass('table-active');
        }
    );

    // Smooth scroll to detailed stats
    $('a[href*="#detailed-stats"]').on('click', function(e) {
        e.preventDefault();
        const target = $('#detailed-stats');
        if (target.length) {
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });

    // Auto-refresh widget data every 5 minutes
    if (typeof BDashboard !== 'undefined') {
        setInterval(function() {
            BDashboard.loadWidget($('#widget_author_stats').find('.widget-content'), $('#widget_author_stats').data('url'));
        }, 300000); // 5 minutes
    }

    // Export functionality
    $('.export-btn').on('click', function(e) {
        e.preventDefault();
        const url = $(this).attr('href');
        const params = new URLSearchParams(window.location.search);
        
        // Add current filter parameters to export URL
        const exportUrl = url + '?' + params.toString();
        window.open(exportUrl, '_blank');
    });

    // Tooltip initialization
    $('[data-bs-toggle="tooltip"]').tooltip();

    // Number formatting for large numbers
    $('.format-number').each(function() {
        const number = parseInt($(this).text().replace(/,/g, ''));
        if (number >= 1000000) {
            $(this).text((number / 1000000).toFixed(1) + 'M');
        } else if (number >= 1000) {
            $(this).text((number / 1000).toFixed(1) + 'K');
        }
    });
});

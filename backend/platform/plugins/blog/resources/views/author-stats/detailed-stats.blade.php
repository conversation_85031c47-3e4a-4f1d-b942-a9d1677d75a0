<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="ti ti-chart-line me-2"></i>
                    Chi tiết thống kê tác giả
                </h5>
            </div>
            <div class="card-body">
                <!-- Summary Stats -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <i class="ti ti-file-text fs-1 text-primary mb-2"></i>
                            <h4 class="mb-0">{{ number_format($totalPosts) }}</h4>
                            <small class="text-muted">Tổng bài viết</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <i class="ti ti-eye fs-1 text-success mb-2"></i>
                            <h4 class="mb-0">{{ number_format($totalViews) }}</h4>
                            <small class="text-muted">Tổng l<PERSON>t xem</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <i class="ti ti-trending-up fs-1 text-info mb-2"></i>
                            <h4 class="mb-0">{{ number_format($avgViews, 0) }}</h4>
                            <small class="text-muted">TB lượt xem/bài</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center p-3 border rounded">
                            <i class="ti ti-star fs-1 text-warning mb-2"></i>
                            <h4 class="mb-0">{{ $posts->isNotEmpty() ? number_format($posts->max(function($post) { return $post->postScore ? $post->postScore->views : 0; })) : 0 }}</h4>
                            <small class="text-muted">Lượt xem cao nhất</small>
                        </div>
                    </div>
                </div>

                <!-- Chart Section -->
                @if($viewsByMonth->isNotEmpty())
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="card-title">Biểu đồ lượt xem theo tháng</h6>
                                </div>
                                <div class="card-body">
                                    <canvas id="viewsChart" height="100"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Posts Table -->
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Tiêu đề bài viết</th>
                                <th class="text-center">Lượt xem</th>
                                <th class="text-center">Ngày tạo</th>
                                <th class="text-center">Trạng thái</th>
                                <th class="text-center">Hành động</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($posts as $post)
                                <tr>
                                    <td>
                                        <div>
                                            <strong>{{ Str::limit($post->name, 60) }}</strong>
                                            @if($post->is_featured)
                                                <span class="badge bg-warning ms-2">Nổi bật</span>
                                            @endif
                                        </div>
                                        @if($post->description)
                                            <small class="text-muted">{{ Str::limit($post->description, 80) }}</small>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-success">{{ number_format($post->postScore ? $post->postScore->views : 0) }}</span>
                                    </td>
                                    <td class="text-center">
                                        <small>{{ $post->created_at->format('d/m/Y H:i') }}</small>
                                    </td>
                                    <td class="text-center">
                                        @if($post->status === 'published')
                                            <span class="badge bg-success">Đã xuất bản</span>
                                        @elseif($post->status === 'draft')
                                            <span class="badge bg-secondary">Bản nháp</span>
                                        @else
                                            <span class="badge bg-warning">{{ ucfirst($post->status) }}</span>
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ route('posts.edit', $post->id) }}" 
                                               class="btn btn-outline-primary btn-sm">
                                                <i class="ti ti-edit"></i>
                                            </a>
                                            @if($post->status === 'published')
                                                <a href="{{ route('public.single', $post->slug) }}" 
                                                   target="_blank" 
                                                   class="btn btn-outline-info btn-sm">
                                                    <i class="ti ti-external-link"></i>
                                                </a>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <i class="ti ti-inbox fs-1 text-muted"></i>
                                        <p class="text-muted mt-2">Không có bài viết nào trong khoảng thời gian này</p>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

@if($viewsByMonth->isNotEmpty())
    @push('footer')
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
            const ctx = document.getElementById('viewsChart').getContext('2d');
            const chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: {!! json_encode($viewsByMonth->pluck('month')->map(function($month) {
                        return \Carbon\Carbon::createFromFormat('Y-m', $month)->format('m/Y');
                    })) !!},
                    datasets: [{
                        label: 'Lượt xem',
                        data: {!! json_encode($viewsByMonth->pluck('total_views')) !!},
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1,
                        fill: true
                    }, {
                        label: 'Số bài viết',
                        data: {!! json_encode($viewsByMonth->pluck('total_posts')) !!},
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        tension: 0.1,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    interaction: {
                        mode: 'index',
                        intersect: false,
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Lượt xem'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Số bài viết'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    },
                    plugins: {
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        title: {
                            display: true,
                            text: 'Thống kê lượt xem và số bài viết theo tháng'
                        }
                    }
                }
            });
        </script>
    @endpush
@endif

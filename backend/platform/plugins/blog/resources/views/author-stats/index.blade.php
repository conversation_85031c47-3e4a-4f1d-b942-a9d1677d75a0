@extends('core/base::layouts.master')

@push('header')
    <link rel="stylesheet" href="{{ asset('vendor/core/plugins/blog/css/author-stats.css') }}">
@endpush

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        <i class="ti ti-chart-bar me-2"></i>
                        Thống kê lượt xem theo tác giả
                    </h4>
                </div>
                <div class="card-body">
                    <!-- Filter Form -->
                    <form method="GET" action="{{ route('blog.author-stats.index') }}" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Tác giả</label>
                                <select name="author_id" class="form-select">
                                    <option value="">-- T<PERSON><PERSON> cả tác giả --</option>
                                    @foreach($authors as $author)
                                        <option value="{{ $author->id }}" 
                                                {{ $authorId == $author->id ? 'selected' : '' }}>
                                            {{ $author->name }} ({{ $author->posts_count }} bài viết)
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            
                            <div class="col-md-2">
                                <label class="form-label">Khoảng thời gian</label>
                                <select name="period" class="form-select" id="period-select">
                                    <option value="all" {{ $period == 'all' ? 'selected' : '' }}>Tất cả</option>
                                    <option value="today" {{ $period == 'today' ? 'selected' : '' }}>Hôm nay</option>
                                    <option value="week" {{ $period == 'week' ? 'selected' : '' }}>Tuần này</option>
                                    <option value="month" {{ $period == 'month' ? 'selected' : '' }}>Tháng này</option>
                                    <option value="year" {{ $period == 'year' ? 'selected' : '' }}>Năm này</option>
                                    <option value="custom" {{ $period == 'custom' ? 'selected' : '' }}>Tùy chỉnh</option>
                                </select>
                            </div>
                            
                            <div class="col-md-2" id="start-date-group" style="{{ $period == 'custom' ? '' : 'display: none;' }}">
                                <label class="form-label">Từ ngày</label>
                                <input type="date" name="start_date" class="form-control" value="{{ $startDate }}">
                            </div>
                            
                            <div class="col-md-2" id="end-date-group" style="{{ $period == 'custom' ? '' : 'display: none;' }}">
                                <label class="form-label">Đến ngày</label>
                                <input type="date" name="end_date" class="form-control" value="{{ $endDate }}">
                            </div>
                            
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ti ti-search me-1"></i>
                                        Lọc
                                    </button>
                                    <a href="{{ route('blog.author-stats.index') }}" class="btn btn-outline-secondary">
                                        <i class="ti ti-refresh me-1"></i>
                                        Reset
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Summary Cards -->
                    @if($authorStats->isNotEmpty())
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-users fs-1 me-3"></i>
                                            <div>
                                                <h6 class="mb-0">Tổng tác giả</h6>
                                                <h3 class="mb-0">{{ $authorStats->count() }}</h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-file-text fs-1 me-3"></i>
                                            <div>
                                                <h6 class="mb-0">Tổng bài viết</h6>
                                                <h3 class="mb-0">{{ number_format($authorStats->sum('total_posts')) }}</h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-eye fs-1 me-3"></i>
                                            <div>
                                                <h6 class="mb-0">Tổng lượt xem</h6>
                                                <h3 class="mb-0">{{ number_format($authorStats->sum('total_views')) }}</h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <i class="ti ti-trending-up fs-1 me-3"></i>
                                            <div>
                                                <h6 class="mb-0">TB lượt xem/bài</h6>
                                                <h3 class="mb-0">{{ number_format($authorStats->avg('avg_views'), 0) }}</h3>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Author Stats Table -->
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Tác giả</th>
                                    <th>Email</th>
                                    <th class="text-center">Số bài viết</th>
                                    <th class="text-center">Tổng lượt xem</th>
                                    <th class="text-center">TB lượt xem</th>
                                    <th class="text-center">Lượt xem cao nhất</th>
                                    <th class="text-center">Hành động</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($authorStats as $stat)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar avatar-sm me-2">
                                                    <span class="avatar-initial bg-primary rounded-circle">
                                                        {{ strtoupper(substr($stat->name, 0, 1)) }}
                                                    </span>
                                                </div>
                                                <strong>{{ $stat->name }}</strong>
                                            </div>
                                        </td>
                                        <td>{{ $stat->email }}</td>
                                        <td class="text-center">
                                            <span class="badge bg-secondary">{{ number_format($stat->total_posts) }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-success">{{ number_format($stat->total_views) }}</span>
                                        </td>
                                        <td class="text-center">{{ number_format($stat->avg_views, 0) }}</td>
                                        <td class="text-center">{{ number_format($stat->max_views) }}</td>
                                        <td class="text-center">
                                            <a href="{{ route('blog.author-stats.index', ['author_id' => $stat->id] + request()->except('author_id')) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="ti ti-eye me-1"></i>
                                                Chi tiết
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <i class="ti ti-inbox fs-1 text-muted"></i>
                                            <p class="text-muted mt-2">Không có dữ liệu thống kê</p>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Stats for Selected Author -->
    @if($selectedAuthorDetails)
        @include('plugins/blog::author-stats.detailed-stats', $selectedAuthorDetails)
    @endif
@endsection

@push('footer')
    <script src="{{ asset('vendor/core/plugins/blog/js/author-stats.js') }}"></script>
@endpush

<?php

use Illuminate\Support\Facades\Route;

Route::group([
    'middleware' => ['api', 'cslant-block-domain'],
    'prefix' => 'api/v1',
    'namespace' => 'Botble\Blog\Http\Controllers\API',
], function (): void {
    Route::get('search', 'PostController@getSearch');
    Route::get('posts', 'PostController@index');
    Route::get('categories', 'CategoryController@index');
    Route::get('tags', 'TagController@index');

    Route::get('posts/filters', 'PostController@getFilters');
    Route::get('posts/{slug}', 'PostController@findBySlug');
    Route::get('categories/filters', 'CategoryController@getFilters');
    Route::get('categories/{slug}', 'CategoryController@findBySlug');
});

<?php

namespace Botble\Blog\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Blog\Models\Post;
use Botble\ACL\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AuthorStatsController extends BaseController
{
    public function index(Request $request)
    {
        $this->pageTitle('Thống kê tác giả');

        // Get filter parameters
        $authorId = $request->get('author_id');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $period = $request->get('period', 'all'); // all, today, week, month, year

        // Set date range based on period
        if ($period !== 'all' && $period !== 'custom') {
            switch ($period) {
                case 'today':
                    $startDate = Carbon::today()->format('Y-m-d');
                    $endDate = Carbon::today()->format('Y-m-d');
                    break;
                case 'week':
                    $startDate = Carbon::now()->startOfWeek()->format('Y-m-d');
                    $endDate = Carbon::now()->endOfWeek()->format('Y-m-d');
                    break;
                case 'month':
                    $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
                    $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
                    break;
                case 'year':
                    $startDate = Carbon::now()->startOfYear()->format('Y-m-d');
                    $endDate = Carbon::now()->endOfYear()->format('Y-m-d');
                    break;
            }
        }

        // Get all authors
        $authors = User::query()
            ->whereHas('posts')
            ->withCount('posts')
            ->orderBy('name')
            ->get();

        // Build query for author stats
        $query = DB::table('posts')
            ->select([
                'users.id',
                'users.name',
                'users.email',
                DB::raw('COUNT(posts.id) as total_posts'),
                DB::raw('SUM(posts.views) as total_views'),
                DB::raw('AVG(posts.views) as avg_views'),
                DB::raw('MAX(posts.views) as max_views'),
                DB::raw('MIN(posts.views) as min_views')
            ])
            ->join('users', 'posts.author_id', '=', 'users.id')
            ->where('posts.author_type', 'Botble\ACL\Models\User')
            ->groupBy('users.id', 'users.name', 'users.email');

        // Apply filters
        if ($authorId) {
            $query->where('users.id', $authorId);
        }

        if ($startDate && $endDate) {
            $query->whereBetween('posts.created_at', [
                Carbon::parse($startDate)->startOfDay(),
                Carbon::parse($endDate)->endOfDay()
            ]);
        }

        $authorStats = $query->orderByDesc('total_views')->get();

        // Get detailed stats for selected author
        $selectedAuthorDetails = null;
        if ($authorId) {
            $selectedAuthorDetails = $this->getAuthorDetailedStats($authorId, $startDate, $endDate);
        }

        return view('plugins/blog::author-stats.index', compact(
            'authors',
            'authorStats',
            'selectedAuthorDetails',
            'authorId',
            'startDate',
            'endDate',
            'period'
        ));
    }

    private function getAuthorDetailedStats($authorId, $startDate = null, $endDate = null)
    {
        $query = Post::query()
            ->where('author_id', $authorId)
            ->where('author_type', 'Botble\ACL\Models\User');

        if ($startDate && $endDate) {
            $query->whereBetween('created_at', [
                Carbon::parse($startDate)->startOfDay(),
                Carbon::parse($endDate)->endOfDay()
            ]);
        }

        $posts = $query->orderByDesc('views')->get();

        $totalViews = $posts->sum('views');
        $totalPosts = $posts->count();
        $avgViews = $totalPosts > 0 ? round($totalViews / $totalPosts, 2) : 0;

        // Get views by month for chart
        $viewsByMonth = Post::query()
            ->select([
                DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                DB::raw('SUM(views) as total_views'),
                DB::raw('COUNT(*) as total_posts')
            ])
            ->where('author_id', $authorId)
            ->where('author_type', 'Botble\ACL\Models\User')
            ->when($startDate && $endDate, function ($q) use ($startDate, $endDate) {
                return $q->whereBetween('created_at', [
                    Carbon::parse($startDate)->startOfDay(),
                    Carbon::parse($endDate)->endOfDay()
                ]);
            })
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        return [
            'posts' => $posts,
            'totalViews' => $totalViews,
            'totalPosts' => $totalPosts,
            'avgViews' => $avgViews,
            'viewsByMonth' => $viewsByMonth
        ];
    }

    public function export(Request $request): BaseHttpResponse
    {
        // Implementation for exporting stats
        return $this->httpResponse()->setMessage('Export functionality will be implemented');
    }

    public function getWidgetAuthorStats(Request $request)
    {
        $totalAuthors = DB::table('posts')
            ->where('author_type', 'Botble\ACL\Models\User')
            ->distinct('author_id')
            ->count('author_id');

        $totalViews = Post::query()
            ->where('author_type', 'Botble\ACL\Models\User')
            ->sum('views');

        $topAuthor = DB::table('posts')
            ->select('author_id', DB::raw('SUM(views) as total_views'))
            ->join('users', 'posts.author_id', '=', 'users.id')
            ->where('posts.author_type', 'Botble\ACL\Models\User')
            ->groupBy('author_id')
            ->orderByDesc('total_views')
            ->first();

        $topAuthorName = $topAuthor ? DB::table('users')->where('id', $topAuthor->author_id)->value('name') : 'N/A';

        return view('plugins/blog::widgets.author-stats', [
            'totalAuthors' => $totalAuthors,
            'totalViews' => number_format($totalViews),
            'topAuthorName' => $topAuthorName,
            'topAuthorViews' => $topAuthor ? number_format($topAuthor->total_views) : '0',
        ]);
    }
}

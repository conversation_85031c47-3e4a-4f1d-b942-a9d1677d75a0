<?php

namespace Bo<PERSON>ble\Blog\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\Blog\Models\Post;
use CSlant\Blog\PostCustom\Models\Post as PostCustom;
use Botble\ACL\Models\User;
use CSlant\Blog\PostCustom\Models\PostScore;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class AuthorStatsController extends BaseController
{
    public function index(Request $request)
    {
        $this->pageTitle('Thống kê tác giả');

        // Get filter parameters
        $authorId = $request->get('author_id');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $period = $request->get('period', 'all'); // all, today, week, month, year

        // Set date range based on period
        if ($period !== 'all' && $period !== 'custom') {
            switch ($period) {
                case 'today':
                    $startDate = Carbon::today()->format('Y-m-d');
                    $endDate = Carbon::today()->format('Y-m-d');
                    break;
                case 'week':
                    $startDate = Carbon::now()->startOfWeek()->format('Y-m-d');
                    $endDate = Carbon::now()->endOfWeek()->format('Y-m-d');
                    break;
                case 'month':
                    $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
                    $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
                    break;
                case 'year':
                    $startDate = Carbon::now()->startOfYear()->format('Y-m-d');
                    $endDate = Carbon::now()->endOfYear()->format('Y-m-d');
                    break;
            }
        }



        // Get all authors who have posts
        $authors = User::query()
            ->select(['id', 'first_name', 'last_name', 'email'])
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('posts')
                    ->whereColumn('posts.author_id', 'users.id')
                    ->where('posts.author_type', 'Botble\ACL\Models\User');
            })
            ->orderBy('first_name')
            ->orderBy('last_name')
            ->get();

        // Build query for author stats - simplified approach
        $authorStats = collect();

        // Always use posts table first for reliability
        $query = DB::table('posts')
            ->select([
                'users.id',
                'users.first_name',
                'users.last_name',
                'users.email',
                DB::raw('COUNT(posts.id) as total_posts'),
                DB::raw('COALESCE(SUM(posts.views), 0) as total_views'),
                DB::raw('0 as total_comments'),
                DB::raw('0 as total_score'),
                DB::raw('COALESCE(AVG(posts.views), 0) as avg_views'),
                DB::raw('COALESCE(MAX(posts.views), 0) as max_views'),
                DB::raw('COALESCE(MIN(posts.views), 0) as min_views')
            ])
            ->join('users', 'posts.author_id', '=', 'users.id')
            ->where('posts.author_type', 'Botble\ACL\Models\User')
            ->groupBy('users.id', 'users.first_name', 'users.last_name', 'users.email');

        // Apply filters
        if ($authorId) {
            $query->where('users.id', $authorId);
        }

        if ($startDate && $endDate) {
            $startDateTime = $startDate . ' 00:00:00';
            $endDateTime = $endDate . ' 23:59:59';

            $query->whereBetween('posts.created_at', [$startDateTime, $endDateTime]);
        }

        try {
            $authorStats = $query->orderByDesc('total_views')->get();

            // If no results with date filter, try without date filter
            if ($authorStats->isEmpty() && $startDate && $endDate) {
                $queryWithoutDates = DB::table('posts')
                    ->select([
                        'users.id',
                        'users.first_name',
                        'users.last_name',
                        'users.email',
                        DB::raw('COUNT(posts.id) as total_posts'),
                        DB::raw('COALESCE(SUM(posts.views), 0) as total_views'),
                        DB::raw('0 as total_comments'),
                        DB::raw('0 as total_score'),
                        DB::raw('COALESCE(AVG(posts.views), 0) as avg_views')
                    ])
                    ->join('users', 'posts.author_id', '=', 'users.id')
                    ->where('posts.author_type', 'Botble\ACL\Models\User')
                    ->groupBy('users.id', 'users.first_name', 'users.last_name', 'users.email');

                if ($authorId) {
                    $queryWithoutDates->where('users.id', $authorId);
                }

                $allTimeStats = $queryWithoutDates->orderByDesc('total_views')->get();

                // Show message that no data in date range but show all time data
                if ($allTimeStats->isNotEmpty()) {
                    session()->flash('warning', 'Không có dữ liệu trong khoảng thời gian đã chọn. Hiển thị dữ liệu tổng thể.');
                    $authorStats = $allTimeStats;
                }
            }

        } catch (\Exception $e) {
            $authorStats = collect();
        }

        // Get detailed stats for selected author
        $selectedAuthorDetails = null;
        if ($authorId) {
            $selectedAuthorDetails = $this->getAuthorDetailedStats($authorId, $startDate, $endDate);
        }

        return view('plugins/blog::author-stats.index', compact(
            'authors',
            'authorStats',
            'selectedAuthorDetails',
            'authorId',
            'startDate',
            'endDate',
            'period'
        ));
    }

    private function getAuthorDetailedStats($authorId, $startDate = null, $endDate = null)
    {
        try {
            // Try to use PostCustom model first
            $query = PostCustom::query()
                ->where('author_id', $authorId)
                ->where('author_type', 'Botble\ACL\Models\User');

            if ($startDate && $endDate) {
                $query->whereBetween('created_at', [
                    Carbon::parse($startDate)->startOfDay(),
                    Carbon::parse($endDate)->endOfDay()
                ]);
            }

            $posts = $query->with('postScore')->get()
                ->sortByDesc(function($post) {
                    return $post->postScore ? $post->postScore->views : 0;
                });

        } catch (\Exception $e) {
            // Fallback to regular Post model
            $query = Post::query()
                ->where('author_id', $authorId)
                ->where('author_type', 'Botble\ACL\Models\User');

            if ($startDate && $endDate) {
                $query->whereBetween('created_at', [
                    Carbon::parse($startDate)->startOfDay(),
                    Carbon::parse($endDate)->endOfDay()
                ]);
            }

            $posts = $query->orderByDesc('views')->get();
        }

        // Calculate stats
        $totalPosts = $posts->count();
        $totalViews = 0;
        $totalComments = 0;
        $totalScore = 0;

        try {
            // Try to calculate from PostScore
            $totalViews = $posts->sum(function($post) {
                return $post->postScore ? $post->postScore->views : 0;
            });
            $totalComments = $posts->sum(function($post) {
                return $post->postScore ? $post->postScore->comments_count : 0;
            });
            $totalScore = $posts->sum(function($post) {
                return $post->postScore ? $post->postScore->score : 0;
            });
        } catch (\Exception $e) {
            // Fallback to posts views
            $totalViews = $posts->sum('views');
            $totalComments = 0;
            $totalScore = 0;
        }

        $avgViews = $totalPosts > 0 ? round($totalViews / $totalPosts, 2) : 0;

        // Get views by month for chart
        $viewsByMonth = collect();
        try {
            $viewsByMonth = DB::table('post_scores')
                ->select([
                    DB::raw('DATE_FORMAT(posts.created_at, "%Y-%m") as month'),
                    DB::raw('SUM(post_scores.views) as total_views'),
                    DB::raw('COUNT(*) as total_posts')
                ])
                ->join('posts', 'post_scores.id', '=', 'posts.id')
                ->where('posts.author_id', $authorId)
                ->where('posts.author_type', 'Botble\ACL\Models\User')
                ->when($startDate && $endDate, function ($q) use ($startDate, $endDate) {
                    return $q->whereBetween('posts.created_at', [
                        Carbon::parse($startDate)->startOfDay(),
                        Carbon::parse($endDate)->endOfDay()
                    ]);
                })
                ->groupBy('month')
                ->orderBy('month')
                ->get();
        } catch (\Exception $e) {
            // Fallback to posts table
            $viewsByMonth = DB::table('posts')
                ->select([
                    DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
                    DB::raw('SUM(views) as total_views'),
                    DB::raw('COUNT(*) as total_posts')
                ])
                ->where('author_id', $authorId)
                ->where('author_type', 'Botble\ACL\Models\User')
                ->when($startDate && $endDate, function ($q) use ($startDate, $endDate) {
                    return $q->whereBetween('created_at', [
                        Carbon::parse($startDate)->startOfDay(),
                        Carbon::parse($endDate)->endOfDay()
                    ]);
                })
                ->groupBy('month')
                ->orderBy('month')
                ->get();
        }

        return [
            'posts' => $posts,
            'totalViews' => $totalViews,
            'totalComments' => $totalComments,
            'totalScore' => $totalScore,
            'totalPosts' => $totalPosts,
            'avgViews' => $avgViews,
            'viewsByMonth' => $viewsByMonth
        ];
    }

    public function export(Request $request): BaseHttpResponse
    {
        // Implementation for exporting stats
        return $this->httpResponse()->setMessage('Export functionality will be implemented');
    }

    public function getWidgetAuthorStats(Request $request)
    {
        try {
            $totalAuthors = DB::table('posts')
                ->where('author_type', 'Botble\ACL\Models\User')
                ->distinct('author_id')
                ->count('author_id');

            // Try to get stats from post_scores, fallback to posts if needed
            $totalViews = 0;
            $totalComments = 0;
            $totalScore = 0;

            try {
                $stats = DB::table('post_scores')
                    ->join('posts', 'post_scores.id', '=', 'posts.id')
                    ->where('posts.author_type', 'Botble\ACL\Models\User')
                    ->selectRaw('
                        SUM(post_scores.views) as total_views,
                        SUM(post_scores.comments_count) as total_comments,
                        SUM(post_scores.score) as total_score
                    ')
                    ->first();

                $totalViews = $stats->total_views ?? 0;
                $totalComments = $stats->total_comments ?? 0;
                $totalScore = $stats->total_score ?? 0;
            } catch (\Exception $e) {
                // Fallback to posts table if post_scores doesn't exist
                $totalViews = DB::table('posts')
                    ->where('author_type', 'Botble\ACL\Models\User')
                    ->sum('views');
                $totalComments = 0;
                $totalScore = 0;
            }

            $topAuthor = null;
            $topAuthorName = 'N/A';

            try {
                $topAuthor = DB::table('post_scores')
                    ->select('posts.author_id', DB::raw('SUM(post_scores.views) as total_views'))
                    ->join('posts', 'post_scores.id', '=', 'posts.id')
                    ->where('posts.author_type', 'Botble\ACL\Models\User')
                    ->groupBy('posts.author_id')
                    ->orderByDesc('total_views')
                    ->first();
            } catch (\Exception $e) {
                // Fallback to posts table
                $topAuthor = DB::table('posts')
                    ->select('author_id', DB::raw('SUM(views) as total_views'))
                    ->where('author_type', 'Botble\ACL\Models\User')
                    ->groupBy('author_id')
                    ->orderByDesc('total_views')
                    ->first();
            }

            if ($topAuthor) {
                $user = DB::table('users')->where('id', $topAuthor->author_id)->first();
                $topAuthorName = $user ? trim(($user->first_name ?? '') . ' ' . ($user->last_name ?? '')) : 'N/A';
                if (empty(trim($topAuthorName))) {
                    $topAuthorName = $user->email ?? 'N/A';
                }
            }

            return view('plugins/blog::widgets.author-stats', [
                'totalAuthors' => $totalAuthors,
                'totalViews' => number_format($totalViews ?: 0),
                'totalComments' => number_format($totalComments ?: 0),
                'totalScore' => number_format($totalScore ?: 0, 1),
                'topAuthorName' => $topAuthorName,
                'topAuthorViews' => $topAuthor ? number_format($topAuthor->total_views ?: 0) : '0',
            ]);

        } catch (\Exception $e) {
            return view('plugins/blog::widgets.author-stats', [
                'totalAuthors' => 0,
                'totalViews' => '0',
                'totalComments' => '0',
                'totalScore' => '0',
                'topAuthorName' => 'N/A',
                'topAuthorViews' => '0',
                'error' => $e->getMessage()
            ]);
        }
    }
}

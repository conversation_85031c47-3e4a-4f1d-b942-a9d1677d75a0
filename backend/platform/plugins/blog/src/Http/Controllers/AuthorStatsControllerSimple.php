<?php

namespace Botble\Blog\Http\Controllers;

use Botble\Base\Http\Controllers\BaseController;
use Botble\ACL\Models\User;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AuthorStatsControllerSimple extends BaseController
{
    public function index(Request $request)
    {
        $this->pageTitle('Thống kê tác giả');

        // Get filter parameters
        $authorId = $request->get('author_id');
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $period = $request->get('period', 'all');

        // Set date range based on period
        if ($period !== 'all' && $period !== 'custom') {
            switch ($period) {
                case 'today':
                    $startDate = Carbon::today()->format('Y-m-d');
                    $endDate = Carbon::today()->format('Y-m-d');
                    break;
                case 'week':
                    $startDate = Carbon::now()->startOfWeek()->format('Y-m-d');
                    $endDate = Carbon::now()->endOfWeek()->format('Y-m-d');
                    break;
                case 'month':
                    $startDate = Carbon::now()->startOfMonth()->format('Y-m-d');
                    $endDate = Carbon::now()->endOfMonth()->format('Y-m-d');
                    break;
                case 'year':
                    $startDate = Carbon::now()->startOfYear()->format('Y-m-d');
                    $endDate = Carbon::now()->endOfYear()->format('Y-m-d');
                    break;
            }
        }

        // Get all users (simplified)
        $authors = User::select(['id', 'first_name', 'last_name', 'email'])
            ->orderBy('first_name')
            ->get();

        // Get basic author stats
        $authorStats = collect();
        
        try {
            // Very simple query
            $sql = "
                SELECT 
                    u.id,
                    u.first_name,
                    u.last_name,
                    u.email,
                    COUNT(p.id) as total_posts,
                    SUM(COALESCE(p.views, 0)) as total_views,
                    AVG(COALESCE(p.views, 0)) as avg_views,
                    MAX(COALESCE(p.views, 0)) as max_views,
                    0 as total_comments,
                    0 as total_score
                FROM users u
                LEFT JOIN posts p ON u.id = p.author_id AND p.author_type = 'Botble\\\\ACL\\\\Models\\\\User'
            ";
            
            $conditions = [];
            $params = [];
            
            if ($authorId) {
                $conditions[] = "u.id = ?";
                $params[] = $authorId;
            }
            
            if ($startDate && $endDate) {
                $conditions[] = "p.created_at BETWEEN ? AND ?";
                $params[] = $startDate . ' 00:00:00';
                $params[] = $endDate . ' 23:59:59';
            }
            
            if (!empty($conditions)) {
                $sql .= " WHERE " . implode(' AND ', $conditions);
            }
            
            $sql .= " GROUP BY u.id, u.first_name, u.last_name, u.email";
            $sql .= " HAVING total_posts > 0";
            $sql .= " ORDER BY total_views DESC";
            
            $results = DB::select($sql, $params);
            $authorStats = collect($results);
            
        } catch (\Exception $e) {
            \Log::error('Simple Author Stats Query Failed: ' . $e->getMessage());
            $authorStats = collect();
        }

        // Get detailed stats for selected author
        $selectedAuthorDetails = null;
        if ($authorId && $authorStats->isNotEmpty()) {
            $selectedAuthorDetails = $this->getSimpleAuthorDetails($authorId, $startDate, $endDate);
        }

        return view('plugins/blog::author-stats.index', compact(
            'authors',
            'authorStats',
            'selectedAuthorDetails',
            'authorId',
            'startDate',
            'endDate',
            'period'
        ));
    }

    private function getSimpleAuthorDetails($authorId, $startDate = null, $endDate = null)
    {
        try {
            $sql = "
                SELECT *
                FROM posts 
                WHERE author_id = ? 
                AND author_type = 'Botble\\\\ACL\\\\Models\\\\User'
            ";
            
            $params = [$authorId];
            
            if ($startDate && $endDate) {
                $sql .= " AND created_at BETWEEN ? AND ?";
                $params[] = $startDate . ' 00:00:00';
                $params[] = $endDate . ' 23:59:59';
            }
            
            $sql .= " ORDER BY views DESC";
            
            $posts = collect(DB::select($sql, $params));
            
            $totalPosts = $posts->count();
            $totalViews = $posts->sum('views');
            $avgViews = $totalPosts > 0 ? round($totalViews / $totalPosts, 2) : 0;
            
            return [
                'posts' => $posts,
                'totalViews' => $totalViews,
                'totalComments' => 0,
                'totalScore' => 0,
                'totalPosts' => $totalPosts,
                'avgViews' => $avgViews,
                'viewsByMonth' => collect()
            ];
            
        } catch (\Exception $e) {
            \Log::error('Simple Author Details Failed: ' . $e->getMessage());
            return [
                'posts' => collect(),
                'totalViews' => 0,
                'totalComments' => 0,
                'totalScore' => 0,
                'totalPosts' => 0,
                'avgViews' => 0,
                'viewsByMonth' => collect()
            ];
        }
    }

    public function getWidgetAuthorStats(Request $request)
    {
        try {
            $totalAuthors = DB::select("
                SELECT COUNT(DISTINCT author_id) as count 
                FROM posts 
                WHERE author_type = 'Botble\\\\ACL\\\\Models\\\\User'
            ")[0]->count ?? 0;

            $totalViews = DB::select("
                SELECT SUM(COALESCE(views, 0)) as total 
                FROM posts 
                WHERE author_type = 'Botble\\\\ACL\\\\Models\\\\User'
            ")[0]->total ?? 0;

            return view('plugins/blog::widgets.author-stats', [
                'totalAuthors' => $totalAuthors,
                'totalViews' => number_format($totalViews),
                'totalComments' => '0',
                'totalScore' => '0',
                'topAuthorName' => 'N/A',
                'topAuthorViews' => '0',
            ]);
            
        } catch (\Exception $e) {
            return view('plugins/blog::widgets.author-stats', [
                'totalAuthors' => 0,
                'totalViews' => '0',
                'totalComments' => '0',
                'totalScore' => '0',
                'topAuthorName' => 'N/A',
                'topAuthorViews' => '0',
                'error' => $e->getMessage()
            ]);
        }
    }
}

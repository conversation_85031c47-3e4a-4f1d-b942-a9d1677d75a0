<?php

namespace Bo<PERSON>ble\Blog\Widgets;

use Botble\Base\Widgets\Card;
use Botble\Blog\Models\Post;
use Illuminate\Support\Facades\DB;

class AuthorStatsWidget extends Card
{
    public function getLabel(): string
    {
        return 'Thống kê tác giả';
    }

    public function getTitle(): string
    {
        return 'Thống kê tác giả';
    }

    public function getContent(): ?string
    {
        return null; // Content will be loaded via AJAX from the route
    }

    public function getRoute(): ?string
    {
        return route('blog.author-stats.index');
    }

    public function getColumns(): int
    {
        return 6;
    }
}

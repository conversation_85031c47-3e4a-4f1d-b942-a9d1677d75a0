(()=>{"use strict";$((function(){window.botbleCookieConsent=function(){var e=$("div[data-site-cookie-name]").data("site-cookie-name"),o=$("div[data-site-cookie-domain]").data("site-cookie-domain"),i=$("div[data-site-cookie-lifetime]").data("site-cookie-lifetime"),t=$("div[data-site-session-secure]").data("site-session-secure"),n=$(".js-cookie-consent"),c=$(".cookie-consent__categories"),s=$(".js-cookie-consent-customize");function a(){var n,c,s,a,d={};$(".js-cookie-category:checked").each((function(){d[$(this).val()]=!0})),n=e,c=JSON.stringify(d),s=i,(a=new Date).setTime(a.getTime()+24*s*60*60*1e3),document.cookie=n+"="+c+";expires="+a.toUTCString()+";domain="+o+";path=/"+t,k()}function d(){a(),c.slideUp(),s.removeClass("active")}function u(){r(e)&&(document.cookie=e+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; domain="+o+"; path=/"+t),"undefined"!=typeof gtag&&gtag("consent","update",{ad_storage:"denied",analytics_storage:"denied"}),k()}function r(e){var o=function(e){var o="; ".concat(document.cookie).split("; ".concat(e,"="));if(2===o.length)return o.pop().split(";").shift();return null}(e);return null!=o}function k(){n.hide()}return n.addClass("cookie-consent--visible"),c.hide(),r(e)&&k(),$(document).on("click",".js-cookie-consent-agree",(function(){a()})),$(document).on("click",".js-cookie-consent-reject",(function(){u()})),$(document).on("click",".js-cookie-consent-customize",(function(){c.slideToggle(),s.toggleClass("active")})),$(document).on("click",".js-cookie-consent-save",(function(){d()})),{consentWithCookies:a,rejectAllCookies:u,hideCookieDialog:k,savePreferences:d}}()}))})();
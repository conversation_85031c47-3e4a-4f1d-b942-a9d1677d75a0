.cookie-consent {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 8px;
    z-index: 99999;
    transition: transform .3s ease;
    transform: translateY(100%);

    &.cookie-consent--visible {
        transform: translateY(0);
    }

    &.cookie-consent-full-width {
        .cookie-consent-body {
            margin: 0 auto;
        }
    }

    &.cookie-consent-minimal {
        padding: 0;
        right: unset;
        border-radius: 5px;
        bottom: 1em;
        flex-direction: column;
        left: 1em;

        .cookie-consent-body {
            margin: 0 16px 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            max-width: 400px !important;
        }

        .cookie-consent__inner {
            flex-direction: column;
            align-items: stretch;
            gap: 12px;
            padding: 4px;
        }

        .cookie-consent__message {
            font-size: 13px;
            line-height: 1.5;
            padding: 12px 12px 0;
        }

        .cookie-consent__actions {
            margin: 0;
            padding: 8px 12px 12px;
            justify-content: flex-end;
            gap: 8px;

            button {
                min-width: auto;
                padding: 6px 12px;
                font-size: 12px;
            }
        }

        .cookie-consent__categories {
            padding: 12px;
            margin-top: 0;

            .cookie-category {
                padding: 8px;
                margin-bottom: 8px;

                &:last-child {
                    margin-bottom: 0;
                }

                &__description {
                    font-size: 12px;
                }
            }

            .cookie-consent__save {
                padding: 8px 0 0;
                margin-top: 8px;

                .cookie-consent__save-button {
                    font-size: 12px;
                    padding: 6px 12px;
                }
            }
        }
    }

    .cookie-consent-body {
        padding: 8px 15px;
        border-radius: 4px;
    }

    .cookie-consent__inner {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .cookie-consent__message {
        margin: 0;
        line-height: 1.4;
        font-size: 14px;
        flex: 1;
        min-width: 200px;

        a {
            color: inherit;
            text-decoration: underline;

            &:hover {
                text-decoration: none;
            }
        }
    }

    .cookie-consent__categories {
        display: none;
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid rgba(255, 255, 255, 0.1);

        .cookie-category {
            margin-bottom: 1rem;
            padding: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 4px;

            &__label {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                margin-bottom: 0.5rem;
                cursor: pointer;

                input[type="checkbox"] {
                    margin: 0;
                    padding: 0;
                    border: none;
                    border-radius: 0;
                    box-shadow: none;
                    font-size: initial;
                    height: initial;
                    width: auto;

                    &:disabled {
                        opacity: 0.5;
                        cursor: not-allowed;
                    }
                }
            }

            &__name {
                font-weight: bold;
            }

            &__description {
                margin: 0;
                font-size: 0.9em;
                opacity: 0.8;
            }
        }

        .cookie-consent__save {
            margin-top: 1rem;
            padding: .75rem;

            [dir="rtl"] & {
                text-align: left;
            }

            .cookie-consent__save-button {
                padding: 6px;
                border-radius: 4px;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 13px;
                min-width: 100px;
                text-align: center;
                font-weight: bold;

                &:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }

                &:active {
                    transform: translateY(0);
                }
            }
        }
    }

    .cookie-consent__actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        justify-content: flex-end;
        margin-left: auto;

        [dir="rtl"] & {
            margin-left: 0;
            margin-right: auto;
        }

        button {
            padding: 6px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 13px;
            min-width: 100px;
            text-align: center;

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            &:active {
                transform: translateY(0);
            }
        }

        .cookie-consent__reject {
            font-weight: 500;
            opacity: 0.95;

            &:hover {
                opacity: 1;
            }
        }

        .cookie-consent__customize {
            font-weight: 500;
            opacity: 0.95;

            &:hover {
                opacity: 1;
            }

            &.active {
                opacity: 0.8;
                transform: translateY(0);
                box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
            }
        }

        .cookie-consent__agree {
            font-weight: bold;
            position: relative;

            &:before {
                content: '';
                position: absolute;
                inset: -2px;
                border-radius: 6px;
                background: rgba(255, 255, 255, 0.1);
                z-index: -1;
            }
        }
    }

    @media (max-width: 767px) {
        .cookie-consent__inner {
            flex-direction: column;
            align-items: stretch;
            gap: 0.75rem;

            [dir="rtl"] & {
                flex-direction: column;
            }
        }

        .cookie-consent__actions {
            justify-content: stretch;
            margin-left: 0;

            [dir="rtl"] & {
                margin-right: 0;
            }

            button {
                flex: 1;
            }
        }
    }
}

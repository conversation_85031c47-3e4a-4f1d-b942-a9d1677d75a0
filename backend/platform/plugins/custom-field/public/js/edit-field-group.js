(()=>{"use strict";var e={};function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){for(var a=0;a<t.length;a++){var n=t[a];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,i(n.key),n)}}function l(e,t,a){return t&&n(e.prototype,t),a&&n(e,a),Object.defineProperty(e,"prototype",{writable:!1}),e}function i(e){var a=function(e,a){if("object"!=t(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var l=n.call(e,a||"default");if("object"!=t(l))return l;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===a?String:Number)(e)}(e,"string");return"symbol"==t(a)?a:a+""}e.d=(t,a)=>{for(var n in a)e.o(a,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:a[n]})},e.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);var o=function(){return l((function e(){a(this,e)}),null,[{key:"arrayGet",value:function(e,t){var a,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;try{a=e[t]}catch(e){return n}return null==a&&(a=n),a}},{key:"stringToSlug",value:function(e,t){return t=t||"-",e.toString().toLowerCase().replace(/á|à|ả|ạ|ã|ă|ắ|ằ|ẳ|ẵ|ặ|â|ấ|ầ|ẩ|ẫ|ậ/gi,"a").replace(/é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ/gi,"e").replace(/i|í|ì|ỉ|ĩ|ị/gi,"i").replace(/ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ/gi,"o").replace(/ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự/gi,"u").replace(/ý|ỳ|ỷ|ỹ|ỵ/gi,"y").replace(/đ/gi,"d").replace(/\s+/g,t).replace(/[^\w\-]+/g,"").replace(/\-\-+/g,t).replace(/^-+/,"").replace(/-+$/,"")}}])}(),r=function(){return l((function e(){a(this,e),this.$body=$("body"),this.RULES_GROUP_TEMPLATE_HTML=$("#rules_group_template").html();var t=this;this.$body.on("submit",".form-update-field-group",(function(){var e=JSON.stringify(t.exportRulesToJson()),a=JSON.stringify(t.exportFieldsToJson());$("#custom_fields_rules").html(e).val(e),$("#custom_fields").html(a).val(a)})),$("#custom_fields_rules").length>0&&(this.handleRules(),this.handleFieldGroups())}),[{key:"handleRules",value:function(){var e=this,t=$.parseJSON($("#custom_fields_rules").val()),a=$(e.RULES_GROUP_TEMPLATE_HTML),n=$("#rules_line_group_template").html(),l=$(".line-group-container");e.$body.on("click",".location-add-rule",(function(e){e.preventDefault();var t=$(e.currentTarget),i=a.clone();if(t.hasClass("location-add-rule-and"))t.closest(".line-group").append(i);else{var o=$(n);o.append(i),l.append(o)}i.find(".rule-a").trigger("change")})),e.$body.on("change",".rule-a",(function(e){e.preventDefault();var t=$(e.currentTarget),a=t.closest(".rule-line");a.find(".rules-b-group select").addClass("hidden"),a.find('.rules-b-group select[data-rel="'+t.val()+'"]').removeClass("hidden")})),e.$body.on("click",".remove-rule-line",(function(e){e.preventDefault();var t=$(e.currentTarget),a=t.closest(".rule-line"),n=t.closest(".line-group");n.find(".rule-line").length<2?n.remove():a.remove()})),t.length<1?$(".location-add-rule").trigger("click"):t.forEach((function(e){var t=$(n);e.forEach((function(e){var n=a.clone();n.find(".rule-a").val(e.name),n.find(".rule-type").val(e.type),n.find('.rule-b:not([data-rel="'+e.name+'"])').addClass("hidden"),n.find('.rule-b[data-rel="'+e.name+'"]').val(e.value),t.append(n)})),l.append(t)}))}},{key:"handleFieldGroups",value:function(){var e=this,t=$.parseJSON($("#custom_fields").val()),a=[],n=$("#_new-field-source_template").html(),l=$("#_options-repeater_template").html(),i=$("#_options-defaultvalue_template").html(),r=$("#_options-defaultvaluetextarea_template").html(),u=$("#_options-placeholdertext_template").html(),s=$("#_options-selectchoices_template").html(),d=$("#_options-buttonlabel_template").html(),c=$("#_options-rows_template").html(),p=function(e){var t="";switch(e){case"text":case"email":case"password":case"number":t+=i+u;break;case"image":case"file":t+="";break;case"textarea":t+=r+u+c;break;case"wysiwyg":t+=r;break;case"select":t+=s+i;break;case"checkbox":case"radio":t+=s;break;case"repeater":t+=l+d}return t},f=function(e){return n.replace(/___options___/gi,p(e||"text"))};e.$body.on("click",".show-item-details",(function(e){e.preventDefault();var t=$(e.currentTarget).closest("li");$(e.currentTarget).toggleClass("active"),t.toggleClass("active")})),e.$body.on("click",".btn-close-field",(function(e){e.preventDefault();var t=$(e.currentTarget).closest("li");t.toggleClass("active"),t.find("> .field-column .show-item-details").toggleClass("active")})),e.$body.on("click",".btn-add-field",(function(e){e.preventDefault();var t=$(e.currentTarget);0;var a=t.closest(".add-new-field").find("> .sortable-wrapper"),n=$(f());a.append(n),n.find(".line[data-option=title] input[type=text]").focus(),function(e,t){e.attr("data-position",t||e.index()+1)}(n),n.find(".sortable-wrapper").sortable()})),e.$body.on("change",".change-field-type",(function(e){e.preventDefault();var t=$(e.currentTarget);t.closest(".item-details").find("> .options").html(p(t.val()))})),e.$body.on("change blur",".line[data-option=slug] input[type=text]",(function(e){var t=$(e.currentTarget),a=o.stringToSlug(t.val(),"_");t.closest(".line").closest(".ui-sortable-handle").find("> .field-column .field-slug").text(a),t.val(a)})),e.$body.on("change blur",".line[data-option=type] select",(function(e){var t=$(e.currentTarget),a=o.stringToSlug(t.val(),"_");t.closest(".line").closest(".ui-sortable-handle").find("> .field-column .field-type").text(t.find('option[value="'+a+'"]').text()),t.val(a)})),e.$body.on("change blur",".line[data-option=title] input[type=text]",(function(e){var t=$(e.currentTarget),a=t.closest(".line"),n=a.find("~ .line[data-option=slug] input[type=text]"),l=t.val();a.closest(".ui-sortable-handle").find("> .field-column .field-label").text(l),n.val()||n.val(o.stringToSlug(l,"_")).trigger("change")})),$("#deleted_items").val(""),e.$body.on("click",".btn-remove",(function(e){e.preventDefault();var t=$(e.currentTarget).closest(".ui-sortable-handle"),n=t.parent();a.push(t.data("id")),t.animate({top:-60,left:60,opacity:.3},300,(function(){t.remove(),n.find("> li").each((function(e,t){var a=e+1;$(t).attr("data-position",a)}))})),$("#deleted_items").val(JSON.stringify(a))}));var v=function(e,t){t.sortable(),e.forEach((function(e,a){var n=$(f(e.type||"text"));n.data("id",e.id||0),n.find(".line[data-option=type] select").val(o.arrayGet(e,"type","text")),n.find(".line[data-option=title] input").val(o.arrayGet(e,"title","")),n.find(".line[data-option=slug] input").val(o.arrayGet(e,"slug","")),n.find(".line[data-option=instructions] textarea").val(o.arrayGet(e,"instructions","")),n.find(".line[data-option=defaultvalue] input").val(o.arrayGet(e.options,"defaultValue","")),n.find(".line[data-option=defaultvaluetextarea] textarea").val(o.arrayGet(e.options,"defaultValueTextarea","")),n.find(".line[data-option=placeholdertext] input").val(o.arrayGet(e.options,"placeholderText","")),n.find(".line[data-option=selectchoices] textarea").val(o.arrayGet(e.options,"selectChoices","")),n.find(".line[data-option=buttonlabel] input").val(o.arrayGet(e.options,"buttonLabel","")),n.find(".line[data-option=rows] input").val(o.arrayGet(e.options,"rows","")),n.find(".field-label").html(o.arrayGet(e,"title","Text")),n.find(".field-slug").html(o.arrayGet(e,"slug","text")),n.find(".field-type").html(o.arrayGet(e,"type","text")),n.removeClass("active"),n.attr("data-position",a+1),v(e.items,n.find(".sortable-wrapper")),t.append(n)}))};v(t,$(".sortable-wrapper"))}},{key:"exportRulesToJson",value:function(){var e=[];return $(".custom-fields-rules .line-group-container .line-group").each((function(t,a){var n=$(a),l=[];n.find(".rule-line").each((function(e,t){var a=$(t),n={name:a.find(".rule-a").val(),type:a.find(".rule-type").val(),value:a.find(".rule-b:not(.hidden)").val()};l.push(n)})),l.length>0&&e.push(l)})),e}},{key:"exportFieldsToJson",value:function(){var e=[],t=function(e,a){e.each((function(e,n){var l={},i=$(n);l.id=i.data("id")||0,l.title=i.find("> .item-details > .line[data-option=title] input[type=text]").val()||null,l.slug=i.find("> .item-details > .line[data-option=slug] input[type=text]").val()||null,l.instructions=i.find("> .item-details > .line[data-option=instructions] textarea").val()||null,l.type=i.find("> .item-details > .line[data-option=type] select").val()||null,l.options={defaultValue:i.find("> .item-details > .options > .line[data-option=defaultvalue] input[type=text]").val()||null,defaultValueTextarea:i.find("> .item-details > .options > .line[data-option=defaultvaluetextarea] textarea").val()||null,placeholderText:i.find("> .item-details > .options > .line[data-option=placeholdertext] input[type=text]").val()||null,selectChoices:i.find("> .item-details > .options > .line[data-option=selectchoices] textarea").val()||null,buttonLabel:i.find("> .item-details > .options > .line[data-option=buttonlabel] input[type=text]").val()||null,rows:i.find("> .item-details > .options > .line[data-option=rows] input[type=number]").val()||null},l.items=[],t(i.find("> .item-details > .options > .line[data-option=repeater] > .col-9 > .add-new-field > .sortable-wrapper > .ui-sortable-handle"),l.items),a.push(l)}))};return t($("#custom_field_group_items > .ui-sortable-handle"),e),e}}])}();jQuery(window).on("load",(function(){new r}))})();
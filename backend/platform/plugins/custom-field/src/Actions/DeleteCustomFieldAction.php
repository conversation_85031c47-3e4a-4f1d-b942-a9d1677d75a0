<?php

namespace Bo<PERSON>ble\CustomField\Actions;

use Bo<PERSON>ble\Base\Events\DeletedContentEvent;
use Bo<PERSON>ble\CustomField\Models\FieldGroup;
use Bo<PERSON>ble\CustomField\Repositories\Interfaces\FieldGroupInterface;

class DeleteCustomFieldAction extends AbstractAction
{
    public function __construct(protected FieldGroupInterface $fieldGroupRepository)
    {
    }

    public function run(FieldGroup $fieldGroup): array
    {
        $result = $this->fieldGroupRepository->delete($fieldGroup);

        DeletedContentEvent::dispatch($fieldGroup::class, request(), $fieldGroup);

        if (! $result) {
            return $this->error();
        }

        return $this->success(null, [
            'id' => $fieldGroup->id,
        ]);
    }
}

<?php

namespace Bo<PERSON>ble\CustomField\Listeners;

use Botble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Facades\BaseHelper;
use Botble\CustomField\Facades\CustomField;
use Exception;

class CreatedContentListener
{
    public function handle(CreatedContentEvent $event): void
    {
        try {
            CustomField::saveCustomFields($event->request, $event->data);
        } catch (Exception $exception) {
            BaseHelper::logError($exception);
        }
    }
}

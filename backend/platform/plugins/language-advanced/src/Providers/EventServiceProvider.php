<?php

namespace Bo<PERSON><PERSON>\LanguageAdvanced\Providers;

use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Bo<PERSON>ble\Base\Events\UpdatedContentEvent;
use Bo<PERSON>ble\LanguageAdvanced\Listeners\AddDefaultTranslations;
use <PERSON><PERSON><PERSON>\LanguageAdvanced\Listeners\AddRefLangToAdminBar;
use Bo<PERSON>ble\LanguageAdvanced\Listeners\ClearCacheAfterUpdateData;
use Botble\LanguageAdvanced\Listeners\PriorityLanguageAdvancedPluginListener;
use Botble\LanguageAdvanced\Listeners\UpdatePermalinkSettingsForEachLanguage;
use Botble\PluginManagement\Events\ActivatedPluginEvent;
use Botble\Slug\Events\UpdatedPermalinkSettings;
use Botble\Theme\Events\RenderingAdminBar;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        CreatedContentEvent::class => [
            AddDefaultTranslations::class,
        ],
        UpdatedContentEvent::class => [
            ClearCacheAfterUpdateData::class,
        ],
        ActivatedPluginEvent::class => [
            PriorityLanguageAdvancedPluginListener::class,
        ],
        UpdatedPermalinkSettings::class => [
            UpdatePermalinkSettingsForEachLanguage::class,
        ],
        RenderingAdminBar::class => [
            AddRefLangToAdminBar::class,
        ],
    ];
}

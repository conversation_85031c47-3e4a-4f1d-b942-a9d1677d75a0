<?php

return [
    'create' => 'New member',
    'menu_name' => 'Members',
    'menu_description' => 'View and manage your members',
    'confirmation_subject' => 'Email verification',
    'confirmation_subject_title' => 'Verify your email',
    'not_confirmed' => 'The given email address has not been confirmed. <a href=":resend_link">Resend confirmation link.</a>',
    'confirmation_successful' => 'You successfully confirmed your email address.',
    'confirmation_info' => 'Please confirm your email address.',
    'confirmation_resent' => 'We sent you another confirmation email. You should receive it shortly.',
    'form' => [
        'login_title' => 'Member login form',
        'register_title' => 'Member registration form',
        'reset_password_title' => 'Member reset password form',
        'forgot_password_title' => 'Member forgot password form',
        'email' => 'Email',
        'password' => 'Password',
        'password_confirmation' => 'Password confirmation',
        'change_password' => 'Change password?',
    ],
    'forgot_password' => 'Forgot password',
    'login' => 'Login',
    'settings' => [
        'email' => [
            'title' => 'Member',
            'description' => 'Member email configuration',
        ],
    ],
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'email_placeholder' => 'Ex: <EMAIL>',
    'write_a_post' => 'Write a post',
    'phone' => 'Phone',
    'phone_placeholder' => 'Phone',
    'confirmed_at' => 'Confirmed at',
    'avatar' => 'Avatar',
    'dob' => 'Date of birth',
    'theme_options' => [
        'name' => 'Member',
        'login_background_image' => 'Login background image',
        'register_background_image' => 'Register background image',
    ],
    'dashboard' => 'Dashboard',
    'author' => 'Author',
    'author_helper' => 'The list of authors is from Admin -> Members.',
    'select_author' => 'Select author',
    'email_verified' => 'Email verified?',
    'verify_email' => [
        'confirm_heading' => 'Verify email confirmation',
        'confirm_description' => 'Are you sure you want to verify email this account?',
        'notification' => 'This account is not verified email yet? :approve_link to verify email.',
        'approve_here' => 'click here',
        'confirm_button' => 'Verify',
    ],
];

(()=>{function o(n){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(o){return typeof o}:function(o){return o&&"function"==typeof Symbol&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},o(n)}function n(n,e){for(var t=0;t<e.length;t++){var r=e[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(n,(i=r.key,a=void 0,a=function(n,e){if("object"!==o(n)||null===n)return n;var t=n[Symbol.toPrimitive];if(void 0!==t){var r=t.call(n,e||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(n)}(i,"string"),"symbol"===o(a)?a:String(a)),r)}var i,a}var e=function(){function o(){!function(o,n){if(!(o instanceof n))throw new TypeError("Cannot call a class as a function")}(this,o)}var e,t,r;return e=o,(t=[{key:"init",value:function(){$('input[name="social_login_enable"]').on("change",(function(o){$(o.currentTarget).prop("checked")?$(".wrapper-list-social-login-options").show():$(".wrapper-list-social-login-options").hide()})),$(".enable-social-login-option").on("change",(function(o){var n=$(o.currentTarget);n.prop("checked")?(n.closest(".card-body").find(".enable-social-login-option-wrapper").show(),n.closest(".form-group").removeClass("mb-0")):(n.closest(".card-body").find(".enable-social-login-option-wrapper").hide(),n.closest(".form-group").addClass("mb-0"))}))}}])&&n(e.prototype,t),r&&n(e,r),Object.defineProperty(e,"prototype",{writable:!1}),o}();$((function(){(new e).init()}))})();
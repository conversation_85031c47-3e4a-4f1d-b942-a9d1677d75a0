(()=>{"use strict";var e={2688:(e,t,n)=>{n.d(t,{Z:()=>i});var r=n(1519),a=n.n(r)()((function(e){return e[1]}));a.push([e.id,'.half-circle-spinner,.half-circle-spinner *{box-sizing:border-box}.half-circle-spinner{border-radius:100%;height:60px;position:relative;width:60px}.half-circle-spinner .circle{border:6px solid transparent;border-radius:100%;content:"";height:100%;position:absolute;width:100%}.half-circle-spinner .circle.circle-1{animation:half-circle-spinner-animation 1s infinite;border-top-color:#ff1d5e}.half-circle-spinner .circle.circle-2{animation:half-circle-spinner-animation 1s infinite alternate;border-bottom-color:#ff1d5e}@keyframes half-circle-spinner-animation{0%{transform:rotate(0)}to{transform:rotate(1turn)}}',""]);const i=a},1519:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n=e(t);return t[2]?"@media ".concat(t[2]," {").concat(n,"}"):n})).join("")},t.i=function(e,n,r){"string"==typeof e&&(e=[[null,e,""]]);var a={};if(r)for(var i=0;i<this.length;i++){var o=this[i][0];null!=o&&(a[o]=!0)}for(var c=0;c<e.length;c++){var l=[].concat(e[c]);r&&a[l[0]]||(n&&(l[2]?l[2]="".concat(n," and ").concat(l[2]):l[2]=n),t.push(l))}},t}},3379:(e,t,n)=>{var r,a=function(){return void 0===r&&(r=Boolean(window&&document&&document.all&&!window.atob)),r},i=function(){var e={};return function(t){if(void 0===e[t]){var n=document.querySelector(t);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}e[t]=n}return e[t]}}(),o=[];function c(e){for(var t=-1,n=0;n<o.length;n++)if(o[n].identifier===e){t=n;break}return t}function l(e,t){for(var n={},r=[],a=0;a<e.length;a++){var i=e[a],l=t.base?i[0]+t.base:i[0],s=n[l]||0,d="".concat(l," ").concat(s);n[l]=s+1;var u=c(d),m={css:i[1],media:i[2],sourceMap:i[3]};-1!==u?(o[u].references++,o[u].updater(m)):o.push({identifier:d,updater:v(m,t),references:1}),r.push(d)}return r}function s(e){var t=document.createElement("style"),r=e.attributes||{};if(void 0===r.nonce){var a=n.nc;a&&(r.nonce=a)}if(Object.keys(r).forEach((function(e){t.setAttribute(e,r[e])})),"function"==typeof e.insert)e.insert(t);else{var o=i(e.insert||"head");if(!o)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");o.appendChild(t)}return t}var d,u=(d=[],function(e,t){return d[e]=t,d.filter(Boolean).join("\n")});function m(e,t,n,r){var a=n?"":r.media?"@media ".concat(r.media," {").concat(r.css,"}"):r.css;if(e.styleSheet)e.styleSheet.cssText=u(t,a);else{var i=document.createTextNode(a),o=e.childNodes;o[t]&&e.removeChild(o[t]),o.length?e.insertBefore(i,o[t]):e.appendChild(i)}}function f(e,t,n){var r=n.css,a=n.media,i=n.sourceMap;if(a?e.setAttribute("media",a):e.removeAttribute("media"),i&&"undefined"!=typeof btoa&&(r+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleSheet)e.styleSheet.cssText=r;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(r))}}var p=null,h=0;function v(e,t){var n,r,a;if(t.singleton){var i=h++;n=p||(p=s(t)),r=m.bind(null,n,i,!1),a=m.bind(null,n,i,!0)}else n=s(t),r=f.bind(null,n,t),a=function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(n)};return r(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap)return;r(e=t)}else a()}}e.exports=function(e,t){(t=t||{}).singleton||"boolean"==typeof t.singleton||(t.singleton=a());var n=l(e=e||[],t);return function(e){if(e=e||[],"[object Array]"===Object.prototype.toString.call(e)){for(var r=0;r<n.length;r++){var a=c(n[r]);o[a].references--}for(var i=l(e,t),s=0;s<n.length;s++){var d=c(n[s]);0===o[d].references&&(o[d].updater(),o.splice(d,1))}n=i}}}},3744:(e,t)=>{t.Z=(e,t)=>{const n=e.__vccOpts||e;for(const[e,r]of t)n[e]=r;return n}}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var i=t[r]={id:r,exports:{}};return e[r](i,i.exports,n),i.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nc=void 0,(()=>{const e=Vue;var t={class:"mb-5 d-block d-md-flex"},r=(0,e.createElementVNode)("div",{class:"col-12 col-md-3"},[(0,e.createElementVNode)("h2",null,"License"),(0,e.createElementVNode)("p",{class:"text-muted"},"Setup license code")],-1),a={class:"col-12 col-md-9"},i={class:"card"},o={class:"card-body"},c={key:0,style:{margin:"auto",width:"30px"}},l={key:1},s={class:"bg-white"},d={class:"alert alert-warning"},u={key:0},m={key:1},f={class:"mb-3"},p=(0,e.createElementVNode)("label",{class:"form-label",for:"buyer"},"Your username on Envato",-1),h=["disabled","readonly"],v=(0,e.createElementVNode)("small",{class:"form-hint"},[(0,e.createTextVNode)("If your profile page is "),(0,e.createElementVNode)("a",{href:"https://codecanyon.net/user/john-smith",rel:"nofollow"},"https://codecanyon.net/user/john-smith"),(0,e.createTextVNode)(", then your username on Envato is "),(0,e.createElementVNode)("strong",null,"john-smith"),(0,e.createTextVNode)(".")],-1),b={class:"mb-3"},g=(0,e.createStaticVNode)('<div><div class="float-start"><label class="form-label" for="purchase_code">Purchase code</label></div><div class="float-end text-end"><small><a href="https://help.market.envato.com/hc/en-us/articles/202822600-Where-Is-My-Purchase-Code" target="_blank">What&#39;s this?</a></small></div><div class="clearfix"></div></div>',1),y=["disabled","readonly"],N={class:"mb-3"},E={class:"form-check"},V=["disabled","readonly"],k=(0,e.createElementVNode)("span",{class:"form-check-label"},[(0,e.createTextVNode)(" Confirm that, according to the Envato License Terms, each license entitles one personfor a single project. Creating multiple unregistered installations is a copyright violation. "),(0,e.createElementVNode)("a",{href:"https://codecanyon.net/licenses/standard",target:"_blank",rel:"nofollow"},"More info"),(0,e.createTextVNode)(". ")],-1),x={class:"mb-3 btn-list"},C=["disabled"],L={key:0,class:"spinner-border spinner-border-sm me-2",role:"status"},w=["disabled"],B={key:0,class:"spinner-border spinner-border-sm me-2",role:"status"},S=(0,e.createElementVNode)("hr",null,null,-1),T=(0,e.createElementVNode)("div",{class:"mb-3"},[(0,e.createElementVNode)("p",null,[(0,e.createElementVNode)("small",{class:"text-danger"},"Note: Your site IP will be added to blacklist after 5 failed attempts.")]),(0,e.createElementVNode)("p",null,[(0,e.createElementVNode)("small",null,[(0,e.createTextVNode)("A purchase code (license) is only valid for One Domain. Are you using this theme on a new domain? Purchase a "),(0,e.createElementVNode)("a",{href:"https://codecanyon.net/user/botble/portfolio",target:"_blank",rel:"nofollow"}," new license here "),(0,e.createTextVNode)(" to get a new purchase code. ")])])],-1),_={key:2},A={class:"text-info"},j={key:0},M={class:"mb-3"},U=["disabled"],z={key:0,class:"spinner-border spinner-border-sm me-2",role:"status"};var D=n(3379),O=n.n(D),R=n(2688),P={insert:"head",singleton:!1};O()(R.Z,P);R.Z.locals;const q={components:{HalfCircleSpinner:((e,t)=>{const n=e.__vccOpts||e;for(const[e,r]of t)n[e]=r;return n})({name:"HalfCircleSpinner",props:{animationDuration:{type:Number,default:1e3},size:{type:Number,default:60},color:{type:String,default:"#fff"}},computed:{spinnerStyle(){return{height:`${this.size}px`,width:`${this.size}px`}},circleStyle(){return{borderWidth:this.size/10+"px",animationDuration:`${this.animationDuration}ms`}},circle1Style(){return Object.assign({borderTopColor:this.color},this.circleStyle)},circle2Style(){return Object.assign({borderBottomColor:this.color},this.circleStyle)}}},[["render",function(t,n,r,a,i,o){return(0,e.openBlock)(),(0,e.createElementBlock)("div",{class:"half-circle-spinner",style:(0,e.normalizeStyle)(o.spinnerStyle)},[(0,e.createElementVNode)("div",{class:"circle circle-1",style:(0,e.normalizeStyle)(o.circle1Style)},null,4),(0,e.createElementVNode)("div",{class:"circle circle-2",style:(0,e.normalizeStyle)(o.circle2Style)},null,4)],4)}]])},props:{verifyUrl:{type:String,default:function(){return null},required:!0},activateLicenseUrl:{type:String,default:function(){return null},required:!0},deactivateLicenseUrl:{type:String,default:function(){return null},required:!0},resetLicenseUrl:{type:String,default:function(){return null},required:!0},manageLicense:{type:String,default:function(){return"no"},required:!0}},data:function(){return{isLoading:!0,verified:!1,purchaseCode:null,buyer:null,licenseRulesAgreement:0,activating:!1,deactivating:!1,license:null}},mounted:function(){this.verifyLicense()},methods:{verifyLicense:function(){var e=this;axios.get(this.verifyUrl).then((function(t){t.data.error||(e.verified=!0,e.license=t.data.data),e.isLoading=!1})).catch((function(t){400===t.response.status&&Botble.showError(t.response.data.message),e.isLoading=!1}))},activateLicense:function(){var e=this;this.activating=!0,axios.post(this.activateLicenseUrl,{purchase_code:this.purchaseCode,buyer:this.buyer,license_rules_agreement:this.licenseRulesAgreement}).then((function(t){t.data.error?Botble.showError(t.data.message):(e.verified=!0,e.license=t.data.data,Botble.showSuccess(t.data.message)),e.activating=!1})).catch((function(t){Botble.handleError(t.response.data),e.activating=!1}))},deactivateLicense:function(){var e=this;this.deactivating=!0,axios.post(this.deactivateLicenseUrl).then((function(t){t.data.error?Botble.showError(t.data.message):e.verified=!1,e.deactivating=!1})).catch((function(t){Botble.handleError(t.response.data),e.deactivating=!1}))},resetLicense:function(){var e=this;this.deactivating=!0,axios.post(this.resetLicenseUrl,{purchase_code:this.purchaseCode,buyer:this.buyer,license_rules_agreement:this.licenseRulesAgreement}).then((function(t){if(t.data.error)return Botble.showError(t.data.message),e.deactivating=!1,!1;e.verified=!1,e.deactivating=!1,Botble.showSuccess(t.data.message)})).catch((function(t){Botble.handleError(t.response.data),e.deactivating=!1}))}}};const I=(0,n(3744).Z)(q,[["render",function(n,D,O,R,P,q){var I=(0,e.resolveComponent)("half-circle-spinner");return(0,e.openBlock)(),(0,e.createElementBlock)("div",t,[r,(0,e.createElementVNode)("div",a,[(0,e.createElementVNode)("div",i,[(0,e.createElementVNode)("div",o,[P.isLoading?((0,e.openBlock)(),(0,e.createElementBlock)("div",c,[(0,e.createVNode)(I,{"animation-duration":1e3,size:15,color:"#808080"})])):(0,e.createCommentVNode)("",!0),P.isLoading||P.verified?(0,e.createCommentVNode)("",!0):((0,e.openBlock)(),(0,e.createElementBlock)("div",l,[(0,e.createElementVNode)("div",s,[(0,e.createElementVNode)("div",d,["yes"===O.manageLicense?((0,e.openBlock)(),(0,e.createElementBlock)("p",u," Your license is invalid. Please activate your license! ")):(0,e.createCommentVNode)("",!0),"no"===O.manageLicense?((0,e.openBlock)(),(0,e.createElementBlock)("p",m," You doesn't have permission to activate the license! ")):(0,e.createCommentVNode)("",!0)])]),(0,e.createElementVNode)("div",f,[p,(0,e.withDirectives)((0,e.createElementVNode)("input",{type:"text",class:"form-control","onUpdate:modelValue":D[0]||(D[0]=function(e){return P.buyer=e}),id:"buyer",placeholder:"Your Envato's username",disabled:"no"===O.manageLicense,readonly:"no"===O.manageLicense},null,8,h),[[e.vModelText,P.buyer]]),v]),(0,e.createElementVNode)("div",b,[g,(0,e.withDirectives)((0,e.createElementVNode)("input",{type:"text",class:"form-control","onUpdate:modelValue":D[1]||(D[1]=function(e){return P.purchaseCode=e}),id:"purchase_code",disabled:"no"===O.manageLicense,readonly:"no"===O.manageLicense,placeholder:"Ex: 10101010-10aa-0101-a1b1010a01b10"},null,8,y),[[e.vModelText,P.purchaseCode]])]),(0,e.createElementVNode)("div",N,[(0,e.createElementVNode)("label",E,[(0,e.withDirectives)((0,e.createElementVNode)("input",{type:"checkbox",name:"license_rules_agreement",value:"1",class:"form-check-input","onUpdate:modelValue":D[2]||(D[2]=function(e){return P.licenseRulesAgreement=e}),disabled:"no"===O.manageLicense,readonly:"no"===O.manageLicense},null,8,V),[[e.vModelCheckbox,P.licenseRulesAgreement]]),k])]),(0,e.createElementVNode)("div",x,[(0,e.createElementVNode)("button",{class:(0,e.normalizeClass)([{"btn btn-info":!0,disabled:P.activating},"btn btn-info"]),type:"button",disabled:"no"===O.manageLicense,onClick:D[3]||(D[3]=function(e){return q.activateLicense()})},[P.activating?((0,e.openBlock)(),(0,e.createElementBlock)("span",L)):(0,e.createCommentVNode)("",!0),(0,e.createTextVNode)(" Activate license ")],10,C),(0,e.createElementVNode)("button",{class:(0,e.normalizeClass)({"btn btn-warning":!0,disabled:P.deactivating}),type:"button",disabled:"no"===O.manageLicense,onClick:D[4]||(D[4]=function(e){return q.resetLicense()})},[P.deactivating?((0,e.openBlock)(),(0,e.createElementBlock)("span",B)):(0,e.createCommentVNode)("",!0),(0,e.createTextVNode)(" Reset license on this domain ")],10,w)]),S,T])),!P.isLoading&&P.verified?((0,e.openBlock)(),(0,e.createElementBlock)("div",_,[(0,e.createElementVNode)("p",A,[P.license.licensed_to?((0,e.openBlock)(),(0,e.createElementBlock)("span",j,"Licensed to "+(0,e.toDisplayString)(P.license.licensed_to)+". ",1)):(0,e.createCommentVNode)("",!0),(0,e.createTextVNode)("Activated since "+(0,e.toDisplayString)(P.license.activated_at)+". ",1)]),(0,e.createElementVNode)("div",M,[(0,e.createElementVNode)("button",{class:(0,e.normalizeClass)({"btn btn-warning":!0,disabled:P.deactivating}),type:"button",onClick:D[5]||(D[5]=function(e){return q.deactivateLicense()}),disabled:"no"===O.manageLicense},[P.deactivating?((0,e.openBlock)(),(0,e.createElementBlock)("span",z)):(0,e.createCommentVNode)("",!0),(0,e.createTextVNode)(" Deactivate license ")],10,U)])])):(0,e.createCommentVNode)("",!0)])])])])}]]);"undefined"!=typeof vueApp&&vueApp.booting((function(e){e.component("license-component",I)}))})()})();
<?php

require_once 'vendor/autoload.php';

use Illuminate\Support\Facades\DB;

// Test query to check if post_scores view exists and works
try {
    echo "Testing post_scores view...\n";
    
    // Test basic query
    $result = DB::select("SELECT COUNT(*) as count FROM post_scores LIMIT 1");
    echo "post_scores view exists and has " . $result[0]->count . " records\n";
    
    // Test users table structure
    $users = DB::select("DESCRIBE users");
    echo "\nUsers table structure:\n";
    foreach ($users as $column) {
        echo "- {$column->Field} ({$column->Type})\n";
    }
    
    // Test if we can get author stats
    $authorStats = DB::select("
        SELECT 
            users.id,
            users.first_name,
            users.last_name, 
            users.email,
            COUNT(post_scores.id) as total_posts,
            SUM(post_scores.views) as total_views
        FROM post_scores
        JOIN posts ON post_scores.id = posts.id
        JOIN users ON posts.author_id = users.id
        WHERE posts.author_type = 'Botble\\\\ACL\\\\Models\\\\User'
        GROUP BY users.id, users.first_name, users.last_name, users.email
        LIMIT 5
    ");
    
    echo "\nSample author stats:\n";
    foreach ($authorStats as $stat) {
        $name = trim($stat->first_name . ' ' . $stat->last_name);
        echo "- {$name}: {$stat->total_posts} posts, {$stat->total_views} views\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

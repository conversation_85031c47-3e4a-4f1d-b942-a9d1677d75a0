<?php

// Simple test to check if the widget works
echo "Testing Author Stats Widget...\n";

try {
    // Test database connection
    $pdo = new PDO('mysql:host=localhost;dbname=your_database', 'username', 'password');
    echo "✓ Database connection successful\n";
    
    // Test if users table has the right structure
    $stmt = $pdo->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array('first_name', $columns) && in_array('last_name', $columns)) {
        echo "✓ Users table has first_name and last_name columns\n";
    } else {
        echo "✗ Users table missing first_name or last_name columns\n";
    }
    
    // Test if posts table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'posts'");
    if ($stmt->rowCount() > 0) {
        echo "✓ Posts table exists\n";
    } else {
        echo "✗ Posts table not found\n";
    }
    
    // Test if post_scores view exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'post_scores'");
    if ($stmt->rowCount() > 0) {
        echo "✓ post_scores view/table exists\n";
    } else {
        echo "⚠ post_scores view/table not found - will use fallback\n";
    }
    
    // Test basic query
    $stmt = $pdo->query("
        SELECT COUNT(DISTINCT author_id) as author_count 
        FROM posts 
        WHERE author_type = 'Botble\\\\ACL\\\\Models\\\\User'
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "✓ Found {$result['author_count']} authors with posts\n";
    
} catch (Exception $e) {
    echo "✗ Error: " . $e->getMessage() . "\n";
}

echo "\nWidget should now work with fallback support!\n";

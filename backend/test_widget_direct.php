<?php

// Test widget directly
echo "Testing Author Stats Widget Direct Access...\n";

// Test URL: /admin/widgets/author-stats
$url = 'http://your-domain.com/admin/widgets/author-stats';

echo "Widget URL: $url\n";
echo "Please test this URL directly in browser\n";

// Test database queries directly
try {
    $pdo = new PDO('mysql:host=localhost;dbname=your_db', 'user', 'pass');
    
    // Test total authors
    $stmt = $pdo->query("
        SELECT COUNT(DISTINCT author_id) as count 
        FROM posts 
        WHERE author_type = 'Botble\\\\ACL\\\\Models\\\\User'
    ");
    $totalAuthors = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Total Authors: <AUTHORS>
    
    // Test total views
    $stmt = $pdo->query("
        SELECT SUM(views) as total 
        FROM posts 
        WHERE author_type = 'Botble\\\\ACL\\\\Models\\\\User'
    ");
    $totalViews = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    echo "Total Views: $totalViews\n";
    
    // Test top author
    $stmt = $pdo->query("
        SELECT author_id, SUM(views) as total_views 
        FROM posts 
        WHERE author_type = 'Botble\\\\ACL\\\\Models\\\\User'
        GROUP BY author_id 
        ORDER BY total_views DESC 
        LIMIT 1
    ");
    $topAuthor = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($topAuthor) {
        $stmt = $pdo->prepare("SELECT first_name, last_name, email FROM users WHERE id = ?");
        $stmt->execute([$topAuthor['author_id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        $name = trim($user['first_name'] . ' ' . $user['last_name']);
        if (empty($name)) {
            $name = $user['email'];
        }
        
        echo "Top Author: $name ({$topAuthor['total_views']} views)\n";
    }
    
    echo "\nWidget data looks good!\n";
    
} catch (Exception $e) {
    echo "Database Error: " . $e->getMessage() . "\n";
}

echo "\nIf widget still empty, check:\n";
echo "1. Widget registration in dashboard\n";
echo "2. CSS loading\n";
echo "3. Route permissions\n";
echo "4. View path\n";

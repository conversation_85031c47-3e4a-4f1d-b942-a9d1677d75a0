@startuml

|Sales|
start
: <PERSON><PERSON><PERSON> kiếm khách hàng tiềm năng;
: G<PERSON>i email/gọi điện tư vấn;
: Hẹn gặp khách hàng;

if (Khách hàng đồng ý gặp?) then (Yes)
  : Gặp trực tiếp hoặc online meeting;
  : <PERSON>hu thập thông tin nhu cầu;
else (<PERSON><PERSON> chối)
  : <PERSON><PERSON> nhận từ chối vào CRM;
  stop
endif

: Phân tích nhu cầu khách hàng;
: Soạn Proposal + Bảng giá;
: Gửi Proposal cho khách hàng;

|Khách_hàng|
: <PERSON><PERSON><PERSON> Proposal;
if (Khách hàng phản hồi?) then (Yes)
  : <PERSON><PERSON><PERSON> hồi, yêu cầu đàm phán;
else (Không phản hồi)
  : <PERSON><PERSON><PERSON> dấu khách hàng không quan tâm;
  stop
endif

|Sales|
: Nhận phản hồi từ khách hàng;
if (<PERSON><PERSON><PERSON> cầu điều chỉnh Proposal?) then (Yes)
  : <PERSON><PERSON> thập feedback chi tiết;
  : <PERSON><PERSON> tích lại nhu cầu;
  : Soạn Proposal và Bảng giá mới;
  : Gửi Proposal mới;
  |Khách_hàng|
  : Đọc Proposal mới;
  if (Khách hàng đồng ý?) then (Yes)
    : Xác nhận đồng ý;
  else (Từ chối tiếp)
    : Ghi nhận thất bại deal;
    stop
  endif
else (Đồng ý ngay)
  : Tiến hành soạn hợp đồng;
endif

|Sales|
: Trình hợp đồng cho phê duyệt nội bộ;

|BOD|
: Xem xét hợp đồng;
if (Hợp đồng được duyệt?) then (Yes)
  : Phê duyệt ký kết;
else (Không duyệt)
  : Ghi nhận lý do chỉnh sửa;
  |Sales|
  : Sửa hợp đồng theo yêu cầu;
  : Trình lại hợp đồng cho BOD;
  -> BOD : Xem xét hợp đồng lại;
endif

|Sales|
: Ký hợp đồng với khách hàng;
: Bàn giao hợp đồng cho vận hành;
: Theo dõi thực hiện hợp đồng;
: Chăm sóc khách hàng định kỳ;

stop
@enduml

'use client';

import { Popover, Transition } from '@/app/headlessui';
import { Fragment, useEffect } from 'react';
import Avatar from '@/components/Avatar/Avatar';
import SwitchDarkMode2 from '@/components/SwitchDarkMode/SwitchDarkMode2';
import { LinkRoute } from '@/components/LinkRoute';
import { clientClearCookie } from '@/lib/clientCookie';
import { useUserServer } from '@/hooks/useUser';
import { IMG_PLACEHOLDER } from '@/contains/contants';
import { forceReload } from '@/utils/forceReload';
import { authApi } from '@/apis/authApi';
import Loading from '@/components/Button/Loading';
import { toast } from 'react-toastify';

export default function AvatarDropdown() {
  const { data: user, isLoading } = useUserServer();

  const handleClearCookie = () => {
    clientClearCookie().finally(() => {
      forceReload();
    });
  };

  const handleLogout = async () => {
    await authApi.post({
      endpoint: 'logout'
    }).then(() => {
      handleClearCookie();
    });
  };

  useEffect(() => {
    if (user?.error) {
      toast.error('Tài khoản của bạn đã bị khóa vui lòng liên hệ quản trị viên để được hỗ trợ.');
      handleClearCookie();
    }
  }, []);

  return (
    <div className="AvatarDropdown ">
      <Popover className="relative">
        {({ open, close }) => (
          <>
            <Popover.Button
              className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-800 focus:outline-none flex items-center justify-center`}
            >
              <svg
                className=" w-6 h-6"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M20.5899 22C20.5899 18.13 16.7399 15 11.9999 15C7.25991 15 3.40991 18.13 3.40991 22"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </Popover.Button>
            <Transition
              as={Fragment}
              enter="transition ease-out duration-200"
              enterFrom="opacity-0 translate-y-1"
              enterTo="opacity-100 translate-y-0"
              leave="transition ease-in duration-150"
              leaveFrom="opacity-100 translate-y-0"
              leaveTo="opacity-0 translate-y-1"
            >
              <Popover.Panel className="absolute z-10 w-screen max-w-[260px] px-4 mt-3.5 -end-2 sm:end-0 sm:px-0">
                <div className="overflow-hidden rounded-3xl shadow-lg ring-1 ring-black ring-opacity-5">
                  {isLoading ? <div className="flex justify-center items-center py-7 px-6"><Loading /></div> :
                    <div className="relative grid grid-cols-1 gap-6 bg-white dark:bg-neutral-800 py-7 px-6">

                      {!!user?.data && <>
                        <div className="flex items-center">
                          <Avatar imgUrl={user?.data?.avatar ?? IMG_PLACEHOLDER} sizeClass="w-12 h-12" />

                          <div className="flex-grow ms-3">
                            <h4 className="font-semibold">{user?.data?.name}</h4>
                          </div>
                        </div>
                        <div className="w-full border-b border-neutral-200 dark:border-neutral-700" />
                        <LinkRoute
                          href={'/dashboard'}
                          className="flex items-center p-2 -m-3 transition duration-150 ease-in-out rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50"
                          onClick={() => close()}
                        >
                          <div className="flex items-center justify-center flex-shrink-0 text-neutral-500 dark:text-neutral-300">
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M12.1601 10.87C12.0601 10.86 11.9401 10.86 11.8301 10.87C9.45006 10.79 7.56006 8.84 7.56006 6.44C7.56006 3.99 9.54006 2 12.0001 2C14.4501 2 16.4401 3.99 16.4401 6.44C16.4301 8.84 14.5401 10.79 12.1601 10.87Z"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M7.15997 14.56C4.73997 16.18 4.73997 18.82 7.15997 20.43C9.90997 22.27 14.42 22.27 17.17 20.43C19.59 18.81 19.59 16.17 17.17 14.56C14.43 12.73 9.91997 12.73 7.15997 14.56Z"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                          <div className="ms-4">
                            <p className="text-sm font-medium ">{'Tài khoản của tôi'}</p>
                          </div>
                        </LinkRoute>
                        <div className="w-full border-b border-neutral-200 dark:border-neutral-700" />
                      </>}


                      {/* ------------------ 1 --------------------- */}


                      {/* ------------------ 2 --------------------- */}
                      {/*<Link*/}
                      {/*  href={"/dashboard/posts" as Route}*/}
                      {/*  className="flex items-center p-2 -m-3 transition duration-150 ease-in-out rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50"*/}
                      {/*  onClick={() => close()}*/}
                      {/*>*/}
                      {/*  <div className="flex items-center justify-center flex-shrink-0 text-neutral-500 dark:text-neutral-300">*/}
                      {/*    <svg*/}
                      {/*      width="24"*/}
                      {/*      height="24"*/}
                      {/*      viewBox="0 0 24 24"*/}
                      {/*      fill="none"*/}
                      {/*    >*/}
                      {/*      <path*/}
                      {/*        d="M8 12.2H15"*/}
                      {/*        stroke="currentColor"*/}
                      {/*        strokeWidth="1.5"*/}
                      {/*        strokeMiterlimit="10"*/}
                      {/*        strokeLinecap="round"*/}
                      {/*        strokeLinejoin="round"*/}
                      {/*      />*/}
                      {/*      <path*/}
                      {/*        d="M8 16.2H12.38"*/}
                      {/*        stroke="currentColor"*/}
                      {/*        strokeWidth="1.5"*/}
                      {/*        strokeMiterlimit="10"*/}
                      {/*        strokeLinecap="round"*/}
                      {/*        strokeLinejoin="round"*/}
                      {/*      />*/}
                      {/*      <path*/}
                      {/*        d="M10 6H14C16 6 16 5 16 4C16 2 15 2 14 2H10C9 2 8 2 8 4C8 6 9 6 10 6Z"*/}
                      {/*        stroke="currentColor"*/}
                      {/*        strokeWidth="1.5"*/}
                      {/*        strokeMiterlimit="10"*/}
                      {/*        strokeLinecap="round"*/}
                      {/*        strokeLinejoin="round"*/}
                      {/*      />*/}
                      {/*      <path*/}
                      {/*        d="M16 4.02002C19.33 4.20002 21 5.43002 21 10V16C21 20 20 22 15 22H9C4 22 3 20 3 16V10C3 5.44002 4.67 4.20002 8 4.02002"*/}
                      {/*        stroke="currentColor"*/}
                      {/*        strokeWidth="1.5"*/}
                      {/*        strokeMiterlimit="10"*/}
                      {/*        strokeLinecap="round"*/}
                      {/*        strokeLinejoin="round"*/}
                      {/*      />*/}
                      {/*    </svg>*/}
                      {/*  </div>*/}
                      {/*  <div className="ms-4">*/}
                      {/*    <p className="text-sm font-medium ">{"My Posts"}</p>*/}
                      {/*  </div>*/}
                      {/*</LinkRoute>*/}

                      {/* ------------------ 2 --------------------- */}
                      {/*<Link*/}
                      {/*  href={"/author/demo-slug"}*/}
                      {/*  className="flex items-center p-2 -m-3 transition duration-150 ease-in-out rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50"*/}
                      {/*  onClick={() => close()}*/}
                      {/*>*/}
                      {/*  <div className="flex items-center justify-center flex-shrink-0 text-neutral-500 dark:text-neutral-300">*/}
                      {/*    <svg*/}
                      {/*      width="24"*/}
                      {/*      height="24"*/}
                      {/*      viewBox="0 0 24 24"*/}
                      {/*      fill="none"*/}
                      {/*    >*/}
                      {/*      <path*/}
                      {/*        d="M12.62 20.81C12.28 20.93 11.72 20.93 11.38 20.81C8.48 19.82 2 15.69 2 8.68998C2 5.59998 4.49 3.09998 7.56 3.09998C9.38 3.09998 10.99 3.97998 12 5.33998C13.01 3.97998 14.63 3.09998 16.44 3.09998C19.51 3.09998 22 5.59998 22 8.68998C22 15.69 15.52 19.82 12.62 20.81Z"*/}
                      {/*        stroke="currentColor"*/}
                      {/*        strokeWidth="1.5"*/}
                      {/*        strokeLinecap="round"*/}
                      {/*        strokeLinejoin="round"*/}
                      {/*      />*/}
                      {/*    </svg>*/}
                      {/*  </div>*/}
                      {/*  <div className="ms-4">*/}
                      {/*    <p className="text-sm font-medium ">{"Wishlist"}</p>*/}
                      {/*  </div>*/}
                      {/*</LinkRoute>*/}


                      {/* ------------------ 2 --------------------- */}
                      <div className="flex items-center justify-between p-2 -m-3 transition duration-150 ease-in-out rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50">
                        <div className="flex items-center">
                          <div className="flex items-center justify-center flex-shrink-0 text-neutral-500 dark:text-neutral-300">
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M12.0001 7.88989L10.9301 9.74989C10.6901 10.1599 10.8901 10.4999 11.3601 10.4999H12.6301C13.1101 10.4999 13.3001 10.8399 13.0601 11.2499L12.0001 13.1099"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M8.30011 18.0399V16.8799C6.00011 15.4899 4.11011 12.7799 4.11011 9.89993C4.11011 4.94993 8.66011 1.06993 13.8001 2.18993C16.0601 2.68993 18.0401 4.18993 19.0701 6.25993C21.1601 10.4599 18.9601 14.9199 15.7301 16.8699V18.0299C15.7301 18.3199 15.8401 18.9899 14.7701 18.9899H9.26011C8.16011 18.9999 8.30011 18.5699 8.30011 18.0399Z"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M8.5 22C10.79 21.35 13.21 21.35 15.5 22"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                          <div className="ms-4">
                            <p className="text-sm font-medium ">{'Giao diện tối'}</p>
                          </div>
                        </div>
                        <SwitchDarkMode2 />
                      </div>

                      {/* ------------------ 2 --------------------- */}
                      {/*<Link*/}
                      {/*  href={"/"}*/}
                      {/*  className="flex items-center p-2 -m-3 transition duration-150 ease-in-out rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50"*/}
                      {/*  onClick={() => close()}*/}
                      {/*>*/}
                      {/*  <div className="flex items-center justify-center flex-shrink-0 text-neutral-500 dark:text-neutral-300">*/}
                      {/*    <svg*/}
                      {/*      width="24"*/}
                      {/*      height="24"*/}
                      {/*      viewBox="0 0 24 24"*/}
                      {/*      fill="none"*/}
                      {/*      xmlns="http://www.w3.org/2000/svg"*/}
                      {/*    >*/}
                      {/*      <path*/}
                      {/*        d="M11.97 22C17.4928 22 21.97 17.5228 21.97 12C21.97 6.47715 17.4928 2 11.97 2C6.44715 2 1.97 6.47715 1.97 12C1.97 17.5228 6.44715 22 11.97 22Z"*/}
                      {/*        stroke="currentColor"*/}
                      {/*        strokeWidth="1.5"*/}
                      {/*        strokeLinecap="round"*/}
                      {/*        strokeLinejoin="round"*/}
                      {/*      />*/}
                      {/*      <path*/}
                      {/*        d="M12 16.5C14.4853 16.5 16.5 14.4853 16.5 12C16.5 9.51472 14.4853 7.5 12 7.5C9.51472 7.5 7.5 9.51472 7.5 12C7.5 14.4853 9.51472 16.5 12 16.5Z"*/}
                      {/*        stroke="currentColor"*/}
                      {/*        strokeWidth="1.5"*/}
                      {/*        strokeLinecap="round"*/}
                      {/*        strokeLinejoin="round"*/}
                      {/*      />*/}
                      {/*      <path*/}
                      {/*        d="M4.89999 4.92993L8.43999 8.45993"*/}
                      {/*        stroke="currentColor"*/}
                      {/*        strokeWidth="1.5"*/}
                      {/*        strokeLinecap="round"*/}
                      {/*        strokeLinejoin="round"*/}
                      {/*      />*/}
                      {/*      <path*/}
                      {/*        d="M4.89999 19.07L8.43999 15.54"*/}
                      {/*        stroke="currentColor"*/}
                      {/*        strokeWidth="1.5"*/}
                      {/*        strokeLinecap="round"*/}
                      {/*        strokeLinejoin="round"*/}
                      {/*      />*/}
                      {/*      <path*/}
                      {/*        d="M19.05 19.07L15.51 15.54"*/}
                      {/*        stroke="currentColor"*/}
                      {/*        strokeWidth="1.5"*/}
                      {/*        strokeLinecap="round"*/}
                      {/*        strokeLinejoin="round"*/}
                      {/*      />*/}
                      {/*      <path*/}
                      {/*        d="M19.05 4.92993L15.51 8.45993"*/}
                      {/*        stroke="currentColor"*/}
                      {/*        strokeWidth="1.5"*/}
                      {/*        strokeLinecap="round"*/}
                      {/*        strokeLinejoin="round"*/}
                      {/*      />*/}
                      {/*    </svg>*/}
                      {/*  </div>*/}
                      {/*  <div className="ms-4">*/}
                      {/*    <p className="text-sm font-medium ">{"Help"}</p>*/}
                      {/*  </div>*/}
                      {/*</LinkRoute>*/}

                      {/* ------------------ 2 --------------------- */}
                      {!!user?.data ? <LinkRoute
                        href={'/#'}
                        className="flex items-center p-2 -m-3 transition duration-150 ease-in-out rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50"
                        onClick={handleLogout}
                      >
                        <div className="flex items-center justify-center flex-shrink-0 text-neutral-500 dark:text-neutral-300">
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M8.90002 7.55999C9.21002 3.95999 11.06 2.48999 15.11 2.48999H15.24C19.71 2.48999 21.5 4.27999 21.5 8.74999V15.27C21.5 19.74 19.71 21.53 15.24 21.53H15.11C11.09 21.53 9.24002 20.08 8.91002 16.54"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M15 12H3.62"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                            <path
                              d="M5.85 8.6499L2.5 11.9999L5.85 15.3499"
                              stroke="currentColor"
                              strokeWidth="1.5"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </div>
                        <div className="ms-4">
                          <p className="text-sm font-medium ">{'Đăng xuất'}</p>
                        </div>
                      </LinkRoute> : <>
                        <LinkRoute
                          href={'/login'}
                          className="flex items-center p-2 -m-3 transition duration-150 ease-in-out rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50"
                        >
                          <div className="flex items-center justify-center flex-shrink-0 text-neutral-500 dark:text-neutral-300 rotate-180">
                            <svg
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M8.90002 7.55999C9.21002 3.95999 11.06 2.48999 15.11 2.48999H15.24C19.71 2.48999 21.5 4.27999 21.5 8.74999V15.27C21.5 19.74 19.71 21.53 15.24 21.53H15.11C11.09 21.53 9.24002 20.08 8.91002 16.54"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M15 12H3.62"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                              <path
                                d="M5.85 8.6499L2.5 11.9999L5.85 15.3499"
                                stroke="currentColor"
                                strokeWidth="1.5"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              />
                            </svg>
                          </div>
                          <div className="ms-4">
                            <p className="text-sm font-medium ">{'Đăng nhập'}</p>
                          </div>
                        </LinkRoute>
                        <LinkRoute
                          href={'/signup'}
                          className="flex items-center p-2 -m-3 transition duration-150 ease-in-out rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-700 focus:outline-none focus-visible:ring focus-visible:ring-orange-500 focus-visible:ring-opacity-50"
                        >
                          <div className="flex items-center justify-center flex-shrink-0 text-neutral-500 dark:text-neutral-300">
                            <svg
                              version="1.1"
                              width="24"
                              height="24"
                              viewBox="0 0 40 40"
                              xmlns="http://www.w3.org/2000/svg"
                              xmlnsXlink="http://www.w3.org/1999/xlink"
                              fill="currentColor"
                            >
                              <path
                                d="M37.1,5.22h-2v-2c0-0.5-0.4-0.9-0.9-0.9c-0.5,0-0.9,0.4-0.9,0.9v2h-2c-0.5,0-0.9,0.4-0.9,0.9c0,0.5,0.4,0.9,0.9,0.9h2v2  c0,0.5,0.4,0.9,0.9,0.9c0.5,0,0.9-0.4,0.9-0.9V7h2C37.6,7,38,6.6,38,6.1s-0.4-0.9-0.9-0.9l0,0V5.22z"
                              />
                              <path
                                d="M28.76,20c2.92-2.28,3.44-6.5,1.16-9.42c-1.24-1.6-3.14-2.54-5.16-2.58c-1.64,0-3.22,0.62-4.42,1.74C18.22,5.8,13.32,4.33,9.38,6.45S3.97,13.47,6.09,17.4c0.65,1.2,1.59,2.23,2.74,2.97C4.87,21.53,2,24.85,2,28.77v8  c0,0.5,0.4,0.9,0.9,0.9c0,0,0,0,0,0h20.88c0.5,0,0.9-0.4,0.9-0.9l0,0v-1.62h8.54c0.5,0,0.9-0.4,0.9-0.9v0V27.3  C34.08,23.97,31.92,21.04,28.76,20z M24.76,9.75c2.71-0.05,4.94,2.09,5,4.8c0.05,2.71-2.09,4.94-4.8,5  c-1.79,0.04-3.46-0.91-4.35-2.46l0.05-0.14c0.08-0.17,0.15-0.34,0.22-0.51s0.13-0.4,0.19-0.6s0.05-0.16,0.07-0.25  c0.17-0.64,0.26-1.29,0.26-1.95c0-0.43,0-0.84-0.06-1.26c-0.04-0.26-0.09-0.52-0.16-0.77c0,0,0-0.08,0-0.11  c0.89-1.1,2.22-1.74,3.63-1.75H24.76z M19.41,21c-0.52-0.24-1.05-0.44-1.6-0.59c0.68-0.45,1.29-1,1.8-1.64  c0.39,0.51,0.86,0.95,1.39,1.32c-0.54,0.24-1.04,0.56-1.49,0.93L19.41,21z M7,13.64c-0.01-3.47,2.81-6.29,6.28-6.3  c2.69,0,5.09,1.7,5.96,4.25c0.24,0.66,0.36,1.35,0.36,2.05c-0.01,0.68-0.12,1.36-0.34,2c-0.79,2.34-3.06,4.28-5.62,4.28  c0,0-0.74,0-0.74,0C9.59,19.71,7,16.96,7,13.64z M22.88,35.91H3.8c0,0,0-7.14,0-7.14c0-1.46,0.54-2.88,1.46-4.01  c0.97-1.19,2.31-2.05,3.77-2.53c0.89-0.29,1.83-0.41,2.76-0.45c0.96-0.04,1.95-0.1,2.91-0.02c0.5,0.04,1,0.14,1.49,0.24  c0.52,0.11,1.03,0.24,1.53,0.39c0.25,0.07,0.49,0.16,0.74,0.24c0.72,0.25,1.36,0.62,1.97,1.08c0.38,0.29,0.66,0.63,0.99,0.96  c0.27,0.27,0.48,0.67,0.64,1.01c0.26,0.53,0.38,1.11,0.51,1.68c0.22,0.99,0.32,1.99,0.32,3.01c0,0.68,0,1.36,0,2.04  c0,1.15,0,2.29,0,3.44c0,0.03,0,0.07,0,0.1L22.88,35.91z M32.32,33.35h-7.64v-4.58c0,0,0-0.06,0-0.09s0-0.18,0-0.28  c-0.1-1.57-0.61-3.09-1.47-4.4c-0.05-0.07-0.11-0.14-0.16-0.22c-0.18-0.24-0.37-0.48-0.57-0.7l-0.05-0.06  c-0.35-0.38-0.75-0.73-1.18-1.02c0.79-0.47,1.69-0.7,2.61-0.65h2.22c3.36-0.07,6.15,2.59,6.24,5.95V33.35z"
                              />
                            </svg>
                          </div>
                          <div className="ms-4">
                            <p className="text-sm font-medium ">{'Đăng ký'}</p>
                          </div>
                        </LinkRoute>
                      </>}
                    </div>}
                </div>
              </Popover.Panel>
            </Transition>
          </>
        )}
      </Popover>
    </div>
  );
}

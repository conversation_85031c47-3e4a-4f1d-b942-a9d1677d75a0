import React, { FC } from 'react';
import NavigationItem from './NavigationItem';
import { useThemeOptions } from '@/hooks/useTheme';
import { INavItemType } from '@/contains/types';

interface Props {
  className?: string;
  menu: INavItemType[];
}

const NavigationAdmin: FC<Props> = ({ className = 'flex', menu }) => {
  const { data: theme } = useThemeOptions();

  return (
    <ul
      className={`nc-Navigation-admin items-center justify-center h-12 bg- ${className}`} style={{
      backgroundColor: theme?.general.primary_color
    }}
    >
      {menu.map((item) => (
        <NavigationItem key={item.id} menuItem={item} />
      ))}
    </ul>
  );
};

export default NavigationAdmin;

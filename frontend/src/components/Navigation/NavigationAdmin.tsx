'use client';

import React, { FC } from 'react';
import NavigationItem from './NavigationItem';
import { useThemeOptions } from '@/hooks/useTheme';
import { INavItemType } from '@/contains/types';
import { LinkRoute } from '@/components/LinkRoute';

interface Props {
  className?: string;
  menu: INavItemType[];
  isAdmin?: boolean;
  postId?: number;
}

const NavigationAdmin: FC<Props> = ({
  className = 'flex',
  menu,
  postId
}) => {
  const { data: theme } = useThemeOptions();
  return (
    <ul
      className={`nc-Navigation-admin items-center justify-center h-12 bg- ${className}`} style={{
      backgroundColor: theme?.general.primary_color
    }}
    >
      {menu.map((item) => (
        <NavigationItem key={item.id} menuItem={item} />
      ))}

      {/* Add edit buttons for post or category when IDs are available */}
      {postId ? (
        <li className={`menu-item flex-shrink-0 menu-megamenu menu-megamenu--large`}>
          <LinkRoute
            href={postId ? `/admin/blog/posts/edit/${postId}` : '/admin/blog/posts'}
            className="inline-flex items-center text-sm font-medium py-2.5 px-4 rounded-full hover:bg-opacity-90"
          >
            <p className="font-medium text-slate-900 dark:text-neutral-200">
              Chỉnh sửa bài viết
            </p>
          </LinkRoute>
        </li>
      ) : <></>}
    </ul>
  );
};

export default NavigationAdmin;

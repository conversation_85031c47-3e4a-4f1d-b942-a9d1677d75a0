'use client';

import { create } from 'zustand';

interface AdminNavigationStore {
  postId: number;
  userId: number;

  setActiveId: (type: 'post' | 'user', id: number) => void;
  reset: () => void;
}

export const useAdminNavigationStore = create<AdminNavigationStore>((set, get) => ({
  postId: 0,
  userId: 0,

  setActiveId: (type, id) => {
    const key = `${type}Id` as 'postId' | 'userId';
    const currentValue = get()[key];

    if (currentValue !== id) {
      set({ [key]: id });
    }
  },

  reset: () => set({
    postId: 0,
    userId: 0,
  })
}));

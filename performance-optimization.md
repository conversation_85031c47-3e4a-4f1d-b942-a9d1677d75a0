# Tối ưu hóa hiệu suất

## Database Queries
- Sử dụng eager loading để tránh N+1 query problem
  ```php
  // Thay vì
  $posts = Post::all();
  foreach ($posts as $post) {
      $post->author->name; // Gây ra N+1 query
  }
  
  // Nên sử dụng
  $posts = Post::with('author')->get();
  ```

- Sử dụng index cho các cột thường xuyên được tìm kiếm
- Tối ưu hóa các query phức tạp bằng cách sử dụng query builder

## Caching
- Áp dụng caching cho các dữ liệu ít thay đổi
  ```php
  // Ví dụ với cache
  $posts = Cache::remember('homepage.posts', 3600, function () {
      return Post::with('author')->latest()->take(10)->get();
  });
  ```

- Sử dụng cache tags để quản lý cache hiệu quả
- Xóa cache khi dữ liệu thay đổi

## API Optimization
- Sử dụng pagination cho các endpoint trả về nhiều dữ liệu
- Áp dụng rate limiting để tránh lạm dụng API
- Sử dụng conditional requests với ETag hoặc Last-Modified